<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签即画廊 - Mizzy Star</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/tag-gallery.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-left">
                <button class="btn-back" onclick="window.history.back()">
                    <i class="fas fa-arrow-left"></i> 返回
                </button>
                <h1 class="page-title">
                    <i class="fas fa-images"></i> 标签即画廊
                </h1>
            </div>
            <div class="header-right">
                <button class="btn-export" id="exportBtn">
                    <i class="fas fa-download"></i> 导出
                </button>
                <button class="btn-settings" id="settingsBtn">
                    <i class="fas fa-cog"></i> 设置
                </button>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧搜索面板 -->
            <aside class="search-panel">
                <div class="search-section">
                    <h3><i class="fas fa-search"></i> 智能搜索</h3>
                    
                    <!-- 快速搜索 -->
                    <div class="quick-search">
                        <input type="text" id="quickSearch" placeholder="输入关键词快速搜索..." class="search-input">
                        <button class="search-btn" id="quickSearchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>

                    <!-- 高级搜索 -->
                    <div class="advanced-search">
                        <h4>高级搜索</h4>
                        
                        <!-- 用户标签 -->
                        <div class="filter-group">
                            <label>用户标签</label>
                            <div class="tag-input-container">
                                <input type="text" id="userTagsInput" placeholder="输入用户标签..." class="tag-input">
                                <div class="selected-tags" id="selectedUserTags"></div>
                            </div>
                        </div>

                        <!-- 自定义标签 -->
                        <div class="filter-group">
                            <label>自定义标签</label>
                            <div class="custom-tags-container" id="customTagsContainer">
                                <!-- 动态生成自定义标签输入 -->
                            </div>
                            <button class="btn-add-custom-tag" id="addCustomTagBtn">
                                <i class="fas fa-plus"></i> 添加自定义标签
                            </button>
                        </div>

                        <!-- 质量过滤 -->
                        <div class="filter-group">
                            <label>质量分数</label>
                            <div class="quality-range">
                                <input type="range" id="qualityMin" min="0" max="100" value="0" class="range-input">
                                <input type="range" id="qualityMax" min="0" max="100" value="100" class="range-input">
                                <div class="range-labels">
                                    <span id="qualityMinLabel">0</span>
                                    <span id="qualityMaxLabel">100</span>
                                </div>
                            </div>
                        </div>

                        <!-- 日期范围 -->
                        <div class="filter-group">
                            <label>日期范围</label>
                            <div class="date-range">
                                <input type="date" id="dateFrom" class="date-input">
                                <span>至</span>
                                <input type="date" id="dateTo" class="date-input">
                            </div>
                        </div>

                        <!-- 搜索操作 -->
                        <div class="search-actions">
                            <button class="btn-search" id="advancedSearchBtn">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <button class="btn-clear" id="clearFiltersBtn">
                                <i class="fas fa-times"></i> 清除
                            </button>
                        </div>
                    </div>

                    <!-- 搜索建议 -->
                    <div class="search-suggestions" id="searchSuggestions" style="display: none;">
                        <h4>搜索建议</h4>
                        <div class="suggestions-list" id="suggestionsList">
                            <!-- 动态生成建议 -->
                        </div>
                    </div>
                </div>

                <!-- 处理工具 -->
                <div class="processing-section">
                    <h3><i class="fas fa-magic"></i> 图像处理</h3>
                    
                    <div class="processor-list">
                        <button class="processor-btn" data-processor="image_inversion">
                            <i class="fas fa-adjust"></i> 图像反相
                        </button>
                        <button class="processor-btn" data-processor="color_correction">
                            <i class="fas fa-palette"></i> 颜色校正
                        </button>
                        <button class="processor-btn" data-processor="auto_crop">
                            <i class="fas fa-crop"></i> 自动裁剪
                        </button>
                    </div>

                    <div class="batch-actions">
                        <button class="btn-batch-process" id="batchProcessBtn" disabled>
                            <i class="fas fa-layer-group"></i> 批量处理
                        </button>
                    </div>
                </div>
            </aside>

            <!-- 右侧画廊区域 -->
            <section class="gallery-area">
                <!-- 搜索结果信息 -->
                <div class="search-info">
                    <div class="result-count">
                        <span id="resultCount">0</span> 个文件
                    </div>
                    <div class="search-time">
                        搜索耗时: <span id="searchTime">0ms</span>
                    </div>
                    <div class="view-controls">
                        <button class="view-btn active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- 当前筛选条件 -->
                <div class="active-filters" id="activeFilters" style="display: none;">
                    <h4>当前筛选条件:</h4>
                    <div class="filter-tags" id="filterTags">
                        <!-- 动态生成筛选标签 -->
                    </div>
                </div>

                <!-- 文件画廊 -->
                <div class="file-gallery" id="fileGallery">
                    <!-- 加载状态 -->
                    <div class="loading-state" id="loadingState">
                        <div class="loading-spinner"></div>
                        <p>正在搜索文件...</p>
                    </div>

                    <!-- 空状态 -->
                    <div class="empty-state" id="emptyState" style="display: none;">
                        <div class="empty-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>未找到匹配的文件</h3>
                        <p>尝试调整搜索条件或清除筛选器</p>
                    </div>

                    <!-- 文件网格 -->
                    <div class="file-grid" id="fileGrid">
                        <!-- 动态生成文件卡片 -->
                    </div>

                    <!-- 分页控制 -->
                    <div class="pagination" id="pagination" style="display: none;">
                        <button class="page-btn" id="prevPageBtn" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <div class="page-info">
                            第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页
                        </div>
                        <button class="page-btn" id="nextPageBtn" disabled>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </section>
        </main>

        <!-- 导出对话框 -->
        <div class="modal" id="exportModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-download"></i> 导出文件</h3>
                    <button class="modal-close" id="closeExportModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="export-options">
                        <div class="option-group">
                            <label>导出预设</label>
                            <select id="exportPreset" class="select-input">
                                <option value="high_quality_jpeg">高质量JPEG</option>
                                <option value="web_optimized">Web优化</option>
                                <option value="thumbnail_collection">缩略图集合</option>
                                <option value="original_archive">原始存档</option>
                            </select>
                        </div>
                        <div class="option-group">
                            <label>输出路径</label>
                            <input type="text" id="exportPath" placeholder="选择导出路径..." class="text-input">
                            <button class="btn-browse" id="browseExportPath">
                                <i class="fas fa-folder-open"></i> 浏览
                            </button>
                        </div>
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="exportMetadata" checked>
                                导出元数据文件
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-cancel" id="cancelExport">取消</button>
                    <button class="btn-primary" id="startExport">开始导出</button>
                </div>
            </div>
        </div>

        <!-- 处理进度对话框 -->
        <div class="modal" id="progressModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-cog fa-spin"></i> 处理中...</h3>
                </div>
                <div class="modal-body">
                    <div class="progress-info">
                        <div class="progress-text" id="progressText">正在处理文件...</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                        </div>
                        <div class="progress-stats">
                            <span id="progressStats">0 / 0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="js/api.js"></script>
    <script src="js/tag-gallery.js"></script>
</body>
</html>
