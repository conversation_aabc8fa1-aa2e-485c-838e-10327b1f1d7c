#!/bin/bash

# Mizzy Star PostgreSQL停止脚本
# 用于安全停止PostgreSQL环境

set -e

echo "🛑 Mizzy Star PostgreSQL环境停止脚本"
echo "=================================="

# 检查是否有运行的容器
if ! docker-compose ps | grep -q "Up"; then
    echo "ℹ️  没有运行中的容器"
    exit 0
fi

# 显示当前运行的服务
echo "📋 当前运行的服务:"
docker-compose ps

echo ""

# 询问是否保留数据
if [ "$1" != "--force" ] && [ "$1" != "--remove-data" ]; then
    echo "🤔 选择停止方式:"
    echo "   1) 停止服务但保留数据 (推荐)"
    echo "   2) 停止服务并删除所有数据"
    echo "   3) 取消操作"
    echo ""
    read -p "请选择 (1-3): " choice
    
    case $choice in
        1)
            echo "🔄 停止服务，保留数据..."
            docker-compose down
            ;;
        2)
            echo "⚠️  警告: 这将删除所有数据库数据!"
            read -p "确认删除所有数据? (yes/no): " confirm
            if [ "$confirm" = "yes" ]; then
                echo "🗑️  停止服务并删除数据..."
                docker-compose down -v
                echo "✅ 所有数据已删除"
            else
                echo "❌ 操作已取消"
                exit 0
            fi
            ;;
        3)
            echo "❌ 操作已取消"
            exit 0
            ;;
        *)
            echo "❌ 无效选择"
            exit 1
            ;;
    esac
elif [ "$1" = "--remove-data" ]; then
    echo "🗑️  强制停止服务并删除数据..."
    docker-compose down -v
    echo "✅ 所有数据已删除"
else
    echo "🔄 停止服务，保留数据..."
    docker-compose down
fi

echo ""
echo "✅ PostgreSQL环境已停止"

# 显示清理信息
if [ "$1" = "--remove-data" ] || [ "$choice" = "2" ]; then
    echo ""
    echo "🧹 数据清理完成:"
    echo "   - 所有容器已停止并删除"
    echo "   - 所有数据卷已删除"
    echo "   - 网络已删除"
    echo ""
    echo "💡 下次启动将创建全新的数据库环境"
else
    echo ""
    echo "💾 数据保留:"
    echo "   - 数据库数据已保留"
    echo "   - 下次启动将恢复之前的状态"
fi

echo ""
echo "📚 更多信息请查看 README.md"
