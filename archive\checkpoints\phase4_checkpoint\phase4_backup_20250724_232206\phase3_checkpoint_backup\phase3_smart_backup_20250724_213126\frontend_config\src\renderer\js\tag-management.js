// 标签管理页面主要逻辑
// 🎯 配置驱动的标签类别定义
const TAG_CATEGORIES = {
    'custom': {
        displayName: '自定义标签',
        icon: '🏷️',
        handler: 'handleCustomTagClick',
        finder: 'findCustomTag',
        expander: 'expandTagCategory',
        selector: '.custom-tag-item',
        fileFilter: 'showFilesWithCustomTag',
        titleTemplate: '自定义标签: {name}',
        priority: 1
    },
    'metadata': {
        displayName: '元数据标签',
        icon: '📋',
        handler: 'handleMetadataTagClick',
        finder: 'findMetadataTag',
        expander: 'expandTagCategory',
        selector: '.tag-item',
        fileFilter: 'showFilesWithMetadataTag',
        titleTemplate: '元数据标签: {key}={value}',
        priority: 2
    },
    'properties': {
        displayName: '属性标签',
        icon: '⚙️',
        handler: 'handlePropertiesTagClick',
        finder: 'findPropertiesTag',
        expander: 'expandTagCategory',
        selector: '.tag-item',
        fileFilter: 'showFilesWithPropertiesTag',
        titleTemplate: '属性标签: {key}={value}',
        priority: 3
    },
    'quality': {
        displayName: '质量标签',
        icon: '⭐',
        handler: 'handleQualityTagClick',
        finder: 'findQualityTag',
        expander: 'expandTagCategory',
        selector: '.tag-item',
        fileFilter: 'showFilesWithQualityTag',
        titleTemplate: '质量标签: {key}={value}',
        priority: 4
    },
    'cv': {
        displayName: 'CV标签',
        icon: '🤖',
        handler: 'handleCvTagClick',
        finder: 'findCvTag',
        expander: 'expandTagCategory',
        selector: '.tag-item',
        fileFilter: 'showFilesWithCvTag',
        titleTemplate: 'CV标签: {key}={value}',
        priority: 5
    },
    'ai': {
        displayName: 'AI标签',
        icon: '🧠',
        handler: 'handleAiTagClick',
        finder: 'findAiTag',
        expander: 'expandTagCategory',
        selector: '.tag-item',
        fileFilter: 'showFilesWithAiTag',
        titleTemplate: 'AI标签: {key}={value}',
        priority: 6
    }
};

class TagManagementApp {
    constructor() {
        console.log('🚀 TagManagementApp 初始化 - 版本: 2025-07-23-v4 (配置驱动)');
        this.currentCaseId = null;
        this.currentCase = null;
        this.tagTree = null;
        this.selectedTags = new Set();
        this.selectedFiles = new Set();
        this.currentFiles = [];
        this.isResizing = false;
        this.isSelectionMode = false; // 选择模式状态

        // 🎯 配置驱动：初始化标签类别配置
        this.tagCategories = TAG_CATEGORIES;

        this.init();
    }

    // 🎯 配置驱动：获取标签类别配置
    getTagCategoryConfig(categoryType) {
        return this.tagCategories[categoryType] || null;
    }

    // 🎯 配置驱动：获取所有标签类别
    getAllTagCategories() {
        return Object.keys(this.tagCategories).sort((a, b) => {
            return this.tagCategories[a].priority - this.tagCategories[b].priority;
        });
    }

    // 🎯 配置驱动：添加新的标签类别
    addTagCategory(categoryType, config) {
        console.log(`🎯 添加新标签类别: ${categoryType}`, config);
        this.tagCategories[categoryType] = {
            displayName: config.displayName || categoryType,
            icon: config.icon || '🏷️',
            handler: config.handler || 'handleGenericTagClick',
            finder: config.finder || 'findGenericTag',
            expander: config.expander || 'expandTagCategory',
            selector: config.selector || '.tag-item',
            fileFilter: config.fileFilter || 'showFilesWithGenericTag',
            titleTemplate: config.titleTemplate || `${config.displayName || categoryType}: {key}={value}`,
            priority: config.priority || 999,
            ...config
        };
    }

    // 🎯 配置驱动：移除标签类别
    removeTagCategory(categoryType) {
        console.log(`🎯 移除标签类别: ${categoryType}`);
        delete this.tagCategories[categoryType];
    }

    // 🎯 配置驱动：检查标签类别是否存在
    hasTagCategory(categoryType) {
        return categoryType in this.tagCategories;
    }
    
    async init() {
        // 从URL参数获取案例ID和源文件ID
        const urlParams = new URLSearchParams(window.location.search);
        this.currentCaseId = parseInt(urlParams.get('caseId'));
        this.sourceFileId = parseInt(urlParams.get('sourceFileId'));

        // 获取标签跳转参数
        this.targetTagType = urlParams.get('tagType');
        this.targetTagKey = urlParams.get('tagKey');
        this.targetTagText = urlParams.get('tagText');
        this.targetTagId = urlParams.get('tagId'); // 用于自定义标签

        if (!this.currentCaseId) {
            showNotification('缺少案例ID参数', 'error');
            this.goBack();
            return;
        }

        // 如果有源文件ID，记录下来用于高亮
        if (this.sourceFileId) {
            console.log('🎯 检测到源文件ID:', this.sourceFileId);
        }

        // 如果有标签跳转参数，记录下来用于自动跳转
        if (this.targetTagType && this.targetTagKey) {
            console.log('🏷️ 检测到标签跳转参数:', {
                type: this.targetTagType,
                key: this.targetTagKey,
                text: this.targetTagText
            });
        }
        
        // 初始化事件监听器
        this.initEventListeners();
        
        // 初始化分栏调整
        this.initPanelResize();
        
        // 加载数据
        await this.loadData();
    }
    
    initEventListeners() {
        // 返回按钮
        document.getElementById('back-btn').addEventListener('click', () => {
            this.goBack();
        });
        
        // 刷新按钮
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadData();
        });
        
        // 搜索框
        const searchInput = document.getElementById('tag-search');
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.searchTags(e.target.value);
            }, 300);
        });
        
        // 快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                searchInput.focus();
            } else if (e.key === 'Escape') {
                searchInput.value = '';
                this.clearSearch();
            }
        });

        // 添加自定义标签按钮
        document.getElementById('add-custom-tag-btn').addEventListener('click', () => {
            this.showAddCustomTagModal();
        });
    }

    initPanelResize() {
        const divider = document.getElementById('panel-divider');
        const tagPanel = document.getElementById('tag-panel');
        
        divider.addEventListener('mousedown', (e) => {
            this.isResizing = true;
            document.addEventListener('mousemove', this.handleResize.bind(this));
            document.addEventListener('mouseup', this.stopResize.bind(this));
            e.preventDefault();
        });
    }
    
    handleResize(e) {
        if (!this.isResizing) return;
        
        const tagPanel = document.getElementById('tag-panel');
        const newWidth = e.clientX;
        
        if (newWidth >= 300 && newWidth <= 600) {
            tagPanel.style.width = newWidth + 'px';
        }
    }
    
    stopResize() {
        this.isResizing = false;
        document.removeEventListener('mousemove', this.handleResize);
        document.removeEventListener('mouseup', this.stopResize);
    }
    
    async loadData() {
        try {
            // 显示加载状态
            this.showLoading();

            console.log('🔄 开始统一数据加载流程...');

            // 确保数据库已迁移
            await this.ensureDatabaseMigration();

            // 🚀 统一数据加载：使用Promise.all协调所有API请求
            console.log('📥 并行加载所有数据源...');
            
            // 并行执行所有API请求
            const [caseBasicInfo, tagTree, customTags] = await Promise.all([
                // 只获取案例基本信息
                api.getCaseBasicInfo(this.currentCaseId),
                // 获取优化的标签树
                api.getTagTree(this.currentCaseId, true),
                // 加载自定义标签数据
                api.getCustomTags(this.currentCaseId).catch(error => {
                    console.error('❌ 加载自定义标签失败:', error);
                    return []; // 确保自定义标签失败不会阻止其他数据加载
                })
            ]);

            console.log('🔄 聚合所有数据...');
            
            // 原子状态更新 - 一次性更新所有状态
            this.currentCase = caseBasicInfo;
            this.tagTree = tagTree;
            
            // 将自定义标签添加到标签树中
            this.tagTree.custom = customTags || [];
            console.log('✅ 数据聚合完成:', {
                caseInfo: !!this.currentCase,
                tagTree: !!this.tagTree,
                customTags: this.tagTree.custom.length
            });

            // 更新UI
            this.updateCaseInfo();
            this.updateTagStats({});
            this.renderTagTree();
            
            // 🔍 检查文件数据是否需要加载
            if (!this.currentCase.files || this.currentCase.files.length === 0) {
                console.log('📥 按需加载文件数据...');
                try {
                    const caseData = await api.getCase(this.currentCaseId);
                    this.currentCase.files = caseData.files || [];
                    console.log('✅ 文件数据加载完成:', this.currentCase.files.length);
                } catch (fileError) {
                    console.error('❌ 加载文件数据失败:', fileError);
                    this.currentCase.files = [];
                }
            }
            
            // 设置当前文件列表
            this.currentFiles = this.currentCase.files || [];
            
            // 渲染画廊和更新计数
            this.renderGallery();
            this.updateFileCount();

            // 如果有源文件ID，在画廊渲染完成后自动高亮源图片
            if (this.sourceFileId) {
                setTimeout(() => {
                    this.highlightSourceImage();
                }, 100);
            }

            // 如果有标签跳转参数，自动跳转到目标标签
            if (this.targetTagType && this.targetTagKey) {
                setTimeout(() => {
                    autoJumpToTargetTag();
                }, 200);
            }

            console.log('✅ 统一数据加载流程完成');

        } catch (error) {
            console.error('❌ 数据加载失败:', error);
            showNotification('加载数据失败: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    async ensureDatabaseMigration() {
        try {
            const schema = await api.checkTagDatabaseSchema(this.currentCaseId);
            if (schema.migration_needed) {
                showNotification('正在初始化标签数据库...', 'info');
                await api.migrateTagDatabase(this.currentCaseId);
                showNotification('标签数据库初始化完成', 'success');
            }
        } catch (error) {
            console.warn('数据库迁移检查失败:', error);
        }
    }    
 
   updateCaseInfo() {
        if (!this.currentCase) return;
        
        document.getElementById('case-name').textContent = this.currentCase.case_name;
        document.getElementById('case-id').textContent = `ID: ${this.currentCase.id}`;
    }
    
    updateTagStats(stats) {
        // 计算当前显示的标签总数
        const currentDisplayedTags = this.calculateCurrentDisplayedTags();
        document.getElementById('total-tags').textContent = currentDisplayedTags;

        // 自定义标签数量
        const customTagsCount = this.tagTree?.custom?.length || 0;
        document.getElementById('custom-tags').textContent = customTagsCount;

        // 计算活跃文件数
        const activeFiles = this.currentCase?.files?.length || 0;
        document.getElementById('active-files').textContent = activeFiles;
    }

    calculateCurrentDisplayedTags() {
        if (!this.tagTree) return 0;

        let totalTags = 0;

        // 计算自定义标签数量
        totalTags += this.tagTree.custom?.length || 0;

        // 计算系统标签数量
        const systemCategories = ['properties', 'metadata', 'cv', 'user', 'ai'];
        systemCategories.forEach(category => {
            const categoryData = this.tagTree[category === 'properties' ? 'properties' : 'tags']?.[category] || {};
            totalTags += Object.keys(categoryData).length;
        });

        return totalTags;
    }
    
    renderTagTree() {
        if (!this.tagTree) return;

        // 渲染自定义标签
        this.renderCustomTags();

        // 渲染系统标签
        this.renderSystemTags();

        // 初始化标签类别展开/折叠
        this.initTagCategoryToggle();

        // 更新标签统计（使用当前显示的标签数量）
        this.updateTagStats({});

        // 更新批量操作的标签选择器
        this.updateBatchTagSelect();
    }
    
    renderCustomTags() {
        const container = document.getElementById('custom-tags-list');
        const customTags = this.tagTree.custom || [];
        
        if (customTags.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4 text-gray-500 text-sm">
                    <p>暂无自定义标签</p>
                    <p class="mt-1">点击上方 + 按钮添加</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = customTags.map(tag => `
            <div class="custom-tag-item" data-tag-id="${tag.id}" data-tag-type="custom">
                <div class="custom-tag-color" style="background-color: ${tag.color}"></div>
                <span class="custom-tag-name">${tag.name}</span>
                <span class="custom-tag-count">${tag.count}</span>
                <div class="custom-tag-actions">
                    <button class="edit-custom-tag-btn text-blue-600 hover:text-blue-800" data-tag-id="${tag.id}" title="编辑">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                    </button>
                    <button class="delete-custom-tag-btn text-red-600 hover:text-red-800" data-tag-id="${tag.id}" title="删除">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `).join('');
        
        // 添加事件监听器
        this.attachCustomTagEvents(container);
    }    

    renderSystemTags() {
        const categories = ['properties', 'metadata', 'cv', 'user', 'ai'];
        
        categories.forEach(category => {
            const container = document.getElementById(`${category}-tags`);

            // 根据新的后端数据结构获取数据
            let categoryData = {};
            if (category === 'properties') {
                categoryData = this.tagTree.properties || {};
            } else if (category === 'metadata') {
                categoryData = this.tagTree.tags?.metadata || {};
            } else if (category === 'cv') {
                categoryData = this.tagTree.tags?.cv || {};
            } else if (category === 'user') {
                categoryData = this.tagTree.tags?.user || {};
            } else if (category === 'ai') {
                categoryData = this.tagTree.tags?.ai || {};
            } else {
                categoryData = {};
            }

            // 更新类别标签数量
            const header = document.querySelector(`[data-category="${category}"]`);
            const countElement = header?.querySelector('.tag-count');
            if (countElement) {
                const count = Object.keys(categoryData).length;
                countElement.textContent = count;
            }
            
            if (Object.keys(categoryData).length === 0) {
                container.innerHTML = `
                    <div class="text-center py-2 text-gray-500 text-sm">
                        暂无${this.getCategoryDisplayName(category)}
                    </div>
                `;
                return;
            }
            
            // 渲染标签项
            const tagItems = [];

            Object.entries(categoryData).forEach(([tagName, tagValues]) => {
                if (Array.isArray(tagValues)) {
                    // 后端返回的是数组格式（新格式）
                    tagValues.forEach(tagValue => {
                        tagItems.push({
                            name: `${tagName}: ${tagValue}`, // 显示完整的标签名
                            value: tagValue,
                            count: 'N/A', // 数组格式暂时没有计数信息
                            fileIds: [],
                            parentName: tagName,
                            fullName: `${tagName}: ${tagValue}`
                        });
                    });
                } else if (typeof tagValues === 'number') {
                    // 简单的键值对格式（如：metadata标签）
                    tagItems.push({
                        name: tagName, // tagName已经包含完整信息，如"camera: SONY ILCE-7RM4"
                        value: tagName,
                        count: tagValues,
                        fileIds: [],
                        fullName: tagName
                    });
                } else if (typeof tagValues === 'object' && tagValues.count !== undefined) {
                    // 直接的标签值（如user、ai标签）
                    tagItems.push({
                        name: tagName,
                        value: tagName,
                        count: tagValues.count,
                        fileIds: tagValues.file_ids
                    });
                } else if (typeof tagValues === 'object') {
                    // 嵌套的标签值（旧格式，带计数）
                    Object.entries(tagValues).forEach(([tagValue, valueData]) => {
                        tagItems.push({
                            name: `${tagName}: ${tagValue}`,
                            value: tagValue,
                            count: valueData.count || 'N/A',
                            fileIds: valueData.file_ids || [],
                            parentName: tagName,
                            fullName: `${tagName}: ${tagValue}`
                        });
                    });
                }
            });
            
            container.innerHTML = tagItems.map(tag => `
                <div class="tag-item" data-category="${category}" data-tag-name="${tag.parentName || tag.name}" data-tag-value="${tag.value}">
                    <span class="tag-name">${tag.name}</span>
                    <span class="tag-count">${this.calculateTagFileCount(tag.parentName || tag.name, tag.value)}</span>
                </div>
            `).join('');
            
            // 添加点击事件
            container.querySelectorAll('.tag-item').forEach(item => {
                item.addEventListener('click', () => {
                    this.selectTag(item);
                });
            });
        });
    }
    
    getCategoryDisplayName(category) {
        const names = {
            'properties': '属性标签',
            'metadata': '元数据标签',
            'cv': '计算机视觉标签',
            'user': '用户标签',
            'ai': 'AI标签'
        };
        return names[category] || category;
    }

    // 计算标签对应的文件数量
    calculateTagFileCount(tagName, tagValue) {
        if (!this.currentCase || !this.currentCase.files) {
            return 0;
        }

        let count = 0;
        this.currentCase.files.forEach(file => {
            if (this.fileHasTag(file, tagName, tagValue)) {
                count++;
            }
        });

        return count;
    }

    // 检查文件是否包含指定标签
    fileHasTag(file, tagName, tagValue) {
        if (!file.tags || !file.tags.tags) {
            return false;
        }

        const fileTags = file.tags.tags;

        // 检查metadata标签
        if (fileTags.metadata && fileTags.metadata[tagName]) {
            return String(fileTags.metadata[tagName]).toLowerCase() === String(tagValue).toLowerCase();
        }

        // 检查properties标签
        if (file.tags.properties && file.tags.properties[tagName]) {
            return String(file.tags.properties[tagName]).toLowerCase() === String(tagValue).toLowerCase();
        }

        // 检查其他标签类型
        if (fileTags[tagName]) {
            if (Array.isArray(fileTags[tagName])) {
                return fileTags[tagName].some(v => String(v).toLowerCase() === String(tagValue).toLowerCase());
            } else {
                return String(fileTags[tagName]).toLowerCase() === String(tagValue).toLowerCase();
            }
        }

        return false;
    }
    
    initTagCategoryToggle() {
        document.querySelectorAll('.tag-category-header').forEach(header => {
            header.addEventListener('click', () => {
                const category = header.dataset.category;
                const content = document.getElementById(`${category}-tags`);
                const isExpanded = !content.classList.contains('hidden');
                
                if (isExpanded) {
                    content.classList.add('hidden');
                    header.classList.remove('expanded');
                } else {
                    content.classList.remove('hidden');
                    header.classList.add('expanded');
                }
            });
        });
    }    

    attachCustomTagEvents(container) {
        // 自定义标签点击选择
        container.querySelectorAll('.custom-tag-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.closest('button')) {
                    this.selectCustomTag(item);
                }
            });
        });
        
        // 编辑按钮
        container.querySelectorAll('.edit-custom-tag-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const tagId = parseInt(btn.dataset.tagId);
                this.showEditCustomTagModal(tagId);
            });
        });
        
        // 删除按钮
        container.querySelectorAll('.delete-custom-tag-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const tagId = parseInt(btn.dataset.tagId);
                this.deleteCustomTag(tagId);
            });
        });
    }
    
    selectTag(tagElement) {
        // 清除其他选择
        document.querySelectorAll('.tag-item.selected, .custom-tag-item.selected').forEach(item => {
            item.classList.remove('selected');
        });
        
        // 选择当前标签
        tagElement.classList.add('selected');
        
        // 获取标签信息
        const category = tagElement.dataset.category;
        const tagName = tagElement.dataset.tagName;
        const tagValue = tagElement.dataset.tagValue;
        
        // 更新画廊
        this.filterFilesByTag(category, tagName, tagValue);
        
        // 更新画廊标题
        this.updateGalleryTitle(`${this.getCategoryDisplayName(category)}: ${tagName}${tagValue !== tagName ? ` = ${tagValue}` : ''}`);
    }
    
    selectCustomTag(tagElement) {
        // 清除其他选择
        document.querySelectorAll('.tag-item.selected, .custom-tag-item.selected').forEach(item => {
            item.classList.remove('selected');
        });
        
        // 选择当前标签
        tagElement.classList.add('selected');
        
        // 获取标签信息
        const tagId = parseInt(tagElement.dataset.tagId);
        const customTag = this.tagTree.custom.find(tag => tag.id === tagId);
        
        if (customTag) {
            // 显示使用该自定义标签的文件
            this.showFilesWithCustomTag(customTag);
            this.updateGalleryTitle(`自定义标签: ${customTag.name}`);
        }
    }
    
    async filterFilesByTag(category, tagName, tagValue) {
        try {
            console.log('🔍 标签筛选开始:', { category, tagName, tagValue });

            // 🔧 修复方案：优先使用本地筛选，API筛选作为备用

            // 方案1: 本地筛选（基于已加载的文件数据）
            const localResults = this.filterFilesLocally(category, tagName, tagValue);
            console.log('📊 本地筛选结果:', `找到 ${localResults.length} 个文件`);

            if (localResults.length > 0) {
                // 本地筛选成功，使用本地结果
                this.currentFiles = localResults;
                console.log('✅ 使用本地筛选结果');
            } else {
                // 方案2: API筛选（备用方案）
                console.log('⚠️ 本地筛选无结果，尝试API筛选');

                const tagFilters = {};

                // 根据标签类型构造正确的筛选参数
                // 后端期望的格式是直接的字段名，不需要前缀
                switch (category) {
                    case 'metadata':
                        // metadata标签直接使用字段名
                        tagFilters[tagName] = tagValue;
                        break;
                    case 'cv':
                        // cv标签直接使用字段名
                        tagFilters[tagName] = tagValue;
                        break;
                    case 'user':
                        tagFilters['user'] = tagValue;
                        break;
                    case 'ai':
                        tagFilters['ai'] = tagValue;
                        break;
                    case 'properties':
                        tagFilters[tagName] = tagValue;
                        break;
                    default:
                        tagFilters[tagName] = tagValue;
                }

                console.log('🔍 API筛选参数:', tagFilters);

                const response = await api.getFilesWithTags(this.currentCaseId, tagFilters);
                console.log('🔍 API响应数据:', response);
                this.currentFiles = response.files || [];

                console.log('📊 API筛选结果:', `找到 ${this.currentFiles.length} 个文件`);
                console.log('📊 currentFiles示例:', this.currentFiles.slice(0, 2));
            }

            // 更新UI
            this.renderGallery();
            this.updateFileCount();

        } catch (error) {
            console.error('标签筛选失败:', error);
            showNotification('筛选文件失败', 'error');
        }
    }

    /**
     * 本地文件筛选方法
     * @param {string} category 标签分类
     * @param {string} tagName 标签名称
     * @param {string} tagValue 标签值
     * @returns {Array} 匹配的文件列表
     */
    filterFilesLocally(category, tagName, tagValue) {
        console.log('🔍 开始本地筛选:', { category, tagName, tagValue });

        // 获取所有文件数据
        const allFiles = this.currentCase?.files || [];
        console.log('📁 总文件数:', allFiles.length);

        const matchingFiles = allFiles.filter(file => {
            if (!file.tags || !file.tags.tags) {
                return false;
            }

            const fileTags = file.tags.tags;

            switch (category) {
                case 'metadata':
                    return this.checkMetadataTag(fileTags, tagName, tagValue);

                case 'cv':
                    return this.checkCvTag(fileTags, tagName, tagValue);

                case 'user':
                    return this.checkUserTag(fileTags, tagValue);

                case 'ai':
                    return this.checkAiTag(fileTags, tagValue);

                case 'properties':
                    return this.checkPropertiesTag(file.tags, tagName, tagValue);

                default:
                    console.warn('未知的标签分类:', category);
                    return false;
            }
        });

        console.log('🎯 本地筛选匹配文件:', matchingFiles.length);
        return matchingFiles;
    }

    /**
     * 检查元数据标签
     */
    checkMetadataTag(fileTags, tagName, tagValue) {
        if (!fileTags.metadata || typeof fileTags.metadata !== 'object') {
            return false;
        }

        const metadataValue = fileTags.metadata[tagName];
        if (metadataValue === undefined || metadataValue === null) {
            return false;
        }

        return String(metadataValue).toLowerCase() === String(tagValue).toLowerCase();
    }

    /**
     * 检查CV标签
     */
    checkCvTag(fileTags, tagName, tagValue) {
        if (!fileTags.cv || typeof fileTags.cv !== 'object') {
            return false;
        }

        const cvValue = fileTags.cv[tagName];
        if (cvValue === undefined || cvValue === null) {
            return false;
        }

        if (Array.isArray(cvValue)) {
            return cvValue.some(v => String(v).toLowerCase() === String(tagValue).toLowerCase());
        } else {
            return String(cvValue).toLowerCase() === String(tagValue).toLowerCase();
        }
    }

    /**
     * 检查用户标签
     */
    checkUserTag(fileTags, tagValue) {
        if (!fileTags.user || !Array.isArray(fileTags.user)) {
            return false;
        }

        return fileTags.user.some(tag => String(tag).toLowerCase() === String(tagValue).toLowerCase());
    }

    /**
     * 检查AI标签
     */
    checkAiTag(fileTags, tagValue) {
        if (!fileTags.ai || !Array.isArray(fileTags.ai)) {
            return false;
        }

        return fileTags.ai.some(tag => String(tag).toLowerCase() === String(tagValue).toLowerCase());
    }

    /**
     * 检查属性标签
     */
    checkPropertiesTag(tags, tagName, tagValue) {
        if (!tags.properties || typeof tags.properties !== 'object') {
            return false;
        }

        const propertyValue = tags.properties[tagName];
        if (propertyValue === undefined || propertyValue === null) {
            return false;
        }

        return String(propertyValue).toLowerCase() === String(tagValue).toLowerCase();
    }

    showFilesWithCustomTag(customTag) {
        // 从所有文件中筛选出包含该自定义标签的文件
        this.currentFiles = this.currentCase.files.filter(file =>
            customTag.file_ids.includes(file.id)
        );

        this.renderGallery();
        this.updateFileCount();
    }
    
    async loadAllFiles() {
        try {
            console.log('📂 按需加载所有文件...');

            // 🚀 性能优化：按需加载文件数据
            if (!this.currentCase.files) {
                console.log('📥 首次加载文件数据...');
                const caseData = await api.getCase(this.currentCaseId);
                this.currentCase.files = caseData.files || [];
                console.log('✅ 文件数据加载完成:', this.currentCase.files.length);
            }

            this.currentFiles = this.currentCase.files || [];

            console.log('📊 显示文件数量:', this.currentFiles.length);

            this.renderGallery();
            this.updateFileCount();
            this.updateGalleryTitle('所有文件');

            // 如果有源文件ID，在画廊渲染完成后自动高亮源图片
            if (this.sourceFileId) {
                setTimeout(() => {
                    this.highlightSourceImage();
                }, 100);
            }

            // 如果有标签跳转参数，自动跳转到目标标签
            if (this.targetTagType && this.targetTagKey) {
                setTimeout(() => {
                    autoJumpToTargetTag();
                }, 200);
            }
        } catch (error) {
            console.error('加载文件失败:', error);
            showNotification('加载文件失败', 'error');
        }
    }
    
    renderGallery() {
        console.log('🖼️ renderGallery 被调用，文件数量:', this.currentFiles.length);

        const container = document.getElementById('gallery-grid');
        const emptyState = document.getElementById('gallery-empty');

        if (!container) {
            console.error('❌ gallery-grid 容器不存在');
            return;
        }

        if (!emptyState) {
            console.error('❌ gallery-empty 容器不存在');
            return;
        }

        // 防御性渲染：确保currentFiles是数组
        if (!Array.isArray(this.currentFiles)) {
            console.warn('⚠️ currentFiles不是数组，重置为空数组');
            this.currentFiles = [];
        }

        if (this.currentFiles.length === 0) {
            console.log('📭 没有文件，显示空状态');
            container.classList.add('hidden');
            emptyState.classList.remove('hidden');
            return;
        }
        
        container.classList.remove('hidden');
        emptyState.classList.add('hidden');
        
        console.log('🎨 渲染文件画廊，文件数量:', this.currentFiles.length);
        
        container.innerHTML = this.currentFiles.map(file => `
            <div class="gallery-item" data-file-id="${file.id}">
                <div class="gallery-item-checkbox">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                
                ${file.file_type && file.file_type.startsWith('image/') ? `
                    <img src="${api.getFileThumbnailUrl(this.currentCaseId, file.id, 300)}" 
                         alt="${file.file_name}"
                         loading="lazy"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik02MCA2MEgxNDBWMTQwSDYwVjYwWiIgZmlsbD0iI0U1RTdFQiIgc3Ryb2tlPSIjRDFENURCIiBzdHJva2Utd2lkdGg9IjIiIHJ4PSI0Ii8+CjxjaXJjbGUgY3g9Ijg1IiBjeT0iODUiIHI9IjEwIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik02MCA5MEw4NSA3NUwxMTAgOTBMMTMwIDgwTDE0MCA5MFYxNDBINjBWOTBaIiBmaWxsPSIjRDFENURCIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTcwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiPuaaguaXoOWyuumdojwvdGV4dD4KPC9zdmc+'">
                ` : `
                    <div class="w-full h-full flex items-center justify-center bg-gray-100">
                        <div class="text-center">
                            <div class="text-4xl mb-2">${getFileIcon(file.file_name)}</div>
                            <div class="text-xs text-gray-500">${file.file_type || 'Unknown'}</div>
                        </div>
                    </div>
                `}
                
                <div class="gallery-item-overlay">
                    <div class="gallery-item-title">${file.file_name}</div>
                    <div class="gallery-item-tags">
                        ${this.getFileTagsForDisplay(file).map(tag => `
                            <span class="gallery-item-tag">${tag}</span>
                        `).join('')}
                    </div>
                </div>
            </div>
        `).join('');
        
        // 添加事件监听器
        this.attachGalleryEvents();
        
        console.log('✅ 画廊渲染完成，显示', this.currentFiles.length, '个文件');
    }
    
    getFileTagsForDisplay(file) {
        const tags = [];
        console.log('🏷️ getFileTagsForDisplay 被调用:', file.file_name, file.tags);

        if (file.tags) {
            try {
                const tagsData = typeof file.tags === 'string' ? JSON.parse(file.tags) : file.tags;
                console.log('📊 解析后的标签数据:', tagsData);

                // 按照新的精准格式显示关键标签
                if (tagsData.tags && tagsData.tags.metadata) {
                    const metadata = tagsData.tags.metadata;
                    console.log('🔍 元数据:', metadata);

                    // 1. 文件类型 - 按照新格式显示
                    if (metadata.fileType) {
                        tags.push(metadata.fileType.toUpperCase()); // 直接显示 "JPG"
                        console.log('✅ 添加文件类型标签:', metadata.fileType.toUpperCase());
                    }

                    // 2. 相机型号 - 按照新格式（已合并品牌+型号）
                    if (metadata.camera_model) {
                        tags.push(metadata.camera_model); // 直接显示 "SONY ILCE-7RM4"
                        console.log('✅ 添加相机标签:', metadata.camera_model);
                    }

                    // 3. 分辨率 - 按照新格式显示
                    if (metadata.resolution) {
                        tags.push(metadata.resolution); // 直接显示 "300 DPI"
                        console.log('✅ 添加分辨率标签:', metadata.resolution);
                    }

                    // 4. 添加更多EXIF字段 - 显示多个标签
                    if (metadata.aperture) {
                        tags.push(metadata.aperture); // 显示 "f/11.0"
                        console.log('✅ 添加光圈标签:', metadata.aperture);
                    }

                    if (metadata.shutter_speed) {
                        tags.push(metadata.shutter_speed); // 显示 "1/500 秒"
                        console.log('✅ 添加快门标签:', metadata.shutter_speed);
                    }

                    if (metadata.iso) {
                        tags.push(metadata.iso); // 显示 "ISO-100"
                        console.log('✅ 添加ISO标签:', metadata.iso);
                    }

                    if (metadata.shooting_date) {
                        tags.push(metadata.shooting_date); // 显示 "2023/8/22"
                        console.log('✅ 添加拍摄日期标签:', metadata.shooting_date);
                    }

                    if (metadata.color_standard) {
                        tags.push(metadata.color_standard); // 显示 "sRGB"
                        console.log('✅ 添加色彩标准标签:', metadata.color_standard);
                    }
                }

                // 添加质量分数（如果有的话）
                if (tagsData.properties && tagsData.properties.qualityScore) {
                    tags.push(`质量: ${tagsData.properties.qualityScore}%`);
                }

            } catch (error) {
                console.error('❌ 解析文件标签失败:', error);
            }
        }

        console.log('🎯 最终返回的标签:', tags);
        return tags.slice(0, 3); // 最多显示3个标签
    }

    // 🔧 关键修复：基于文件数据构建标签树，确保与大图查看页面数据一致
    buildTagTreeFromFiles(files) {
        console.log('🏗️ 开始构建标签树，文件数量:', files.length);

        const tagTree = {
            tags: {
                metadata: {},
                cv: {},
                user: {},
                ai: {}
            },
            properties: {},
            custom: []
        };

        // 遍历所有文件，收集标签数据
        files.forEach((file, index) => {
            if (!file.tags || !file.tags.tags) {
                console.log(`⚠️ 文件 ${file.id} 没有标签数据`);
                return;
            }

            const fileTags = file.tags.tags;
            console.log(`📋 处理文件 ${file.id} 的标签:`, fileTags);

            // 处理元数据标签
            if (fileTags.metadata) {
                Object.entries(fileTags.metadata).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        if (!tagTree.tags.metadata[key]) {
                            tagTree.tags.metadata[key] = {};
                        }
                        if (!tagTree.tags.metadata[key][value]) {
                            tagTree.tags.metadata[key][value] = {
                                count: 0,
                                file_ids: []
                            };
                        }
                        tagTree.tags.metadata[key][value].count++;
                        tagTree.tags.metadata[key][value].file_ids.push(file.id);
                    }
                });
            }

            // 处理CV标签
            if (fileTags.cv) {
                Object.entries(fileTags.cv).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        if (!tagTree.tags.cv[key]) {
                            tagTree.tags.cv[key] = {};
                        }
                        const valueStr = Array.isArray(value) ? value.join(', ') : String(value);
                        if (!tagTree.tags.cv[key][valueStr]) {
                            tagTree.tags.cv[key][valueStr] = {
                                count: 0,
                                file_ids: []
                            };
                        }
                        tagTree.tags.cv[key][valueStr].count++;
                        tagTree.tags.cv[key][valueStr].file_ids.push(file.id);
                    }
                });
            }

            // 处理用户标签
            if (fileTags.user && Array.isArray(fileTags.user)) {
                fileTags.user.forEach(tag => {
                    if (tag && tag.trim()) {
                        if (!tagTree.tags.user[tag]) {
                            tagTree.tags.user[tag] = {
                                count: 0,
                                file_ids: []
                            };
                        }
                        tagTree.tags.user[tag].count++;
                        tagTree.tags.user[tag].file_ids.push(file.id);
                    }
                });
            }

            // 处理AI标签
            if (fileTags.ai && Array.isArray(fileTags.ai)) {
                fileTags.ai.forEach(tag => {
                    if (tag && tag.trim()) {
                        if (!tagTree.tags.ai[tag]) {
                            tagTree.tags.ai[tag] = {
                                count: 0,
                                file_ids: []
                            };
                        }
                        tagTree.tags.ai[tag].count++;
                        tagTree.tags.ai[tag].file_ids.push(file.id);
                    }
                });
            }

            // 处理属性标签
            if (file.tags.properties) {
                Object.entries(file.tags.properties).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        if (!tagTree.properties[key]) {
                            tagTree.properties[key] = {};
                        }
                        const valueStr = String(value);
                        if (!tagTree.properties[key][valueStr]) {
                            tagTree.properties[key][valueStr] = {
                                count: 0,
                                file_ids: []
                            };
                        }
                        tagTree.properties[key][valueStr].count++;
                        tagTree.properties[key][valueStr].file_ids.push(file.id);
                    }
                });
            }
        });

        console.log('✅ 标签树构建完成:', tagTree);
        return tagTree;
    }

    // 异步加载并显示文件的所有标签
    async loadAndDisplayFileTags(fileId, container) {
        try {
            // 检查容器是否存在
            if (!container) {
                console.error('❌ 标签容器不存在');
                return;
            }

            // 显示加载状态
            container.innerHTML = '<span class="text-gray-400">加载标签中...</span>';

            // 获取文件的所有标签
            const tagData = await api.getFileAllTags(this.currentCaseId, fileId);

            // 构建标签显示
            const tagElements = [];

            // 添加自定义标签 - 添加点击事件支持双向关联
            if (tagData.custom_tags && tagData.custom_tags.length > 0) {
                tagData.custom_tags.forEach(tag => {
                    tagElements.push(`
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white mr-2 mb-2 cursor-pointer hover:opacity-80 transition-opacity"
                              style="background-color: ${tag.color || '#3B82F6'}"
                              data-tag-type="custom"
                              data-tag-id="${tag.id}"
                              data-tag-text="${tag.name}"
                              title="点击跳转到标签管理 - 自定义标签: ${tag.name}">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd"></path>
                            </svg>
                            ${tag.name}
                        </span>
                    `);
                });
            }

            // 添加系统标签 - 适应新的API返回格式
            if (tagData.tags || tagData.properties) {
                // 质量分数 - 添加点击事件支持双向关联
                if (tagData.properties && tagData.properties.qualityScore !== null && tagData.properties.qualityScore !== undefined) {
                    const score = tagData.properties.qualityScore;
                    const color = score >= 80 ? '#10B981' : score >= 60 ? '#F59E0B' : '#EF4444';
                    const qualityText = `质量: ${score}%`;

                    tagElements.push(`
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white mr-2 mb-2 cursor-pointer hover:opacity-80 transition-opacity"
                              style="background-color: ${color}"
                              data-tag-type="quality"
                              data-tag-key="qualityScore"
                              data-tag-text="${qualityText}"
                              title="点击跳转到标签管理 - 质量分数: ${score}%">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            质量: ${score}%
                        </span>
                    `);
                }

                // 元数据标签 - 根据新规则处理
                if (tagData.tags && tagData.tags.metadata) {
                    const metadata = tagData.tags.metadata;
                    console.log('🔍 处理元数据:', metadata);

                    // === 元数据标签 (metadata) - 拍摄相关的技术参数 ===

                    // 1. 相机信息（合并后的camera字段）
                    if (metadata.camera) {
                        tagElements.push(this.createMetadataTag('', `相机: ${metadata.camera}`, 'metadata', 'camera', metadata.camera));
                        console.log('✅ 添加相机:', metadata.camera);
                    }

                    // 2. ISO感光度
                    if (metadata.iso) {
                        tagElements.push(this.createMetadataTag('', `ISO: ${metadata.iso}`, 'metadata', 'iso', metadata.iso));
                        console.log('✅ 添加ISO:', metadata.iso);
                    }

                    // 3. 光圈
                    if (metadata.aperture) {
                        tagElements.push(this.createMetadataTag('', `光圈: ${metadata.aperture}`, 'metadata', 'aperture', metadata.aperture));
                        console.log('✅ 添加光圈:', metadata.aperture);
                    }

                    // 4. 快门速度
                    if (metadata.shutter_speed) {
                        tagElements.push(this.createMetadataTag('', `快门: ${metadata.shutter_speed}`, 'metadata', 'shutter_speed', metadata.shutter_speed));
                        console.log('✅ 添加快门:', metadata.shutter_speed);
                    }

                    // 5. 色彩标准
                    if (metadata.color_standard) {
                        tagElements.push(this.createMetadataTag('', `色彩: ${metadata.color_standard}`, 'metadata', 'color_standard', metadata.color_standard));
                        console.log('✅ 添加色彩标准:', metadata.color_standard);
                    }

                    // 6. 焦距
                    if (metadata.focal_length) {
                        tagElements.push(this.createMetadataTag('', `焦距: ${metadata.focal_length}`, 'metadata', 'focal_length', metadata.focal_length));
                        console.log('✅ 添加焦距:', metadata.focal_length);
                    }

                    // 7. 白平衡
                    if (metadata.white_balance) {
                        tagElements.push(this.createMetadataTag('', `白平衡: ${metadata.white_balance}`, 'metadata', 'white_balance', metadata.white_balance));
                        console.log('✅ 添加白平衡:', metadata.white_balance);
                    }

                    // 8. 闪光灯
                    if (metadata.flash) {
                        tagElements.push(this.createMetadataTag('', `闪光灯: ${metadata.flash}`, 'metadata', 'flash', metadata.flash));
                        console.log('✅ 添加闪光灯:', metadata.flash);
                    }
                }

                // === 处理属性标签 (properties) - 文件和图像的基本属性 ===
                if (tagData.properties) {
                    const properties = tagData.properties;

                    // 1. 文件类型
                    if (properties.fileType) {
                        const displayType = properties.fileType.toUpperCase();
                        tagElements.push(this.createMetadataTag('', `类型: ${displayType}`, 'properties', 'fileType', properties.fileType));
                        console.log('✅ 添加文件类型:', displayType);
                    }

                    // 2. 图像尺寸
                    if (properties.dimensions) {
                        tagElements.push(this.createMetadataTag('', `尺寸: ${properties.dimensions}`, 'properties', 'dimensions', properties.dimensions));
                        console.log('✅ 添加图像尺寸:', properties.dimensions);
                    }

                    // 3. 分辨率
                    if (properties.resolution) {
                        tagElements.push(this.createMetadataTag('', `分辨率: ${properties.resolution}`, 'properties', 'resolution', properties.resolution));
                        console.log('✅ 添加分辨率:', properties.resolution);
                    }

                    // 4. 色深信息
                    if (properties.color_depth) {
                        tagElements.push(this.createMetadataTag('', `色深: ${properties.color_depth}`, 'properties', 'color_depth', properties.color_depth));
                        console.log('✅ 添加色深:', properties.color_depth);
                    }
                }

                // AI标签
                if (tagData.tags && tagData.tags.ai) {
                    tagData.tags.ai.slice(0, 3).forEach(aiTag => {
                        tagElements.push(`
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mr-2 mb-2">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                </svg>
                                ${aiTag}
                            </span>
                        `);
                    });
                }
            }

            // 显示标签或无标签提示
            if (tagElements.length > 0) {
                container.innerHTML = `
                    <div class="flex flex-wrap items-start">
                        ${tagElements.join('')}
                    </div>
                `;

                // 为标签添加点击事件监听器
                this.attachTagClickEvents(container, fileId);
            } else {
                container.innerHTML = '<span class="text-gray-400 text-sm">暂无标签</span>';
            }

        } catch (error) {
            console.error('❌ 加载文件标签失败:', error);
            console.error('错误详情:', {
                message: error.message,
                stack: error.stack,
                fileId,
                caseId: this.currentCaseId
            });
            if (container) {
                container.innerHTML = '<span class="text-red-500 text-sm">加载标签失败: ' + error.message + '</span>';
            }
        }
    }

    // 创建元数据标签的辅助方法 - 添加点击事件支持双向关联
    createMetadataTag(icon, text, tagType = 'metadata', tagKey = null, tagValue = null) {
        // 如果没有提供tagValue，从text中提取（去掉前缀）
        let actualValue = tagValue;
        if (!actualValue && text.includes(': ')) {
            actualValue = text.split(': ')[1];
        } else if (!actualValue) {
            actualValue = text;
        }

        return `
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2 mb-2 cursor-pointer hover:bg-blue-200 transition-colors"
                  data-tag-type="${tagType}"
                  data-tag-key="${tagKey || actualValue}"
                  data-tag-text="${actualValue}"
                  title="点击跳转到标签管理 - ${tagKey}: ${actualValue}">
                <span class="mr-1">${icon}</span>
                ${text}
            </span>
        `;
    }

  attachGalleryEvents() {
        const galleryItems = document.querySelectorAll('.gallery-item');
        
        galleryItems.forEach(item => {
            const fileId = parseInt(item.dataset.fileId);
            
            // 点击图片处理
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.gallery-item-checkbox')) {
                    if (this.isSelectionMode) {
                        // 选择模式下，点击图片也可以选择
                        this.toggleFileSelection(fileId, item);
                    } else {
                        // 非选择模式下，点击图片查看大图
                        this.showImageModal(fileId);
                    }
                }
            });

            // 选择框点击
            const checkbox = item.querySelector('.gallery-item-checkbox');
            checkbox.addEventListener('click', (e) => {
                e.stopPropagation();
                if (!this.isSelectionMode) {
                    // 如果不在选择模式，先进入选择模式
                    this.enterSelectionMode();
                }
                this.toggleFileSelection(fileId, item);
            });
        });
        
        // 批量操作按钮
        this.initBatchActions();
    }
    
    initBatchActions() {
        // 选择模式按钮
        document.getElementById('select-mode-btn').addEventListener('click', () => {
            this.enterSelectionMode();
        });

        // 全选按钮
        document.getElementById('select-all-btn').addEventListener('click', () => {
            this.selectAllFiles();
        });

        // 清除选择按钮
        document.getElementById('clear-selection-btn').addEventListener('click', () => {
            this.clearFileSelection();
        });

        // 退出选择模式按钮
        document.getElementById('exit-selection-btn').addEventListener('click', () => {
            this.exitSelectionMode();
        });
        
        // 批量添加标签
        document.getElementById('batch-add-tag-btn').addEventListener('click', () => {
            this.batchAddTag();
        });
        
        // 批量移除标签
        document.getElementById('batch-remove-tag-btn').addEventListener('click', () => {
            this.batchRemoveTag();
        });
        
        // 更新批量操作的标签选择器
        this.updateBatchTagSelect();
    }
    
    toggleFileSelection(fileId, itemElement) {
        if (this.selectedFiles.has(fileId)) {
            this.selectedFiles.delete(fileId);
            itemElement.classList.remove('selected');
        } else {
            this.selectedFiles.add(fileId);
            itemElement.classList.add('selected');
        }
        
        this.updateBatchActionsVisibility();
        this.updateFileCount();
    }

    // 进入选择模式
    enterSelectionMode() {
        // 显示选择操作按钮，隐藏选择模式按钮
        document.getElementById('select-mode-btn').classList.add('hidden');
        document.getElementById('selection-actions').classList.remove('hidden');

        // 启用文件选择功能
        this.isSelectionMode = true;

        // 添加CSS类以显示选择框
        document.body.classList.add('selection-mode');

        // 更新画廊显示，显示选择框
        this.renderGallery();

        showNotification('已进入选择模式，点击照片进行选择', 'info');
    }

    // 退出选择模式
    exitSelectionMode() {
        // 隐藏选择操作按钮，显示选择模式按钮
        document.getElementById('select-mode-btn').classList.remove('hidden');
        document.getElementById('selection-actions').classList.add('hidden');

        // 清除所有选择
        this.clearFileSelection();

        // 禁用文件选择功能
        this.isSelectionMode = false;

        // 移除CSS类以隐藏选择框
        document.body.classList.remove('selection-mode');

        // 更新画廊显示，隐藏选择框
        this.renderGallery();

        showNotification('已退出选择模式', 'info');
    }

    selectAllFiles() {
        if (!this.isSelectionMode) {
            this.enterSelectionMode();
        }

        this.selectedFiles.clear();
        this.currentFiles.forEach(file => {
            this.selectedFiles.add(file.id);
        });

        document.querySelectorAll('.gallery-item').forEach(item => {
            item.classList.add('selected');
        });

        this.updateBatchActionsVisibility();
        this.updateFileCount();
    }
    
    clearFileSelection() {
        this.selectedFiles.clear();
        
        document.querySelectorAll('.gallery-item.selected').forEach(item => {
            item.classList.remove('selected');
        });
        
        this.updateBatchActionsVisibility();
        this.updateFileCount();
    }
    
    updateBatchActionsVisibility() {
        const batchActions = document.getElementById('batch-actions');
        const selectedCount = document.getElementById('batch-selected-count');
        
        if (this.selectedFiles.size > 0) {
            batchActions.classList.remove('hidden');
            selectedCount.textContent = this.selectedFiles.size;
        } else {
            batchActions.classList.add('hidden');
        }
    }    

    updateBatchTagSelect() {
        const select = document.getElementById('batch-tag-select');
        const customTags = this.tagTree?.custom || [];
        
        select.innerHTML = '<option value="">选择标签...</option>' +
            customTags.map(tag => `
                <option value="${tag.id}">${tag.name}</option>
            `).join('');
    }
    
    updateFileCount() {
        document.getElementById('selected-files-count').textContent = this.selectedFiles.size;
        document.getElementById('total-files-count').textContent = this.currentFiles.length;
    }
    
    updateGalleryTitle(title) {
        document.getElementById('gallery-subtitle').textContent = title;
    }
    
    async batchAddTag() {
        const tagId = parseInt(document.getElementById('batch-tag-select').value);
        if (!tagId || this.selectedFiles.size === 0) {
            showNotification('请选择标签和文件', 'warning');
            return;
        }

        try {
            // 根据标签ID找到标签名称
            const customTag = this.tagTree.custom.find(tag => tag.id === tagId);
            if (!customTag) {
                showNotification('找不到指定的标签', 'error');
                return;
            }

            const fileIds = Array.from(this.selectedFiles);
            await api.batchTagOperation(this.currentCaseId, 'add', fileIds, customTag.name);

            showNotification(`成功为 ${fileIds.length} 个文件添加标签 "${customTag.name}"`, 'success');

            // 刷新数据
            await this.loadData();

        } catch (error) {
            console.error('批量添加标签失败:', error);
            showNotification('批量添加标签失败', 'error');
        }
    }
    
    async batchRemoveTag() {
        const tagId = parseInt(document.getElementById('batch-tag-select').value);
        if (!tagId || this.selectedFiles.size === 0) {
            showNotification('请选择标签和文件', 'warning');
            return;
        }

        try {
            // 根据标签ID找到标签名称
            const customTag = this.tagTree.custom.find(tag => tag.id === tagId);
            if (!customTag) {
                showNotification('找不到指定的标签', 'error');
                return;
            }

            const fileIds = Array.from(this.selectedFiles);
            await api.batchTagOperation(this.currentCaseId, 'remove', fileIds, customTag.name);

            showNotification(`成功从 ${fileIds.length} 个文件移除标签 "${customTag.name}"`, 'success');

            // 刷新数据
            await this.loadData();

        } catch (error) {
            console.error('批量移除标签失败:', error);
            showNotification('批量移除标签失败', 'error');
        }
    }
    
    async searchTags(query) {
        if (!query.trim()) {
            this.clearSearch();
            return;
        }
        
        try {
            const results = await api.searchTags(this.currentCaseId, query);
            this.renderSearchResults(results.results);
        } catch (error) {
            console.error('搜索标签失败:', error);
            showNotification('搜索失败', 'error');
        }
    }
    
    clearSearch() {
        // 恢复原始标签树显示
        this.renderTagTree();
        this.loadAllFiles();
    }  
  
    renderSearchResults(results) {
        // 隐藏所有标签类别
        document.querySelectorAll('.tag-category-content').forEach(content => {
            content.classList.add('hidden');
        });
        
        // 清空自定义标签列表
        document.getElementById('custom-tags-list').innerHTML = '';
        
        // 按类别分组搜索结果
        const groupedResults = {};
        results.forEach(result => {
            if (!groupedResults[result.category]) {
                groupedResults[result.category] = [];
            }
            groupedResults[result.category].push(result);
        });
        
        // 渲染搜索结果
        Object.entries(groupedResults).forEach(([category, items]) => {
            if (category === 'custom') {
                // 渲染自定义标签搜索结果
                const container = document.getElementById('custom-tags-list');
                container.innerHTML = items.map(item => `
                    <div class="custom-tag-item search-result" data-file-ids='${JSON.stringify(item.file_ids)}'>
                        <div class="custom-tag-color" style="background-color: ${item.color || '#3B82F6'}"></div>
                        <span class="custom-tag-name">${item.name}</span>
                        <span class="custom-tag-count">${item.count}</span>
                    </div>
                `).join('');
                
                // 添加点击事件
                container.querySelectorAll('.search-result').forEach(item => {
                    item.addEventListener('click', () => {
                        const fileIds = JSON.parse(item.dataset.fileIds);
                        this.showFilesById(fileIds);
                        this.updateGalleryTitle(`搜索结果: ${item.querySelector('.custom-tag-name').textContent}`);
                    });
                });
            } else {
                // 渲染系统标签搜索结果
                const container = document.getElementById(`${category}-tags`);
                if (container) {
                    container.classList.remove('hidden');
                    container.innerHTML = items.map(item => `
                        <div class="tag-item search-result" data-file-ids='${JSON.stringify(item.file_ids)}'>
                            <span class="tag-name">${item.name}: ${item.value}</span>
                            <span class="tag-count">${item.count}</span>
                        </div>
                    `).join('');
                    
                    // 添加点击事件
                    container.querySelectorAll('.search-result').forEach(item => {
                        item.addEventListener('click', () => {
                            const fileIds = JSON.parse(item.dataset.fileIds);
                            this.showFilesById(fileIds);
                            this.updateGalleryTitle(`搜索结果: ${item.querySelector('.tag-name').textContent}`);
                        });
                    });
                }
            }
        });
    }
    
    async showFilesById(fileIds) {
        try {
            // 🚀 性能优化：按需加载文件数据
            if (!this.currentCase.files) {
                console.log('📥 按需加载文件数据用于筛选...');
                const caseData = await api.getCase(this.currentCaseId);
                this.currentCase.files = caseData.files || [];
            }

            this.currentFiles = this.currentCase.files.filter(file =>
                fileIds.includes(file.id)
            );
            this.renderGallery();
            this.updateFileCount();
        } catch (error) {
            console.error('显示文件失败:', error);
            showNotification('显示文件失败', 'error');
        }
    }
    
    async showImageModal(fileId) {
        const file = this.currentFiles.find(f => f.id === fileId);
        if (!file) return;

        // 记录当前模态框的文件ID，用于标签点击事件
        this.currentModalFileId = fileId;

        const modal = document.getElementById('image-modal');
        const modalImage = document.getElementById('modal-image');
        const imageTitle = document.getElementById('image-title');
        const imageInfo = document.getElementById('image-info');
        const imageTags = document.getElementById('image-tags');

        // 显示模态框
        modal.classList.remove('hidden');

        // 防止页面滚动
        document.body.style.overflow = 'hidden';

        // 设置加载状态
        modalImage.style.opacity = '0';
        modalImage.style.transition = 'opacity 0.3s ease';

        // 检查并更新EXIF数据（按照新规则：属性标签和元数据标签不可变更，自动更新）
        await this.checkAndUpdateExifData(file);

        // 设置图片
        if (file.file_type && file.file_type.startsWith('image/')) {
            const imageUrl = api.getFileViewUrl(this.currentCaseId, file.id);
            modalImage.src = imageUrl;

            // 图片加载完成后显示
            modalImage.onload = () => {
                modalImage.style.opacity = '1';
            };

            // 图片加载失败处理
            modalImage.onerror = () => {
                modalImage.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgMTAwSDMwMFYyMDBIMTAwVjEwMFoiIGZpbGw9IiNFNUU3RUIiIHN0cm9rZT0iI0QxRDVEQiIgc3Ryb2tlLXdpZHRoPSIyIiByeD0iNCIvPgo8Y2lyY2xlIGN4PSIxNTAiIGN5PSIxMzAiIHI9IjE1IiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xMDAgMTYwTDE1MCA5MUwyMDAgMTMwTDI1MCA5MUwzMDAgMTYwVjIwMEgxMDBWMTYwWiIgZmlsbD0iI0QxRDVEQiIvPgo8dGV4dCB4PSIyMDAiIHk9IjI1MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzZCNzI4MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4Ij7ml6DmtZXpooTop4jnlKjlm77niYc8L3RleHQ+Cjwvc3ZnPg==';
                modalImage.style.opacity = '1';
                console.warn('图片加载失败，显示占位图');
            };
        } else {
            modalImage.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgMTAwSDMwMFYyMDBIMTAwVjEwMFoiIGZpbGw9IiNFNUU3RUIiIHN0cm9rZT0iI0QxRDVEQiIgc3Ryb2tlLXdpZHRoPSIyIiByeD0iNCIvPgo8Y2lyY2xlIGN4PSIxNTAiIGN5PSIxMzAiIHI9IjE1IiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xMDAgMTYwTDE1MCA5MUwyMDAgMTMwTDI1MCA5MUwzMDAgMTYwVjIwMEgxMDBWMTYwWiIgZmlsbD0iI0QxRDVEQiIvPgo8dGV4dCB4PSIyMDAiIHk9IjI1MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzZCNzI4MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4Ij7ml6DmtZXpooTop4jnlKjlm77niYc8L3RleHQ+Cjwvc3ZnPg==';
            modalImage.style.opacity = '1';
        }

        // 设置标题和信息
        imageTitle.textContent = file.file_name;
        imageInfo.textContent = `${file.file_type || 'Unknown'} • ${this.formatFileSize(file)} • ${formatDate(file.created_at)}`;

        // 异步加载并设置标签
        if (imageTags) {
            this.loadAndDisplayFileTags(file.id, imageTags);
        } else {
            console.error('❌ imageTags 元素未找到');
        }

        // 关闭模态框函数
        const closeModal = () => {
            modal.classList.add('hidden');
            document.body.style.overflow = '';
            document.removeEventListener('keydown', handleKeydown);
        };

        // 键盘事件处理
        const handleKeydown = (e) => {
            if (e.key === 'Escape') {
                closeModal();
            }
        };

        // 添加键盘事件监听
        document.addEventListener('keydown', handleKeydown);

        // 关闭按钮事件
        document.getElementById('close-image-modal').onclick = closeModal;

        // 点击背景关闭
        modal.onclick = (e) => {
            if (e.target === modal) {
                closeModal();
            }
        };

        // 为图片添加标签按钮
        document.getElementById('add-tag-to-image-btn').onclick = () => {
            this.showAddTagToImageModal(file);
        };
    }

    formatFileSize(file) {
        // 简单的文件大小格式化
        const size = file.file_size || 0;
        if (size === 0) return '未知大小';

        const units = ['B', 'KB', 'MB', 'GB'];
        let unitIndex = 0;
        let fileSize = size;

        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }

        return `${fileSize.toFixed(1)} ${units[unitIndex]}`;
    }
 
   showAddCustomTagModal() {
        this.showCustomTagModal();
    }
    
    showEditCustomTagModal(tagId) {
        const tag = this.tagTree.custom.find(t => t.id === tagId);
        if (tag) {
            this.showCustomTagModal(tag);
        }
    }
    
    showCustomTagModal(existingTag = null) {
        const modal = document.getElementById('modal-overlay');
        const content = document.getElementById('modal-content');
        
        const isEdit = existingTag !== null;
        const title = isEdit ? '编辑自定义标签' : '添加自定义标签';
        
        content.innerHTML = `
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">${title}</h2>
                    <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <form id="custom-tag-form">
                    <div class="mb-4">
                        <label for="tag-name" class="block text-sm font-medium text-gray-700 mb-2">标签名称</label>
                        <input type="text" id="tag-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                               value="${isEdit ? existingTag.name : ''}" required>
                    </div>
                    
                    <div class="mb-6">
                        <label for="tag-color" class="block text-sm font-medium text-gray-700 mb-2">标签颜色</label>
                        <div class="flex items-center space-x-3">
                            <input type="color" id="tag-color" class="w-12 h-10 border border-gray-300 rounded cursor-pointer" 
                                   value="${isEdit ? existingTag.color : '#3B82F6'}">
                            <div class="flex space-x-2">
                                ${['#EF4444', '#F59E0B', '#10B981', '#3B82F6', '#8B5CF6', '#EC4899'].map(color => `
                                    <button type="button" class="color-preset w-8 h-8 rounded-full border-2 border-gray-300 hover:border-gray-400" 
                                            style="background-color: ${color}" data-color="${color}"></button>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" id="cancel-btn" class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                            ${isEdit ? '保存' : '添加'}
                        </button>
                    </div>
                </form>
            </div>
        `;
        
        modal.classList.remove('hidden');
        
        // 事件监听器
        document.getElementById('close-modal').onclick = () => modal.classList.add('hidden');
        document.getElementById('cancel-btn').onclick = () => modal.classList.add('hidden');
        
        // 颜色预设按钮
        document.querySelectorAll('.color-preset').forEach(btn => {
            btn.onclick = () => {
                document.getElementById('tag-color').value = btn.dataset.color;
            };
        });
        
        // 表单提交
        document.getElementById('custom-tag-form').onsubmit = async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('tag-name').value.trim();
            const color = document.getElementById('tag-color').value;
            
            if (!name) {
                showNotification('请输入标签名称', 'warning');
                return;
            }
            
            try {
                if (isEdit) {
                    await api.updateCustomTag(this.currentCaseId, existingTag.id, { name, color });
                    showNotification('标签更新成功', 'success');
                } else {
                    await api.createCustomTag(this.currentCaseId, name, color);
                    showNotification('标签创建成功', 'success');
                }
                
                modal.classList.add('hidden');
                await this.refreshCustomTags();
                
            } catch (error) {
                console.error('保存标签失败:', error);
                showNotification('保存标签失败', 'error');
            }
        };
        
        // 聚焦输入框
        document.getElementById('tag-name').focus();
    }
    
    async deleteCustomTag(tagId) {
        const tag = this.tagTree.custom.find(t => t.id === tagId);
        if (!tag) return;
        
        const confirmed = confirm(`确定要删除标签"${tag.name}"吗？\n这将从所有文件中移除该标签。`);
        if (!confirmed) return;
        
        try {
            await api.deleteCustomTag(this.currentCaseId, tagId);
            showNotification('标签删除成功', 'success');
            await this.refreshCustomTags();
        } catch (error) {
            console.error('删除标签失败:', error);
            showNotification('删除标签失败', 'error');
        }
    }

    async refreshCustomTags() {
        try {
            console.log('🔄 增量更新自定义标签...');
            
            // 只重新加载自定义标签数据
            const customTags = await api.getCustomTags(this.currentCaseId);
            
            // 更新标签树中的自定义标签部分
            this.tagTree.custom = customTags || [];
            
            // 只重新渲染自定义标签部分，保持其他标签的展开状态
            this.renderCustomTags();
            
            // 更新标签统计
            this.updateTagStats({});
            
            console.log('✅ 自定义标签增量更新完成');
            
        } catch (error) {
            console.error('❌ 刷新自定义标签失败:', error);
            showNotification('刷新标签失败', 'warning');
        }
    }    
    sh
owAddTagToImageModal(file) {
        const modal = document.getElementById('modal-overlay');
        const content = document.getElementById('modal-content');
        
        const customTags = this.tagTree?.custom || [];
        
        content.innerHTML = `
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">为文件添加标签</h2>
                    <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">文件: ${file.file_name}</p>
                </div>
                
                <form id="add-tag-form">
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">选择要添加的标签:</label>
                        <div class="space-y-2 max-h-60 overflow-y-auto">
                            ${customTags.map(tag => `
                                <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <input type="checkbox" value="${tag.id}" class="mr-3">
                                    <div class="w-4 h-4 rounded-full mr-2" style="background-color: ${tag.color}"></div>
                                    <span>${tag.name}</span>
                                    <span class="ml-auto text-sm text-gray-500">${tag.count} 个文件</span>
                                </label>
                            `).join('')}
                        </div>
                        
                        ${customTags.length === 0 ? `
                            <div class="text-center py-4 text-gray-500">
                                <p>暂无自定义标签</p>
                                <button type="button" id="create-tag-btn" class="mt-2 text-blue-600 hover:text-blue-800">
                                    创建新标签
                                </button>
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" id="cancel-btn" class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                            添加标签
                        </button>
                    </div>
                </form>
            </div>
        `;
        
        modal.classList.remove('hidden');
        
        // 事件监听器
        document.getElementById('close-modal').onclick = () => modal.classList.add('hidden');
        document.getElementById('cancel-btn').onclick = () => modal.classList.add('hidden');
        
        // 创建新标签按钮
        const createTagBtn = document.getElementById('create-tag-btn');
        if (createTagBtn) {
            createTagBtn.onclick = () => {
                modal.classList.add('hidden');
                this.showAddCustomTagModal();
            };
        }
        
        // 表单提交
        document.getElementById('add-tag-form').onsubmit = async (e) => {
            e.preventDefault();
            
            const selectedTags = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                .map(cb => parseInt(cb.value));
            
            if (selectedTags.length === 0) {
                showNotification('请选择要添加的标签', 'warning');
                return;
            }
            
            try {
                await api.addCustomTagsToFile(this.currentCaseId, file.id, selectedTags);
                showNotification(`成功为文件添加 ${selectedTags.length} 个标签`, 'success');

                modal.classList.add('hidden');

                // 刷新整个页面数据
                await this.loadData();

                // 如果图片模态框仍然打开，刷新其中的标签显示
                const imageModal = document.getElementById('image-modal');
                if (imageModal && !imageModal.classList.contains('hidden')) {
                    const imageTags = document.getElementById('image-tags');
                    if (imageTags) {
                        console.log('🔄 刷新图片模态框中的标签显示');
                        await this.loadAndDisplayFileTags(file.id, imageTags);
                    }
                }

            } catch (error) {
                console.error('添加标签失败:', error);
                showNotification('添加标签失败', 'error');
            }
        };
    }
    
    showLoading() {
        document.getElementById('gallery-loading').classList.remove('hidden');
        document.getElementById('gallery-grid').classList.add('hidden');
        document.getElementById('gallery-empty').classList.add('hidden');
    }
    
    hideLoading() {
        document.getElementById('gallery-loading').classList.add('hidden');
    }
    
    goBack() {
        // 返回案例详情页面
        if (this.currentCaseId) {
            window.location.href = `case-view.html?caseId=${this.currentCaseId}`;
        } else {
            // 如果没有案例ID，返回案例列表
            window.location.href = 'index.html';
        }
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification bg-white border-l-4 p-4 mb-2 rounded-r shadow-lg ${
            type === 'success' ? 'border-green-500 text-green-700' :
            type === 'error' ? 'border-red-500 text-red-700' :
            type === 'warning' ? 'border-yellow-500 text-yellow-700' :
            'border-blue-500 text-blue-700'
        }`;

        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button class="ml-4 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;

        const container = document.getElementById('notifications');
        if (container) {
            container.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    // ==================== 标签双向关联方法 ====================

    // 为标签添加点击事件监听器
    attachTagClickEvents(container, fileId) {
        attachTagClickEvents(container, fileId);
    }

    // 标签点击处理主方法
    handleTagClick(tagType, tagId, tagKey, tagText, sourceFileId) {
        handleTagClick(tagType, tagId, tagKey, tagText, sourceFileId);
    }

    // 关闭图片模态框
    closeImageModal() {
        closeImageModal();
    }

    // 检查并更新EXIF数据
    async checkAndUpdateExifData(file) {
        try {
            console.log('🔍 检查EXIF数据是否需要更新:', file.file_name);

            // 重新提取EXIF数据（按照新规则：属性标签和元数据标签自动更新）
            const response = await api.reprocessFileMetadata(this.currentCaseId, file.id);

            if (response && response.tags) {
                console.log('✅ EXIF数据已更新:', file.file_name);

                // 更新当前文件的标签数据
                const fileIndex = this.currentFiles.findIndex(f => f.id === file.id);
                if (fileIndex !== -1) {
                    this.currentFiles[fileIndex].tags = response.tags;
                }

                // 如果当前案例数据中也有这个文件，同步更新
                if (this.currentCase && this.currentCase.files) {
                    const caseFileIndex = this.currentCase.files.findIndex(f => f.id === file.id);
                    if (caseFileIndex !== -1) {
                        this.currentCase.files[caseFileIndex].tags = response.tags;
                    }
                }

                console.log('🔄 文件标签数据已同步更新');

                // 重新渲染画廊以反映标签变化
                this.renderGallery();
            }

        } catch (error) {
            console.warn('⚠️ 更新EXIF数据失败:', error.message);
            // 不阻断用户操作，只是记录警告
        }
    }
}

// 全局通知函数，供其他地方调用
function showNotification(message, type = 'info') {
    if (window.tagApp && window.tagApp.showNotification) {
        window.tagApp.showNotification(message, type);
    } else {
        // 如果tagApp还没有初始化，使用简单的alert
        alert(message);
    }
}

// ==================== 标签双向关联功能 ====================

// 为标签添加点击事件监听器
function attachTagClickEvents(container, fileId) {
    const tagElements = container.querySelectorAll('[data-tag-type]');

    tagElements.forEach(tagElement => {
        tagElement.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            const tagType = tagElement.dataset.tagType;
            const tagId = tagElement.dataset.tagId;
            const tagKey = tagElement.dataset.tagKey;
            const tagText = tagElement.dataset.tagText;

            console.log('🔗 标签点击事件:', { tagType, tagId, tagKey, tagText, fileId });

            // 调用标签点击处理方法
            if (window.tagApp) {
                window.tagApp.handleTagClick(tagType, tagId, tagKey, tagText, fileId);
            }
        });
    });
}

// 🎯 配置驱动的标签点击处理主方法
function handleTagClick(tagType, tagId, tagKey, tagText, sourceFileId) {
    console.log('🔗 处理标签点击 (配置驱动):', { tagType, tagId, tagKey, tagText, sourceFileId });

    // 记录源图片ID（用于后续高亮）
    if (window.tagApp) {
        window.tagApp.sourceFileId = sourceFileId;
        window.tagApp.currentModalFileId = sourceFileId;
    }

    // 关闭图片模态框
    closeImageModal();

    // 🎯 配置驱动：从配置中获取标签类别信息
    const categoryConfig = TAG_CATEGORIES[tagType];

    if (!categoryConfig) {
        console.warn('未知标签类型:', tagType);
        showNotification(`未知标签类型: ${tagType}`, 'error');
        return;
    }

    console.log(`${categoryConfig.icon} 处理${categoryConfig.displayName}点击`);

    // 🎯 配置驱动：动态调用对应的处理器
    try {
        const handlerName = categoryConfig.handler;
        const handlerFunction = window[handlerName];

        if (typeof handlerFunction === 'function') {
            // 根据标签类型传递不同的参数
            if (tagType === 'custom') {
                handlerFunction(tagId, tagText, categoryConfig);
            } else {
                handlerFunction(tagKey, tagText, categoryConfig);
            }
        } else {
            console.error(`处理器函数不存在: ${handlerName}`);
            showNotification(`标签处理器未实现: ${categoryConfig.displayName}`, 'error');
        }
    } catch (error) {
        console.error(`处理${categoryConfig.displayName}点击时出错:`, error);
        showNotification(`处理${categoryConfig.displayName}失败`, 'error');
    }
}

// 🎯 配置驱动的通用标签处理逻辑
function handleTagClickGeneric(tagType, tagId, tagText, categoryConfig, options = {}) {
    console.log(`${categoryConfig.icon} 通用标签处理:`, { tagType, tagId, tagText, options });

    if (!window.tagApp) return;

    // 构建查找参数
    const searchParams = {
        category: tagType,
        id: options.useId ? tagId : null,
        key: options.searchKey || tagId,
        value: options.searchValue || tagText,
        text: tagText
    };

    // 使用全局查找逻辑
    const foundTag = findTagGlobally(searchParams.key, searchParams.text);

    if (foundTag && foundTag.category === tagType) {
        console.log('🎯 全局查找成功:', foundTag);

        // 展开标签分类
        if (categoryConfig.expander) {
            const expanderFunction = window[categoryConfig.expander];
            if (typeof expanderFunction === 'function') {
                expanderFunction(tagType);
            }
        }

        // 等待展开完成后查找并高亮标签
        setTimeout(() => {
            const tagElement = document.querySelector(foundTag.selector);
            if (tagElement) {
                // 清除其他选择
                document.querySelectorAll('.tag-item.selected, .custom-tag-item.selected').forEach(item => {
                    item.classList.remove('selected');
                });

                // 高亮当前标签
                tagElement.classList.add('selected');

                // 滚动到标签位置
                setTimeout(() => {
                    tagElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);

                // 筛选显示文件
                filterAndDisplayFiles(tagType, foundTag, categoryConfig, tagText);

                // 高亮源图片
                setTimeout(() => {
                    highlightSourceImage();
                }, 500);

                // 生成标题
                const title = generateTagTitle(categoryConfig.titleTemplate, {
                    name: tagText,
                    key: searchParams.key,
                    value: searchParams.value
                });

                window.tagApp.showNotification(`已跳转到${title}`, 'success');
            } else {
                console.warn(`展开后仍未找到${categoryConfig.displayName}元素:`, tagId);
                // 降级处理
                handleTagFallback(tagType, tagId, tagText, categoryConfig);
            }
        }, 200);
    } else {
        console.warn(`全局查找未找到${categoryConfig.displayName}:`, tagId, tagText);
        // 降级处理
        handleTagFallback(tagType, tagId, tagText, categoryConfig);
    }
}

// 🎯 配置驱动的文件筛选和显示
function filterAndDisplayFiles(tagType, foundTag, categoryConfig, tagText) {
    if (tagType === 'custom' && foundTag.customTag) {
        window.tagApp.showFilesWithCustomTag(foundTag.customTag);
        window.tagApp.updateGalleryTitle(`${categoryConfig.displayName}: ${foundTag.customTag.name}`);
    } else if (foundTag.fileIds && foundTag.fileIds.length > 0) {
        window.tagApp.showFilesById(foundTag.fileIds);
        const title = generateTagTitle(categoryConfig.titleTemplate, {
            name: tagText,
            key: foundTag.key || tagText,
            value: foundTag.value || tagText
        });
        window.tagApp.updateGalleryTitle(title);
    }
}

// 🎯 配置驱动的标题生成
function generateTagTitle(template, params) {
    return template
        .replace('{name}', params.name || '')
        .replace('{key}', params.key || '')
        .replace('{value}', params.value || '');
}

// 🎯 配置驱动的降级处理
function handleTagFallback(tagType, tagId, tagText, categoryConfig) {
    console.log(`${categoryConfig.icon} 执行${categoryConfig.displayName}降级处理`);

    if (tagType === 'custom') {
        const customTag = window.tagApp.tagTree.custom?.find(tag => tag.id === parseInt(tagId));
        if (customTag) {
            window.tagApp.showFilesWithCustomTag(customTag);
            window.tagApp.updateGalleryTitle(`${categoryConfig.displayName}: ${customTag.name}`);
            window.tagApp.showNotification(`已筛选${categoryConfig.displayName}: ${tagText}`, 'success');
            return;
        }
    }

    // 其他类型的降级处理可以在这里添加
    window.tagApp.showNotification(`未找到${categoryConfig.displayName}: ${tagText}`, 'warning');
}

// 处理自定义标签点击
function handleCustomTagClick(tagId, tagText, categoryConfig) {
    // 🎯 配置驱动：支持向后兼容
    if (!categoryConfig) {
        categoryConfig = TAG_CATEGORIES['custom'];
    }

    console.log(`${categoryConfig.icon} 处理${categoryConfig.displayName}点击:`, { tagId, tagText });

    if (!window.tagApp) return;

    // 🎯 配置驱动：使用通用的标签处理逻辑
    handleTagClickGeneric('custom', tagId, tagText, categoryConfig, {
        searchKey: tagId.toString(),
        searchValue: tagText,
        useId: true // 自定义标签优先使用ID查找
    });
}

// 🎯 配置驱动的元数据标签点击处理
function handleMetadataTagClick(tagKey, tagText, categoryConfig) {
    // 🎯 配置驱动：支持向后兼容
    if (!categoryConfig) {
        categoryConfig = TAG_CATEGORIES['metadata'];
    }

    console.log(`${categoryConfig.icon} 处理${categoryConfig.displayName}点击:`, { tagKey, tagText });

    if (!window.tagApp) return;

    // 🎯 配置驱动：使用通用的标签处理逻辑
    handleTagClickGeneric('metadata', tagKey, tagText, categoryConfig, {
        searchKey: tagKey,
        searchValue: tagText,
        useId: false // 元数据标签使用key查找
    });
}

// 🎯 配置驱动的属性标签点击处理
function handlePropertiesTagClick(tagKey, tagText, categoryConfig) {
    // 🎯 配置驱动：支持向后兼容
    if (!categoryConfig) {
        categoryConfig = TAG_CATEGORIES['properties'];
    }

    console.log(`${categoryConfig.icon} 处理${categoryConfig.displayName}点击:`, { tagKey, tagText });

    if (!window.tagApp) return;

    // 🎯 配置驱动：使用通用的标签处理逻辑
    handleTagClickGeneric('properties', tagKey, tagText, categoryConfig, {
        searchKey: tagKey,
        searchValue: tagText,
        useId: false
    });
}

// 🎯 配置驱动的质量标签点击处理
function handleQualityTagClick(tagKey, tagText, categoryConfig) {
    // 🎯 配置驱动：支持向后兼容
    if (!categoryConfig) {
        categoryConfig = TAG_CATEGORIES['quality'];
    }

    console.log(`${categoryConfig.icon} 处理${categoryConfig.displayName}点击:`, { tagKey, tagText });

    if (!window.tagApp) return;

    // 🎯 配置驱动：使用通用的标签处理逻辑
    handleTagClickGeneric('quality', tagKey, tagText, categoryConfig, {
        searchKey: tagKey,
        searchValue: tagText,
        useId: false
    });
}

// 🎯 配置驱动的CV标签点击处理
function handleCvTagClick(tagKey, tagText, categoryConfig) {
    // 🎯 配置驱动：支持向后兼容
    if (!categoryConfig) {
        categoryConfig = TAG_CATEGORIES['cv'];
    }

    console.log(`${categoryConfig.icon} 处理${categoryConfig.displayName}点击:`, { tagKey, tagText });

    if (!window.tagApp) return;

    // 🎯 配置驱动：使用通用的标签处理逻辑
    handleTagClickGeneric('cv', tagKey, tagText, categoryConfig, {
        searchKey: tagKey,
        searchValue: tagText,
        useId: false
    });
}

// 🎯 配置驱动的AI标签点击处理
function handleAiTagClick(tagKey, tagText, categoryConfig) {
    // 🎯 配置驱动：支持向后兼容
    if (!categoryConfig) {
        categoryConfig = TAG_CATEGORIES['ai'];
    }

    console.log(`${categoryConfig.icon} 处理${categoryConfig.displayName}点击:`, { tagKey, tagText });

    if (!window.tagApp) return;

    // 🎯 配置驱动：使用通用的标签处理逻辑
    handleTagClickGeneric('ai', tagKey, tagText, categoryConfig, {
        searchKey: tagKey,
        searchValue: tagText,
        useId: false
    });
}

// 处理元数据标签点击 (旧版本，保留向后兼容)
function handleMetadataTagClickLegacy(tagKey, tagText) {
    console.log('📊 处理元数据标签点击:', { tagKey, tagText });

    if (!window.tagApp) return;

    // 1. 全局查找标签（在所有分类中搜索）
    const foundTag = findTagGlobally(tagKey, tagText);

    if (foundTag) {
        console.log('🎯 全局查找成功:', foundTag);

        // 2. 展开对应的分类
        expandTagCategory(foundTag.category);

        // 3. 等待展开完成后查找并高亮标签
        setTimeout(() => {
            const tagElement = document.querySelector(foundTag.selector);
            if (tagElement) {
                // 清除其他选择
                document.querySelectorAll('.tag-item.selected, .custom-tag-item.selected').forEach(item => {
                    item.classList.remove('selected');
                });

                // 高亮当前标签
                tagElement.classList.add('selected');

                // 滚动到标签位置
                setTimeout(() => {
                    tagElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);

                // 4. 筛选显示该标签的所有文件
                window.tagApp.filterFilesByTag(foundTag.category, tagKey, tagText);
                window.tagApp.updateGalleryTitle(`${foundTag.categoryName}标签: ${tagText}`);

                // 5. 高亮源图片
                setTimeout(() => {
                    highlightSourceImage();
                }, 500);

                window.tagApp.showNotification(`已跳转到${foundTag.categoryName}标签: ${tagText}`, 'success');
            } else {
                console.warn('展开后仍未找到标签元素:', tagKey);
                // 降级处理：直接筛选文件
                window.tagApp.filterFilesByTag(foundTag.category, tagKey, tagText);
                window.tagApp.updateGalleryTitle(`${foundTag.categoryName}标签: ${tagText}`);
                window.tagApp.showNotification(`已筛选${foundTag.categoryName}标签: ${tagText}`, 'success');
            }
        }, 200);
    } else {
        console.warn('全局查找未找到标签:', tagKey, tagText);
        // 降级处理：尝试直接筛选
        window.tagApp.filterFilesByTag('metadata', tagKey, tagText);
        window.tagApp.updateGalleryTitle(`元数据标签: ${tagText}`);
        window.tagApp.showNotification(`已筛选标签: ${tagText}（可能不在当前视图中）`, 'warning');
    }
}

// 处理属性标签点击
function handlePropertiesTagClick(tagKey, tagText) {
    console.log('🏷️ 处理属性标签点击:', { tagKey, tagText });

    if (!window.tagApp) return;

    // 1. 全局查找标签（在所有分类中搜索）
    const foundTag = findTagGlobally(tagKey, tagText);

    if (foundTag) {
        console.log('🎯 全局查找成功:', foundTag);

        // 2. 展开对应的分类
        expandTagCategory(foundTag.category);

        // 3. 等待展开完成后查找并高亮标签
        setTimeout(() => {
            const tagElement = document.querySelector(foundTag.selector);
            if (tagElement) {
                // 清除其他选择
                document.querySelectorAll('.tag-item.selected, .custom-tag-item.selected').forEach(item => {
                    item.classList.remove('selected');
                });

                // 高亮当前标签
                tagElement.classList.add('selected');

                // 滚动到标签位置
                setTimeout(() => {
                    tagElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);

                // 4. 筛选显示该标签的所有文件
                window.tagApp.filterFilesByTag(foundTag.category, tagKey, tagText);
                window.tagApp.updateGalleryTitle(`${foundTag.categoryName}标签: ${tagText}`);

                // 5. 高亮源图片
                setTimeout(() => {
                    highlightSourceImage();
                }, 500);

                window.tagApp.showNotification(`已跳转到${foundTag.categoryName}标签: ${tagText}`, 'success');
            } else {
                console.warn('展开后仍未找到标签元素:', tagKey);
                // 降级处理：直接筛选文件
                window.tagApp.filterFilesByTag(foundTag.category, foundTag.actualKey || tagKey, tagText);
                window.tagApp.updateGalleryTitle(`${foundTag.categoryName}标签: ${tagText}`);
                window.tagApp.showNotification(`已筛选${foundTag.categoryName}标签: ${tagText}`, 'success');
            }
        }, 200);
    } else {
        console.warn('全局查找未找到标签:', tagKey, tagText);
        // 降级处理：尝试直接筛选
        window.tagApp.filterFilesByTag('properties', tagKey, tagText);
        window.tagApp.updateGalleryTitle(`属性标签: ${tagText}`);
        window.tagApp.showNotification(`已筛选标签: ${tagText}（可能不在当前视图中）`, 'warning');
    }
}

// 处理质量标签点击
function handleQualityTagClick(tagKey, tagText) {
    console.log('⭐ 处理质量标签点击:', { tagKey, tagText });

    if (!window.tagApp) return;

    // 使用全局查找逻辑
    const foundTag = findTagGlobally(tagKey, tagText);

    if (foundTag) {
        console.log('🎯 全局查找成功:', foundTag);

        // 展开对应的分类
        expandTagCategory(foundTag.category);

        // 等待展开完成后查找并高亮标签
        setTimeout(() => {
            const tagElement = document.querySelector(foundTag.selector);
            if (tagElement) {
                // 清除其他选择
                document.querySelectorAll('.tag-item.selected, .custom-tag-item.selected').forEach(item => {
                    item.classList.remove('selected');
                });

                // 高亮当前标签
                tagElement.classList.add('selected');

                // 滚动到标签位置
                setTimeout(() => {
                    tagElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);

                // 筛选显示该标签的所有文件
                window.tagApp.filterFilesByTag(foundTag.category, foundTag.actualKey || tagKey, tagText);
                window.tagApp.updateGalleryTitle(`${foundTag.categoryName}标签: ${tagText}`);

                // 高亮源图片
                setTimeout(() => {
                    highlightSourceImage();
                }, 500);

                window.tagApp.showNotification(`已跳转到${foundTag.categoryName}标签: ${tagText}`, 'success');
            } else {
                console.warn('展开后仍未找到标签元素:', tagKey);
                // 降级处理：直接筛选文件
                window.tagApp.filterFilesByTag(foundTag.category, foundTag.actualKey || tagKey, tagText);
                window.tagApp.updateGalleryTitle(`${foundTag.categoryName}标签: ${tagText}`);
                window.tagApp.showNotification(`已筛选${foundTag.categoryName}标签: ${tagText}`, 'success');
            }
        }, 200);
    } else {
        console.warn('全局查找未找到标签:', tagKey, tagText);
        // 降级处理：尝试直接筛选
        window.tagApp.filterFilesByTag('properties', tagKey, tagText);
        window.tagApp.updateGalleryTitle(`质量标签: ${tagText}`);
        window.tagApp.showNotification(`已筛选标签: ${tagText}（可能不在当前视图中）`, 'warning');
    }
}

// 关闭图片模态框
function closeImageModal() {
    const modal = document.getElementById('image-modal');
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = '';
    }
}

// 高亮源图片
function highlightSourceImage() {
    if (!window.tagApp || !window.tagApp.sourceFileId) return;

    const sourceFileId = window.tagApp.sourceFileId;
    const galleryItem = document.querySelector(`[data-file-id="${sourceFileId}"]`);

    if (galleryItem) {
        // 移除其他高亮
        document.querySelectorAll('.gallery-item.source-highlight').forEach(item => {
            item.classList.remove('source-highlight');
        });

        // 添加高亮效果
        galleryItem.classList.add('source-highlight');

        // 滚动到图片位置
        galleryItem.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 3秒后移除高亮
        setTimeout(() => {
            galleryItem.classList.remove('source-highlight');
        }, 3000);
    }
}

// 调试函数：显示标签树结构
function debugTagTree() {
    if (!window.tagApp || !window.tagApp.tagTree) {
        console.log('❌ 标签树数据不可用');
        return;
    }

    const tagTree = window.tagApp.tagTree;
    console.log('🌳 标签树结构调试信息:');
    console.log('📊 元数据标签:', tagTree.tags?.metadata || {});
    console.log('⭐ 质量标签:', tagTree.properties || {});
    console.log('🤖 CV标签:', tagTree.tags?.cv || {});
    console.log('👤 用户标签:', tagTree.tags?.user || {});
    console.log('🧠 AI标签:', tagTree.tags?.ai || {});
    console.log('🏷️ 自定义标签:', tagTree.custom || []);

    // 统计信息
    const stats = {
        metadata: Object.keys(tagTree.tags?.metadata || {}).length,
        properties: Object.keys(tagTree.properties || {}).length,
        cv: Object.keys(tagTree.tags?.cv || {}).length,
        user: Object.keys(tagTree.tags?.user || {}).length,
        ai: Object.keys(tagTree.tags?.ai || {}).length,
        custom: (tagTree.custom || []).length
    };
    console.log('📈 标签统计:', stats);
}

// 调试函数：测试标签查找
function debugTagSearch(tagKey, tagText) {
    console.log('🔍 调试标签查找:', { tagKey, tagText });
    debugTagTree();
    const result = findTagGlobally(tagKey, tagText);
    console.log('🎯 查找结果:', result);
    return result;
}

// 将调试函数暴露到全局
window.debugTagTree = debugTagTree;
window.debugTagSearch = debugTagSearch;

// 全局查找标签函数
function findTagGlobally(tagKey, tagText) {
    console.log('🔍 开始全局查找标签:', { tagKey, tagText });

    if (!window.tagApp || !window.tagApp.tagTree) {
        console.warn('标签树数据不可用');
        return null;
    }

    const tagTree = window.tagApp.tagTree;
    console.log('🌳 当前标签树结构:', tagTree);
    console.log('🌳 当前标签树结构:', tagTree);

    // 定义搜索范围和对应的分类信息
    // 适配后端返回的标签树结构：{ ai_tags: {}, user_tags: {}, properties: {} }
    const searchCategories = [
        {
            category: 'metadata',
            categoryName: '元数据',
            data: tagTree.ai_tags || {}, // metadata标签现在在ai_tags中
            selectorTemplate: (key) => `[data-tag-name="${key}"][data-category="metadata"]`
        },
        {
            category: 'properties',
            categoryName: '质量',
            data: tagTree.properties || {},
            selectorTemplate: (key) => `[data-tag-name="${key}"][data-category="properties"]`
        },
        {
            category: 'cv',
            categoryName: 'CV',
            data: tagTree.ai_tags || {}, // cv标签也在ai_tags中
            selectorTemplate: (key) => `[data-tag-name="${key}"][data-category="cv"]`
        },
        {
            category: 'user',
            categoryName: '用户',
            data: tagTree.user_tags || {},
            selectorTemplate: (key) => `[data-tag-name="${key}"][data-category="user"]`
        },
        {
            category: 'ai',
            categoryName: 'AI',
            data: tagTree.ai_tags || {},
            selectorTemplate: (key) => `[data-tag-name="${key}"][data-category="ai"]`
        }
    ];

    // 在每个分类中搜索
    for (const searchCategory of searchCategories) {
        const categoryData = searchCategory.data;

        // 搜索方式1: 直接匹配标签键
        if (categoryData[tagKey]) {
            console.log(`✅ 在${searchCategory.categoryName}分类中找到标签:`, tagKey);
            // 对于用户标签和AI标签，使用简洁格式的选择器
            let selector;
            if (searchCategory.category === 'user' || searchCategory.category === 'ai') {
                selector = `[data-tag-value="${tagKey}"][data-category="${searchCategory.category}"]`;
            } else {
                // 对于metadata、cv、properties标签，需要找到匹配的值
                const tagData = categoryData[tagKey];
                if (typeof tagData === 'object' && tagData !== null) {
                    // 查找匹配的值
                    for (const [value, info] of Object.entries(tagData)) {
                        if (value === tagText) {
                            selector = `[data-tag-value="${value}"][data-category="${searchCategory.category}"]`;
                            break;
                        }
                    }
                }
                if (!selector) {
                    selector = searchCategory.selectorTemplate(tagKey);
                }
            }
            return {
                category: searchCategory.category,
                categoryName: searchCategory.categoryName,
                selector: selector,
                found: true,
                matchType: 'key',
                actualKey: tagKey
            };
        }

        // 搜索方式2: 遍历所有标签值，匹配标签文本（支持简洁格式）
        for (const [key, valueData] of Object.entries(categoryData)) {
            // 对于用户标签和AI标签，直接检查键名匹配
            if ((searchCategory.category === 'user' || searchCategory.category === 'ai') &&
                (key === tagText || key === tagKey)) {
                console.log(`✅ 在${searchCategory.categoryName}分类中直接匹配找到标签:`, key);
                const simpleSelector = `[data-tag-value="${key}"][data-category="${searchCategory.category}"]`;
                return {
                    category: searchCategory.category,
                    categoryName: searchCategory.categoryName,
                    selector: simpleSelector,
                    found: true,
                    matchType: 'direct',
                    actualKey: key,
                    actualValue: key
                };
            }

            if (typeof valueData === 'object' && valueData !== null) {
                // 检查是否有匹配的值（用于metadata、cv、properties标签）
                for (const [value, info] of Object.entries(valueData)) {
                    if (value === tagText || value === tagKey) {
                        console.log(`✅ 在${searchCategory.categoryName}分类中通过值匹配找到标签:`, { key, value });
                        // 使用简洁格式的选择器：直接匹配标签值而不是键名
                        const simpleSelector = `[data-tag-value="${value}"][data-category="${searchCategory.category}"]`;
                        return {
                            category: searchCategory.category,
                            categoryName: searchCategory.categoryName,
                            selector: simpleSelector,
                            found: true,
                            matchType: 'value',
                            actualKey: key,
                            actualValue: value
                        };
                    }
                }
            }
        }
    }

    // 搜索自定义标签
    if (tagTree.custom && Array.isArray(tagTree.custom)) {
        for (const customTag of tagTree.custom) {
            if (customTag.name === tagText || customTag.id.toString() === tagKey) {
                console.log('✅ 在自定义标签中找到:', customTag);
                return {
                    category: 'custom',
                    categoryName: '自定义',
                    selector: `[data-tag-id="${customTag.id}"].custom-tag-item`,
                    found: true,
                    matchType: 'custom',
                    customTag: customTag
                };
            }
        }
    }

    console.warn('❌ 全局查找未找到标签:', { tagKey, tagText });
    return null;
}

// 展开标签分类函数
function expandTagCategory(category) {
    console.log('🔓 展开标签分类:', category);

    const categoryMappings = {
        'metadata': 'metadata',
        'properties': 'properties',
        'cv': 'cv',
        'user': 'user',
        'ai': 'ai',
        'custom': 'custom'
    };

    const targetCategory = categoryMappings[category] || category;
    const categoryHeader = document.querySelector(`[data-category="${targetCategory}"]`);
    const categoryContent = document.getElementById(`${targetCategory}-tags`);

    if (categoryHeader && categoryContent) {
        if (categoryContent.classList.contains('hidden')) {
            categoryContent.classList.remove('hidden');
            categoryHeader.classList.add('expanded');
            console.log(`✅ 已展开${targetCategory}分类`);
        } else {
            console.log(`ℹ️ ${targetCategory}分类已经是展开状态`);
        }
    } else {
        console.warn(`❌ 未找到${targetCategory}分类的DOM元素`);
    }
}

// 自动跳转到目标标签
function autoJumpToTargetTag() {
    if (!window.tagApp || !window.tagApp.targetTagType || !window.tagApp.targetTagKey) return;

    const tagType = window.tagApp.targetTagType;
    const tagKey = window.tagApp.targetTagKey;
    const tagText = window.tagApp.targetTagText;
    const tagId = window.tagApp.targetTagId;

    console.log('🎯 自动跳转到目标标签:', { tagType, tagKey, tagText, tagId });

    // 根据标签类型调用相应的处理函数
    switch (tagType) {
        case 'metadata':
        case 'cv':
            handleMetadataTagClick(tagKey, tagText);
            break;
        case 'custom':
            // 自定义标签跳转
            if (tagId) {
                handleCustomTagClick(tagId, tagText);
            } else {
                console.warn('自定义标签缺少标签ID');
            }
            break;
        case 'user':
        case 'ai':
            // 使用全局查找处理用户和AI标签
            handleMetadataTagClick(tagKey, tagText);
            break;
        default:
            console.warn('未知的标签类型:', tagType);
    }

    // 显示跳转成功的通知
    if (window.tagApp.showNotification) {
        window.tagApp.showNotification(`已跳转到${tagText}标签`, 'success');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.tagApp = new TagManagementApp();
});

// 在文件末尾添加初始化代码
document.addEventListener('DOMContentLoaded', function() {
    // 创建全局标签管理实例
    window.tagApp = new TagManagementApp();
    
    // 添加桥接函数，使TagManagementApp与UnifiedArchitecture兼容
    if (window.tagApp) {
        // 添加数据同步方法
        window.tagApp.syncWithUnifiedArchitecture = function(unifiedData) {
            if (!unifiedData || !unifiedData.data) return false;
            
            console.log('🔄 正在同步统一架构数据...');
            
            try {
                // 同步基本信息
                if (unifiedData.data.basicInfo) {
                    this.currentCase = unifiedData.data.basicInfo;
                }
                
                // 同步标签树
                if (unifiedData.data.tagTree) {
                    this.tagTree = unifiedData.data.tagTree;
                }
                
                // 同步文件列表
                if (Array.isArray(unifiedData.data.files)) {
                    this.currentFiles = unifiedData.data.files;
                }
                
                // 更新UI
                this.updateCaseInfo();
                this.updateTagStats({});
                this.renderTagTree();
                this.renderGallery();
                this.updateFileCount();
                
                console.log('✅ 数据同步完成');
                return true;
            } catch (error) {
                console.error('❌ 数据同步失败:', error);
                return false;
            }
        };
        
        // 添加统一架构回调
        window.onUnifiedArchitectureUpdate = function(state) {
            if (window.tagApp && window.tagApp.syncWithUnifiedArchitecture) {
                window.tagApp.syncWithUnifiedArchitecture(state);
            }
        };
    }
});

// 自动激活统一架构
setTimeout(function() {
    try {
        // 初始化统一架构
        if (window.initUnifiedArchitecture) {
            console.log('🚀 自动激活统一数据流架构...');
            window.initUnifiedArchitecture();
        }
    } catch (error) {
        console.warn('⚠️ 统一架构激活失败，回退到标准模式:', error);
    }
}, 1000);