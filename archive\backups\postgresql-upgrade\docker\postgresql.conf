# PostgreSQL 15 优化配置 for Mizzy Star
# 针对SSD存储和JSONB查询优化

#------------------------------------------------------------------------------
# CONNECTIONS AND AUTHENTICATION
#------------------------------------------------------------------------------

# 连接配置
listen_addresses = '*'                    # 允许所有IP连接 (Docker环境需要)
max_connections = 200
superuser_reserved_connections = 3

#------------------------------------------------------------------------------
# RESOURCE USAGE (except WAL)
#------------------------------------------------------------------------------

# 内存配置 (假设4GB RAM)
shared_buffers = 256MB                    # 25% of RAM
effective_cache_size = 1GB                # 75% of RAM  
work_mem = 4MB                           # 查询工作内存
maintenance_work_mem = 64MB              # 维护操作内存
autovacuum_work_mem = -1                 # 使用maintenance_work_mem

# 后台进程
max_worker_processes = 8
max_parallel_workers_per_gather = 2
max_parallel_workers = 8
max_parallel_maintenance_workers = 2

#------------------------------------------------------------------------------
# WRITE AHEAD LOG
#------------------------------------------------------------------------------

# WAL配置
wal_buffers = 16MB                       # WAL缓冲区
checkpoint_completion_target = 0.9       # 检查点优化
max_wal_size = 1GB
min_wal_size = 80MB

#------------------------------------------------------------------------------
# QUERY TUNING
#------------------------------------------------------------------------------

# 查询优化
random_page_cost = 1.1                   # SSD优化 (默认4.0)
effective_io_concurrency = 200           # 并发IO优化 (SSD)
seq_page_cost = 1.0                      # 顺序扫描成本

# 查询计划
default_statistics_target = 100          # 统计信息目标
constraint_exclusion = partition         # 分区约束排除

#------------------------------------------------------------------------------
# REPORTING AND LOGGING
#------------------------------------------------------------------------------

# 日志配置
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_file_mode = 0600
log_rotation_age = 1d
log_rotation_size = 10MB

# 日志内容
log_min_duration_statement = 100         # 记录慢查询 (100ms)
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 0

# 错误报告
log_min_messages = warning
log_min_error_statement = error

#------------------------------------------------------------------------------
# RUNTIME STATISTICS
#------------------------------------------------------------------------------

# 统计信息
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all

#------------------------------------------------------------------------------
# AUTOVACUUM PARAMETERS
#------------------------------------------------------------------------------

# 自动清理
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1

#------------------------------------------------------------------------------
# CLIENT CONNECTION DEFAULTS
#------------------------------------------------------------------------------

# 客户端连接默认值
search_path = '"$user", public'
default_tablespace = ''
temp_tablespaces = ''

# 语言和格式
datestyle = 'iso, mdy'
timezone = 'UTC'
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

#------------------------------------------------------------------------------
# SHARED LIBRARY PRELOADING
#------------------------------------------------------------------------------

# 预加载库
shared_preload_libraries = 'pg_stat_statements'

#------------------------------------------------------------------------------
# CUSTOMIZED OPTIONS FOR JSONB AND GIN INDEXES
#------------------------------------------------------------------------------

# JSONB和GIN索引优化
gin_pending_list_limit = 4MB             # GIN索引优化
gin_fuzzy_search_limit = 0               # 禁用模糊搜索限制

#------------------------------------------------------------------------------
# LOCK MANAGEMENT
#------------------------------------------------------------------------------

# 锁管理
deadlock_timeout = 1s
max_locks_per_transaction = 64
max_pred_locks_per_transaction = 64

#------------------------------------------------------------------------------
# VERSION/PLATFORM COMPATIBILITY
#------------------------------------------------------------------------------

# 兼容性
array_nulls = on
backslash_quote = safe_encoding
escape_string_warning = on
lo_compat_privileges = off
quote_all_identifiers = off
standard_conforming_strings = on
synchronize_seqscans = on
