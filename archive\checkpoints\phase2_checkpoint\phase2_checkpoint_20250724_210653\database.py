# src/database.py - PostgreSQL单一数据库架构
import os
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.orm import declarative_base
from typing import Optional, Union, Any
import logging

logger = logging.getLogger(__name__)

# --- Configuration ---
PROJECT_ROOT = Path(__file__).resolve().parents[2]  # 指向 mizzy_star_v0.3
DATA_DIR = PROJECT_ROOT / "data"
TRASH_DIR = DATA_DIR / "trash"  # 回收站目录

# 使用新的数据库配置系统
from .database_config import db_config

# Ensure data directory exists
DATA_DIR.mkdir(exist_ok=True)
TRASH_DIR.mkdir(exist_ok=True)

# --- Base Model ---
# 导入models模块以确保所有模型都被注册
from . import models
# 使用models.py中的Base
Base = models.Base

def create_case_directory(case_id: Union[int, Any]) -> Path:
    """
    创建案例专用目录结构
    返回案例目录路径
    """
    case_dir = DATA_DIR / f"case_{case_id}"
    case_dir.mkdir(exist_ok=True)

    # 创建子目录：uploads（原始文件）和thumbnails（缩略图）
    uploads_dir = case_dir / "uploads"
    thumbnails_dir = case_dir / "thumbnails"
    uploads_dir.mkdir(exist_ok=True)
    thumbnails_dir.mkdir(exist_ok=True)

    logger.info(f"创建案例目录: {case_dir}")
    return case_dir

# --- PostgreSQL单一数据库设置 ---
DATABASE_URL = db_config.get_master_database_url()

# PostgreSQL连接参数
engine = create_engine(
    DATABASE_URL,
    pool_size=db_config.pool_size,
    max_overflow=db_config.max_overflow,
    pool_timeout=db_config.pool_timeout,
    pool_recycle=db_config.pool_recycle,
    pool_pre_ping=True,
    echo=db_config.echo_sql
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_master_db():
    """获取数据库会话的依赖函数"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 为了兼容性，保留这个别名
get_db = get_master_db

def create_independent_db_session():
    """
    创建独立的数据库会话，用于后台任务
    这个会话不依赖于FastAPI的依赖注入系统
    """
    return SessionLocal()

from contextlib import contextmanager

@contextmanager
def get_independent_db():
    """
    上下文管理器：为后台任务提供独立的数据库会话
    使用方式：
    with get_independent_db() as db:
        # 执行数据库操作
        pass
    """
    db = create_independent_db_session()
    try:
        yield db
        db.commit()  # 自动提交事务
    except Exception as e:
        db.rollback()  # 出错时回滚
        logger.error(f"数据库操作失败，已回滚: {e}")
        raise
    finally:
        db.close()

# --- 数据库初始化函数 ---
def create_tables():
    """创建所有数据库表"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("✅ 数据库表创建成功")

        # 创建pgvector扩展
        try:
            with engine.connect() as conn:
                conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))
                conn.commit()
            logger.info("✅ pgvector扩展已启用")
        except Exception as e:
            logger.warning(f"⚠️ pgvector扩展创建失败: {e}")

    except Exception as e:
        logger.error(f"❌ 数据库表创建失败: {e}")
        raise

def move_case_to_trash(case_id: int) -> bool:
    """
    将案例文件夹移动到回收站目录
    PostgreSQL架构下只需要移动文件，数据库记录通过软删除处理

    Args:
        case_id: 案例ID

    Returns:
        bool: 是否成功移动到回收站
    """
    # 获取案例目录路径
    case_dir = DATA_DIR / f"case_{case_id}"
    if not case_dir.exists():
        logger.warning(f"案例目录不存在: {case_dir}")
        return False

    # 生成回收站中的目录路径
    trash_case_dir = TRASH_DIR / f"case_{case_id}"

    # 移动整个案例文件夹到回收站
    try:
        import shutil

        # 如果回收站中已存在同名目录，先删除
        if trash_case_dir.exists():
            logger.warning(f"回收站中已存在案例目录，将被覆盖: {trash_case_dir}")
            shutil.rmtree(trash_case_dir)

        # 移动整个案例文件夹
        shutil.move(str(case_dir), str(trash_case_dir))

        logger.info(f"✅ 案例文件夹已移动到回收站: {case_dir} -> {trash_case_dir}")
        return True

    except Exception as e:
        logger.error(f"❌ 无法移动案例文件夹到回收站 {case_dir} -> {trash_case_dir}: {e}")
        return False

def restore_case_from_trash(case_id: int) -> bool:
    """
    从回收站恢复案例文件夹
    PostgreSQL架构下只需要移动文件，数据库记录通过软删除恢复

    Args:
        case_id: 案例ID

    Returns:
        bool: 是否成功从回收站恢复
    """
    # 获取回收站中的案例目录路径
    trash_case_dir = TRASH_DIR / f"case_{case_id}"
    if not trash_case_dir.exists():
        logger.warning(f"回收站中的案例目录不存在: {trash_case_dir}")
        return False

    # 生成恢复后的目录路径
    restored_case_dir = DATA_DIR / f"case_{case_id}"

    # 从回收站移动整个案例文件夹回数据目录
    try:
        import shutil

        # 如果数据目录中已存在同名目录，先删除
        if restored_case_dir.exists():
            logger.warning(f"数据目录中已存在案例目录，将被覆盖: {restored_case_dir}")
            shutil.rmtree(restored_case_dir)

        # 移动整个案例文件夹
        shutil.move(str(trash_case_dir), str(restored_case_dir))

        logger.info(f"✅ 案例文件夹已从回收站恢复: {trash_case_dir} -> {restored_case_dir}")
        return True

    except Exception as e:
        logger.error(f"❌ 无法从回收站恢复案例文件夹 {trash_case_dir} -> {restored_case_dir}: {e}")
        return False

