/* 标签即画廊系统样式 */

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 顶部导航栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn-back {
    padding: 0.5rem 1rem;
    background: #6366f1;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-back:hover {
    background: #4f46e5;
    transform: translateY(-2px);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.header-right {
    display: flex;
    gap: 1rem;
}

.btn-export, .btn-settings {
    padding: 0.5rem 1rem;
    background: #10b981;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-export:hover, .btn-settings:hover {
    background: #059669;
    transform: translateY(-2px);
}

/* 主内容区域 */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 左侧搜索面板 */
.search-panel {
    width: 350px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    overflow-y: auto;
    padding: 1.5rem;
}

.search-section h3, .processing-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 快速搜索 */
.quick-search {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.search-input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #6366f1;
}

.search-btn {
    padding: 0.75rem 1rem;
    background: #6366f1;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #4f46e5;
}

/* 高级搜索 */
.advanced-search h4 {
    font-size: 1rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 1rem;
}

.filter-group {
    margin-bottom: 1.5rem;
}

.filter-group label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.tag-input-container {
    position: relative;
}

.tag-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.tag-chip {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: #6366f1;
    color: white;
    border-radius: 16px;
    font-size: 0.8rem;
}

.tag-chip .remove {
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.tag-chip .remove:hover {
    opacity: 1;
}

/* 自定义标签容器 */
.custom-tags-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.custom-tag-input {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.custom-tag-input input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 0.85rem;
}

.btn-remove-custom-tag {
    padding: 0.5rem;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-remove-custom-tag:hover {
    background: #dc2626;
}

.btn-add-custom-tag {
    padding: 0.5rem 1rem;
    background: #10b981;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-add-custom-tag:hover {
    background: #059669;
}

/* 质量范围滑块 */
.quality-range {
    position: relative;
}

.range-input {
    width: 100%;
    margin-bottom: 0.5rem;
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    outline: none;
}

.range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #6366f1;
    border-radius: 50%;
    cursor: pointer;
}

.range-input::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #6366f1;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #6b7280;
}

/* 日期范围 */
.date-range {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.date-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 0.85rem;
}

/* 搜索操作按钮 */
.search-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-search, .btn-clear {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-search {
    background: #6366f1;
    color: white;
}

.btn-search:hover {
    background: #4f46e5;
}

.btn-clear {
    background: #f3f4f6;
    color: #374151;
}

.btn-clear:hover {
    background: #e5e7eb;
}

/* 搜索建议 */
.search-suggestions {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.suggestion-item {
    padding: 0.5rem;
    background: #f9fafb;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 0.85rem;
}

.suggestion-item:hover {
    background: #f3f4f6;
}

/* 处理工具 */
.processing-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.processor-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.processor-btn {
    padding: 0.75rem;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    text-align: left;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.processor-btn:hover {
    background: #f3f4f6;
    border-color: #6366f1;
}

.processor-btn.active {
    background: #6366f1;
    color: white;
    border-color: #6366f1;
}

.btn-batch-process {
    width: 100%;
    padding: 0.75rem;
    background: #f59e0b;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-batch-process:hover:not(:disabled) {
    background: #d97706;
}

.btn-batch-process:disabled {
    background: #d1d5db;
    cursor: not-allowed;
}

/* 右侧画廊区域 */
.gallery-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

/* 搜索结果信息 */
.search-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.result-count {
    font-weight: 600;
    color: #1f2937;
}

.search-time {
    font-size: 0.9rem;
    color: #6b7280;
}

.view-controls {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    padding: 0.5rem;
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active, .view-btn:hover {
    background: #6366f1;
    color: white;
    border-color: #6366f1;
}

/* 当前筛选条件 */
.active-filters {
    padding: 1rem 2rem;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.active-filters h4 {
    font-size: 0.9rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: #6366f1;
    color: white;
    border-radius: 16px;
    font-size: 0.8rem;
}

.filter-tag .remove {
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.filter-tag .remove:hover {
    opacity: 1;
}

/* 文件画廊 */
.file-gallery {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* 加载和空状态 */
.loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state .empty-icon {
    font-size: 4rem;
    color: #d1d5db;
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: #374151;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #6b7280;
}

/* 文件网格 */
.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.file-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.file-card.selected {
    border: 2px solid #6366f1;
}

.file-thumbnail {
    width: 100%;
    height: 150px;
    object-fit: cover;
    background: #f3f4f6;
}

.file-info {
    padding: 1rem;
}

.file-name {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    line-height: 1.3;
}

.file-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.file-tag {
    padding: 0.125rem 0.375rem;
    background: #e0e7ff;
    color: #3730a3;
    border-radius: 12px;
    font-size: 0.7rem;
}

.file-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #6b7280;
}

.quality-score {
    font-weight: 500;
}

.quality-high { color: #10b981; }
.quality-medium { color: #f59e0b; }
.quality-low { color: #ef4444; }

/* 分页控制 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.page-btn {
    padding: 0.5rem 1rem;
    background: #6366f1;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
    background: #4f46e5;
}

.page-btn:disabled {
    background: #d1d5db;
    cursor: not-allowed;
}

.page-info {
    font-weight: 500;
    color: #374151;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #6b7280;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: #374151;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

/* 导出选项 */
.export-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.option-group label {
    font-weight: 500;
    color: #374151;
}

.select-input, .text-input {
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
}

.btn-browse {
    padding: 0.5rem 1rem;
    background: #6366f1;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.btn-cancel, .btn-primary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #f3f4f6;
    color: #374151;
}

.btn-cancel:hover {
    background: #e5e7eb;
}

.btn-primary {
    background: #6366f1;
    color: white;
}

.btn-primary:hover {
    background: #4f46e5;
}

/* 进度条 */
.progress-info {
    text-align: center;
}

.progress-text {
    font-weight: 500;
    color: #374151;
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: #6366f1;
    transition: width 0.3s ease;
}

.progress-stats {
    font-size: 0.9rem;
    color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-panel {
        width: 100%;
        position: fixed;
        top: 0;
        left: -100%;
        height: 100vh;
        z-index: 100;
        transition: left 0.3s ease;
    }
    
    .search-panel.open {
        left: 0;
    }
    
    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .header {
        padding: 1rem;
    }
    
    .file-gallery {
        padding: 1rem;
    }
}
