// API 配置
const API_BASE_URL = 'http://localhost:8000';  // 统一使用8000端口

// API 客户端类
class ApiClient {
    constructor() {
        this.baseURL = API_BASE_URL;
        this.client = axios.create({
            baseURL: this.baseURL,
            timeout: 30000,  // 增加超时时间
            headers: {
                'Content-Type': 'application/json',
            },
            // 添加更多配置以提高兼容性
            validateStatus: function (status) {
                return status >= 200 && status < 300; // 默认
            },
            maxRedirects: 5
        });

        // 请求拦截器
        this.client.interceptors.request.use(
            (config) => {
                console.log(`API请求: ${config.method?.toUpperCase()} ${config.url}`);
                return config;
            },
            (error) => {
                console.error('请求错误:', error);
                return Promise.reject(error);
            }
        );

        // 响应拦截器
        this.client.interceptors.response.use(
            (response) => {
                console.log(`API响应: ${response.status} ${response.config.url}`);
                return response;
            },
            (error) => {
                console.error('响应错误:', error);
                console.error('错误详情:', {
                    message: error.message,
                    code: error.code,
                    response: error.response ? {
                        status: error.response.status,
                        data: error.response.data
                    } : null,
                    request: error.request ? 'Request made but no response' : null
                });
                this.handleError(error);
                return Promise.reject(error);
            }
        );
    }

    // 错误处理
    handleError(error) {
        console.log('🔍 处理错误:', error);

        let message = '未知错误';

        try {
            if (error.response) {
                // 服务器返回错误状态码
                const { status, data } = error.response;
                message = `服务器错误 (${status})`;

                if (data && typeof data === 'object') {
                    if (data.detail) {
                        message = Array.isArray(data.detail) ? data.detail[0].msg || data.detail[0] : data.detail;
                    } else if (data.message) {
                        message = data.message;
                    }
                } else if (typeof data === 'string') {
                    message = data;
                }

                console.log('服务器错误:', { status, data, message });
            } else if (error.request) {
                // 网络错误
                message = '网络连接失败，请检查后端服务是否启动';
                console.log('网络错误:', error.request);
            } else if (error.message) {
                // 其他错误
                message = `请求失败: ${error.message}`;
                console.log('请求错误:', error.message);
            } else {
                message = '请求失败: 未知错误';
                console.log('未知错误:', error);
            }
        } catch (handleError) {
            console.error('错误处理失败:', handleError);
            message = '错误处理失败';
        }

        // 安全地显示通知
        try {
            if (typeof showNotification === 'function') {
                showNotification(message, 'error');
            } else {
                console.error('showNotification函数不可用:', message);
            }
        } catch (notificationError) {
            console.error('显示通知失败:', notificationError);
        }
    }

    // 连接测试
    async testConnection() {
        try {
            console.log('🔍 测试后端连接...');
            const response = await this.client.get('/api/v1/cases/', { timeout: 5000 });
            console.log('✅ 后端连接成功:', response.status);
            return { success: true, status: response.status };
        } catch (error) {
            console.error('❌ axios连接失败，尝试备用方法:', error);

            // 备用方法：使用原生fetch
            try {
                console.log('🔄 尝试使用fetch连接...');
                const fetchResponse = await fetch(`${this.baseURL}/api/v1/cases/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    timeout: 5000
                });

                if (fetchResponse.ok) {
                    console.log('✅ fetch连接成功:', fetchResponse.status);
                    return { success: true, status: fetchResponse.status, method: 'fetch' };
                } else {
                    throw new Error(`HTTP ${fetchResponse.status}: ${fetchResponse.statusText}`);
                }
            } catch (fetchError) {
                console.error('❌ fetch连接也失败:', fetchError);
                return {
                    success: false,
                    error: error.message || '连接失败',
                    fetchError: fetchError.message,
                    details: {
                        code: error.code,
                        response: error.response ? {
                            status: error.response.status,
                            statusText: error.response.statusText
                        } : null
                    }
                };
            }
        }
    }

    // 备用获取案例方法（使用fetch）
    async getCasesFallback() {
        try {
            console.log('🔄 使用fetch获取案例列表...');
            const response = await fetch(`${this.baseURL}/api/v1/cases/`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('✅ fetch获取案例成功:', data.length, '条记录');
            return data;
        } catch (error) {
            console.error('❌ fetch获取案例失败:', error);
            throw error;
        }
    }

    // 案例相关 API
    async getCases() {
        try {
            console.log('🔄 开始获取案例列表...');
            const response = await this.client.get('/api/v1/cases/');
            console.log('✅ 案例列表获取成功:', response.status, response.data?.length || 0, '条记录');
            return response.data;
        } catch (error) {
            console.error('❌ 获取案例列表失败:', error);
            console.error('错误详情:', {
                message: error.message,
                code: error.code,
                response: error.response ? {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    data: error.response.data
                } : null,
                request: error.request ? 'Request made but no response received' : null
            });
            throw error;
        }
    }

    async getCase(caseId) {
        try {
            const response = await this.client.get(`/api/v1/cases/${caseId}`);
            return response.data;
        } catch (error) {
            // 为404错误添加更具体的错误信息
            if (error.response && error.response.status === 404) {
                const errorMsg = `案例 ${caseId} 不存在 (404)`;
                console.error(errorMsg);
                throw new Error(errorMsg);
            }
            throw error;
        }
    }

    // 获取案例基本信息（不包含所有文件数据，用于性能优化）
    async getCaseBasicInfo(caseId) {
        try {
            const response = await this.client.get(`/api/v1/cases/${caseId}/basic`);
            return response.data;
        } catch (error) {
            // 如果基本信息API不存在，回退到完整API
            if (error.response && error.response.status === 404) {
                console.warn('基本信息API不存在，回退到完整案例API');
                return this.getCase(caseId);
            }
            throw error;
        }
    }

    async createCase(caseData) {
        try {
            const response = await this.client.post('/api/v1/cases/', caseData);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async updateCase(caseId, caseData) {
        try {
            const response = await this.client.put(`/api/v1/cases/${caseId}`, caseData);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async deleteCase(caseId) {
        try {
            const response = await this.client.delete(`/api/v1/cases/${caseId}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // 文件相关 API
    async getCaseFiles(caseId) {
        try {
            const response = await this.client.get(`/api/v1/cases/${caseId}/files/`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async uploadFile(caseId, file) {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await this.client.post(
                `/api/v1/cases/${caseId}/files/upload-and-copy`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                    onUploadProgress: (progressEvent) => {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        console.log(`上传进度: ${percentCompleted}%`);
                    }
                }
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async importLocalFile(caseId, filePath) {
        try {
            const formData = new FormData();
            formData.append('file_path', filePath);

            const response = await this.client.post(
                `/api/v1/cases/${caseId}/files/import-by-path`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    }
                }
            );

            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // 明确区分两种文件处理方式
    async uploadAndCopyFile(caseId, file) {
        /**
         * 上传文件并复制到案例目录
         * ⚠️ 警告：会复制文件，适用于需要复制的特殊场景（如封面设置）
         */
        return this.uploadFile(caseId, file);
    }

    async importByPath(caseId, filePath) {
        /**
         * 按路径导入本地文件，不复制文件
         * ✅ 推荐：用于文件导入，只记录路径，不占用额外存储空间
         */
        return this.importLocalFile(caseId, filePath);
    }

    async setCaseCover(caseId, file) {
        /**
         * 设置案例封面（需要复制文件以确保稳定性）
         * ⚠️ 这是唯一需要复制文件的正当场景
         */
        try {
            // 封面需要复制到案例目录以确保稳定存在
            const response = await this.uploadAndCopyFile(caseId, file);

            // 更新案例封面信息
            await this.client.patch(`/api/v1/cases/${caseId}`, {
                cover_file_id: response.id
            });

            return response;
        } catch (error) {
            throw error;
        }
    }

    async deleteFile(caseId, fileId) {
        try {
            const response = await this.client.delete(`/api/v1/cases/${caseId}/files/${fileId}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async downloadFile(caseId, fileId) {
        try {
            const response = await this.client.get(
                `/api/v1/cases/${caseId}/files/${fileId}/download`,
                { responseType: 'blob' }
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async viewFile(caseId, fileId) {
        try {
            const response = await this.client.get(
                `/api/v1/cases/${caseId}/files/${fileId}/view`,
                { responseType: 'blob' }
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    getFileViewUrl(caseId, fileId) {
        return `${this.baseURL}/api/v1/cases/${caseId}/files/${fileId}/view`;
    }

    getFileThumbnailUrl(caseId, fileId, size = 300) {
        return `${this.baseURL}/api/v1/cases/${caseId}/files/${fileId}/thumbnail?size=${size}`;
    }

    // 回收站相关 API
    async getTrashCases() {
        try {
            const response = await this.client.get('/api/v1/trash/');
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async restoreCase(caseId) {
        try {
            const response = await this.client.post(`/api/v1/trash/${caseId}/restore`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async permanentlyDeleteCase(caseId) {
        try {
            const response = await this.client.delete(`/api/v1/trash/${caseId}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async emptyTrash() {
        try {
            const response = await this.client.delete('/api/v1/trash/empty');
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // 封面管理相关 API

    /**
     * 手动设置案例封面
     * @param {number} caseId - 案例ID
     * @param {number} fileId - 要设置为封面的文件ID
     * @returns {Promise<Object>} 更新后的案例信息
     */
    async setCover(caseId, fileId) {
        try {
            const response = await this.client.put(`/api/v1/cases/${caseId}/cover`, {
                fileId: fileId
            });
            return response.data;
        } catch (error) {
            // 处理特定的封面设置错误
            if (error.response && error.response.status === 422) {
                const errorData = error.response.data;
                if (errorData.error === 'FILE_NOT_FOUND') {
                    throw new Error('该文件已被更名或删除');
                } else if (errorData.error === 'INVALID_FILE_TYPE') {
                    throw new Error('只能选择图片文件作为封面');
                }
            }
            throw error;
        }
    }

    /**
     * 移除手动封面选择，回退到自动选择
     * @param {number} caseId - 案例ID
     * @returns {Promise<Object>} 包含更新后案例信息和回退类型的对象
     */
    async removeCover(caseId) {
        try {
            const response = await this.client.delete(`/api/v1/cases/${caseId}/cover`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 手动触发封面重选
     * @param {number} caseId - 案例ID
     * @returns {Promise<Object>} 重选结果信息
     */
    async reselectCover(caseId) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/cover/reselect`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 获取封面状态信息
     * @param {Object} caseData - 案例数据
     * @returns {Object} 封面状态信息
     */
    getCoverStatus(caseData) {
        if (!caseData) return null;

        return {
            url: caseData.cover_image_url,
            type: caseData.cover_type,
            sourceFileId: caseData.cover_source_file_id,
            needsAttention: caseData.cover_needs_attention,
            updatedAt: caseData.cover_updated_at,
            isManual: caseData.cover_type === 'manual',
            isAutomatic: caseData.cover_type === 'automatic',
            isPlaceholder: caseData.cover_type === 'placeholder'
        };
    }

    /**
     * 检查文件是否可以设置为封面
     * @param {Object} file - 文件对象
     * @returns {boolean} 是否可以设置为封面
     */
    canSetAsCover(file) {
        return file && file.file_type && file.file_type.startsWith('image/');
    }

    // ==================== 标签系统 API ====================

    // 获取标签树结构
    async getTagTree(caseId, includeEmpty = false) {
        try {
            const response = await this.client.get(`/api/v1/cases/${caseId}/tags/tree?include_empty=${includeEmpty}`);
            return response.data;
        } catch (error) {
            console.error('获取标签树失败:', error);
            throw error;
        }
    }

    // 搜索标签
    async searchTags(caseId, query, category = null, limit = 50) {
        try {
            const params = new URLSearchParams({ q: query, limit: limit.toString() });
            if (category) params.append('category', category);

            const response = await this.client.get(`/api/v1/cases/${caseId}/tags/search?${params}`);
            return response.data;
        } catch (error) {
            console.error('搜索标签失败:', error);
            throw error;
        }
    }

    // 自定义标签相关 API
    async getCustomTags(caseId) {
        try {
            const response = await this.client.get(`/api/v1/cases/${caseId}/custom-tags`);
            return response.data;
        } catch (error) {
            console.error('获取自定义标签失败:', error);
            return [];
        }
    }

    async createCustomTag(caseId, tagData) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/custom-tags`, {
                name: tagData.name,
                color: tagData.color
            });
            return response.data;
        } catch (error) {
            console.error('创建自定义标签失败:', error);
            throw error;
        }
    }

    // 更新自定义标签
    async updateCustomTag(caseId, tagId, tagData) {
        try {
            const response = await this.client.put(`/api/v1/cases/${caseId}/custom-tags/${tagId}`, tagData);
            return response.data;
        } catch (error) {
            console.error('更新自定义标签失败:', error);
            throw error;
        }
    }

    // 删除自定义标签
    async deleteCustomTag(caseId, tagId) {
        try {
            const response = await this.client.delete(`/api/v1/cases/${caseId}/custom-tags/${tagId}`);
            return response.data;
        } catch (error) {
            console.error('删除自定义标签失败:', error);
            throw error;
        }
    }

    // 为文件添加自定义标签
    async addCustomTagToFile(caseId, fileId, tagId) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/files/${fileId}/custom-tags`, {
                tag_id: tagId
            });
            return response.data;
        } catch (error) {
            console.error('为文件添加自定义标签失败:', error);
            throw error;
        }
    }

    // 从文件移除自定义标签
    async removeCustomTagFromFile(caseId, fileId, tagId) {
        try {
            const response = await this.client.delete(`/api/v1/cases/${caseId}/files/${fileId}/custom-tags/${tagId}`);
            return response.data;
        } catch (error) {
            console.error('从文件移除自定义标签失败:', error);
            throw error;
        }
    }

    // 批量操作自定义标签
    async batchCustomTagOperation(caseId, operation) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/custom-tags/batch`, operation);
            return response.data;
        } catch (error) {
            console.error('批量操作自定义标签失败:', error);
            throw error;
        }
    }

    // 获取文件的自定义标签
    async getFileCustomTags(caseId, fileId) {
        try {
            const response = await this.client.get(`/api/v1/cases/${caseId}/files/${fileId}/custom-tags`);
            return response.data;
        } catch (error) {
            console.error('获取文件自定义标签失败:', error);
            return [];
        }
    }

    // 获取文件的所有标签（包括自定义标签）
    async getFileAllTags(caseId, fileId) {
        try {
            const response = await this.client.get(`/api/v1/cases/${caseId}/files/${fileId}/all-tags`);
            return response.data;
        } catch (error) {
            console.error('获取文件所有标签失败:', error);
            return {};
        }
    }

    // 批量标签操作
    async batchTagOperation(caseId, action, fileIds, tagName) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/custom-tags/${tagName}/batch`, {
                action: action,
                file_ids: fileIds
            });
            return response.data;
        } catch (error) {
            console.error('批量标签操作失败:', error);
            throw error;
        }
    }



    // 刷新标签缓存
    async refreshTagCache(caseId) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/tags/cache/refresh`);
            return response.data;
        } catch (error) {
            console.error('刷新标签缓存失败:', error);
            throw error;
        }
    }

    // 获取缓存统计
    async getCacheStats(caseId) {
        try {
            const response = await this.client.get(`/api/v1/cases/${caseId}/tags/cache/stats`);
            return response.data;
        } catch (error) {
            console.error('获取缓存统计失败:', error);
            throw error;
        }
    }

    // 清理空标签
    async cleanEmptyTags(caseId) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/tags/cache/clean`);
            return response.data;
        } catch (error) {
            console.error('清理空标签失败:', error);
            throw error;
        }
    }

    // 数据库迁移
    async migrateTagDatabase(caseId) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/tags/migrate`);
            return response.data;
        } catch (error) {
            console.error('数据库迁移失败:', error);
            throw error;
        }
    }

    // 检查数据库结构
    async checkTagDatabaseSchema(caseId) {
        try {
            const response = await this.client.get(`/api/v1/tags/${caseId}/tags/database/schema`);
            return response.data;
        } catch (error) {
            console.error('检查数据库结构失败:', error);
            throw error;
        }
    }

    // 获取支持的规则类型
    async getRuleTypes() {
        try {
            const response = await this.client.get('/api/v1/rule-types');
            return response.data;
        } catch (error) {
            console.error('获取规则类型失败:', error);
            throw error;
        }
    }

    // 验证规则配置
    async validateRuleConfig(ruleType, ruleConfig) {
        try {
            const response = await this.client.post('/api/v1/rules/validate-config', {
                rule_type: ruleType,
                rule_config: ruleConfig
            });
            return response.data;
        } catch (error) {
            console.error('验证规则配置失败:', error);
            throw error;
        }
    }

    // 创建案例处理规则
    async createRule(caseId, ruleData) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/rules`, ruleData);
            return response.data;
        } catch (error) {
            console.error('创建规则失败:', error);
            throw error;
        }
    }

    // 获取案例的所有规则
    async getCaseRules(caseId, options = {}) {
        try {
            const params = new URLSearchParams();
            if (options.isActive !== undefined) {
                params.append('isActive', options.isActive);
            }
            if (options.ruleType) {
                params.append('ruleType', options.ruleType);
            }

            const url = `/api/v1/cases/${caseId}/rules${params.toString() ? '?' + params.toString() : ''}`;
            const response = await this.client.get(url);
            return response.data;
        } catch (error) {
            console.error('获取案例规则失败:', error);
            throw error;
        }
    }

    // 获取单条规则详情
    async getRule(ruleId) {
        try {
            const response = await this.client.get(`/api/v1/rules/${ruleId}`);
            return response.data;
        } catch (error) {
            console.error('获取规则详情失败:', error);
            throw error;
        }
    }

    // 更新规则
    async updateRule(ruleId, updateData) {
        try {
            const response = await this.client.patch(`/api/v1/rules/${ruleId}`, updateData);
            return response.data;
        } catch (error) {
            console.error('更新规则失败:', error);
            throw error;
        }
    }

    // 删除规则
    async deleteRule(ruleId) {
        try {
            await this.client.delete(`/api/v1/rules/${ruleId}`);
            return true;
        } catch (error) {
            console.error('删除规则失败:', error);
            throw error;
        }
    }

    // 根据标签筛选文件
    async getFilesWithTags(caseId, tagFilters = {}, options = {}) {
        try {
            const params = new URLSearchParams();

            // 添加标签筛选参数
            Object.entries(tagFilters).forEach(([key, value]) => {
                if (value) {
                    params.append(`tag_${key}`, value);
                }
            });

            // 添加分页参数
            if (options.limit) {
                params.append('limit', options.limit);
            }
            if (options.offset) {
                params.append('offset', options.offset);
            }

            const url = `/api/v1/cases/${caseId}/files${params.toString() ? '?' + params.toString() : ''}`;
            const response = await this.client.get(url);
            return response.data;
        } catch (error) {
            console.error('根据标签筛选文件失败:', error);
            throw error;
        }
    }

    // ==================== 回收站相关API ====================

    // 获取回收站文件列表
    async getTrashFiles(caseId) {
        try {
            const response = await this.client.get(`/api/v1/cases/${caseId}/trash`);
            return response.data;
        } catch (error) {
            console.error('获取回收站文件失败:', error);
            throw error;
        }
    }

    // 恢复单个文件
    async restoreFile(caseId, fileId) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/trash/${fileId}/restore`);
            return response.data;
        } catch (error) {
            console.error('恢复文件失败:', error);
            throw error;
        }
    }

    // 永久删除单个文件
    async deletePermanently(caseId, fileId) {
        try {
            const response = await this.client.delete(`/api/v1/cases/${caseId}/trash/${fileId}`);
            return response.data;
        } catch (error) {
            console.error('永久删除文件失败:', error);
            throw error;
        }
    }

    // 恢复所有文件
    async restoreAllFiles(caseId) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/trash/restore-all`);
            return response.data;
        } catch (error) {
            console.error('恢复所有文件失败:', error);
            throw error;
        }
    }

    // 清空案例文件回收站
    async emptyCaseTrash(caseId) {
        // 验证案例ID
        if (!caseId || caseId === 'undefined') {
            throw new Error('案例ID无效，无法清空回收站');
        }

        try {
            const response = await this.client.delete(`/api/v1/cases/${caseId}/trash`);
            return response.data;
        } catch (error) {
            console.error('清空案例回收站失败:', error);
            throw error;
        }
    }

    // 重新处理文件元数据
    async reprocessFileMetadata(caseId, fileId) {
        try {
            const response = await this.client.post(`/api/v1/cases/${caseId}/files/${fileId}/reprocess`);
            return response.data;
        } catch (error) {
            console.error('重新处理文件元数据失败:', error);
            throw error;
        }
    }

}

// 创建全局 API 客户端实例
const api = new ApiClient();

// 工具函数
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type} p-4 rounded-lg shadow-lg max-w-sm`;

    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';

    notification.className += ` ${bgColor} text-white`;
    notification.textContent = message;

    const container = document.getElementById('notifications');
    container.appendChild(notification);

    // 自动移除通知
    setTimeout(() => {
        notification.classList.add('opacity-0', 'transform', 'translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取文件类型图标
function getFileIcon(filename) {
    // 处理 undefined 或 null 值
    if (!filename || typeof filename !== 'string') {
        return '📄'; // 默认文件图标
    }

    const ext = filename.split('.').pop().toLowerCase();
    const iconMap = {
        'pdf': '📄',
        'doc': '📝',
        'docx': '📝',
        'txt': '📄',
        'jpg': '🖼️',
        'jpeg': '🖼️',
        'png': '🖼️',
        'gif': '🖼️',
        'mp4': '🎬',
        'avi': '🎬',
        'mov': '🎬',
        'zip': '📦',
        'rar': '📦',
        'excel': '📊',
        'xlsx': '📊',
        'ppt': '📊',
        'pptx': '📊'
    };
    return iconMap[ext] || '📄';
}

// 导出 API 客户端和工具函数
window.api = api;
window.showNotification = showNotification;
window.formatDate = formatDate;
window.formatFileSize = formatFileSize;
window.getFileIcon = getFileIcon;