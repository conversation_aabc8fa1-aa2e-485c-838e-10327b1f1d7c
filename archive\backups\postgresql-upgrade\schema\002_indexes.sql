-- PostgreSQL索引定义 - 针对Mizzy Star查询模式优化
-- 基于现有查询模式和JSONB数据结构设计高性能索引

-- =============================================================================
-- 主数据库索引
-- =============================================================================

-- 系统配置表索引
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(config_key);
CREATE INDEX IF NOT EXISTS idx_system_config_updated_at ON system_config(updated_at);

-- 案例表索引
CREATE INDEX IF NOT EXISTS idx_cases_status ON cases(status);
CREATE INDEX IF NOT EXISTS idx_cases_created_at ON cases(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_cases_deleted_at ON cases(deleted_at) WHERE deleted_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_cases_case_name ON cases(case_name);
CREATE INDEX IF NOT EXISTS idx_cases_cover_type ON cases(cover_type);

-- 案例处理规则表索引
CREATE INDEX IF NOT EXISTS idx_case_rules_case_id ON case_processing_rules(case_id);
CREATE INDEX IF NOT EXISTS idx_case_rules_type ON case_processing_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_case_rules_active ON case_processing_rules(is_active);

-- =============================================================================
-- 文件表核心索引 - 性能关键
-- =============================================================================

-- 基础查询索引
CREATE INDEX IF NOT EXISTS idx_files_file_name ON files(file_name);
CREATE INDEX IF NOT EXISTS idx_files_file_type ON files(file_type);
CREATE INDEX IF NOT EXISTS idx_files_created_at ON files(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_files_taken_at ON files(taken_at DESC) WHERE taken_at IS NOT NULL;

-- 图像质量分析索引
CREATE INDEX IF NOT EXISTS idx_files_quality_score ON files(quality_score DESC) WHERE quality_score IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_files_num_faces ON files(num_faces) WHERE num_faces IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_files_cluster_id ON files(cluster_id) WHERE cluster_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_files_group_id ON files(group_id) WHERE group_id IS NOT NULL;

-- 感知哈希索引 (用于重复检测)
CREATE INDEX IF NOT EXISTS idx_files_phash ON files(phash) WHERE phash IS NOT NULL;

-- =============================================================================
-- JSONB标签索引 - 高性能标签查询的核心
-- =============================================================================

-- 主要JSONB GIN索引 - 支持所有JSONB查询
CREATE INDEX IF NOT EXISTS idx_files_tags_gin ON files USING GIN (tags);

-- 特定路径的GIN索引 - 优化常用查询路径
CREATE INDEX IF NOT EXISTS idx_files_tags_properties ON files USING GIN ((tags->'properties'));
CREATE INDEX IF NOT EXISTS idx_files_tags_metadata ON files USING GIN ((tags->'tags'->'metadata'));
CREATE INDEX IF NOT EXISTS idx_files_tags_cv ON files USING GIN ((tags->'tags'->'cv'));
CREATE INDEX IF NOT EXISTS idx_files_tags_user ON files USING GIN ((tags->'tags'->'user'));
CREATE INDEX IF NOT EXISTS idx_files_tags_ai ON files USING GIN ((tags->'tags'->'ai'));

-- 特定字段的表达式索引 - 优化精确查询
CREATE INDEX IF NOT EXISTS idx_files_quality_score_jsonb ON files (CAST(tags->'properties'->>'qualityScore' AS numeric))
    WHERE tags->'properties'->>'qualityScore' IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_files_camera_make ON files ((tags->'tags'->'metadata'->>'camera_make')) 
    WHERE tags->'tags'->'metadata'->>'camera_make' IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_files_camera_model ON files ((tags->'tags'->'metadata'->>'camera_model')) 
    WHERE tags->'tags'->'metadata'->>'camera_model' IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_files_file_type_jsonb ON files ((tags->'tags'->'metadata'->>'fileType')) 
    WHERE tags->'tags'->'metadata'->>'fileType' IS NOT NULL;

-- 数组字段索引 - 优化数组查询
CREATE INDEX IF NOT EXISTS idx_files_user_tags ON files USING GIN ((tags->'tags'->'user'));
CREATE INDEX IF NOT EXISTS idx_files_ai_tags ON files USING GIN ((tags->'tags'->'ai'));

-- =============================================================================
-- 全文搜索索引
-- =============================================================================

-- 全文搜索GIN索引
CREATE INDEX IF NOT EXISTS idx_files_search_vector ON files USING GIN (search_vector);

-- 文件名全文搜索索引
CREATE INDEX IF NOT EXISTS idx_files_file_name_fts ON files USING GIN (to_tsvector('english', file_name));

-- =============================================================================
-- 标签缓存表索引
-- =============================================================================

-- 标签缓存查询索引
CREATE INDEX IF NOT EXISTS idx_tag_cache_category ON tag_cache(tag_category);
CREATE INDEX IF NOT EXISTS idx_tag_cache_name ON tag_cache(tag_name);
CREATE INDEX IF NOT EXISTS idx_tag_cache_value ON tag_cache(tag_value);
CREATE INDEX IF NOT EXISTS idx_tag_cache_count ON tag_cache(file_count DESC);

-- 复合索引 - 优化标签搜索
CREATE INDEX IF NOT EXISTS idx_tag_cache_category_name ON tag_cache(tag_category, tag_name);
CREATE INDEX IF NOT EXISTS idx_tag_cache_category_value ON tag_cache(tag_category, tag_value);

-- 文件ID数组索引 - 支持数组查询
CREATE INDEX IF NOT EXISTS idx_tag_cache_file_ids ON tag_cache USING GIN (file_ids);

-- 更新时间索引
CREATE INDEX IF NOT EXISTS idx_tag_cache_updated_at ON tag_cache(updated_at DESC);

-- =============================================================================
-- 自定义标签索引
-- =============================================================================

-- 自定义标签索引
CREATE INDEX IF NOT EXISTS idx_custom_tags_name ON custom_tags(tag_name);
CREATE INDEX IF NOT EXISTS idx_custom_tags_order ON custom_tags(display_order);
CREATE INDEX IF NOT EXISTS idx_custom_tags_created_at ON custom_tags(created_at DESC);

-- 文件自定义标签关联索引
CREATE INDEX IF NOT EXISTS idx_file_custom_tags_file_id ON file_custom_tags(file_id);
CREATE INDEX IF NOT EXISTS idx_file_custom_tags_tag_id ON file_custom_tags(custom_tag_id);
CREATE INDEX IF NOT EXISTS idx_file_custom_tags_created_at ON file_custom_tags(created_at DESC);

-- =============================================================================
-- 删除文件索引
-- =============================================================================

-- 删除文件记录索引
CREATE INDEX IF NOT EXISTS idx_deleted_files_file_id ON deleted_files(file_id);
CREATE INDEX IF NOT EXISTS idx_deleted_files_deleted_at ON deleted_files(deleted_at DESC);
CREATE INDEX IF NOT EXISTS idx_deleted_files_permanent ON deleted_files(is_permanent);

-- =============================================================================
-- 复合索引 - 优化复杂查询
-- =============================================================================

-- 文件质量和时间复合索引
CREATE INDEX IF NOT EXISTS idx_files_quality_time ON files(quality_score DESC, created_at DESC) 
    WHERE quality_score IS NOT NULL;

-- 文件类型和创建时间复合索引
CREATE INDEX IF NOT EXISTS idx_files_type_time ON files(file_type, created_at DESC) 
    WHERE file_type IS NOT NULL;

-- 人脸数量和质量复合索引
CREATE INDEX IF NOT EXISTS idx_files_faces_quality ON files(num_faces DESC, face_quality DESC) 
    WHERE num_faces IS NOT NULL AND face_quality IS NOT NULL;

-- =============================================================================
-- 向量搜索索引 (预留)
-- =============================================================================

-- 向量相似度搜索索引 (当启用向量搜索时)
-- CREATE INDEX IF NOT EXISTS idx_files_embedding_vector ON files USING ivfflat (embedding_vector vector_cosine_ops);

-- =============================================================================
-- 索引统计信息
-- =============================================================================

-- 创建索引使用统计视图
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT
    schemaname,
    relname as tablename,
    indexrelname as indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 创建表大小统计视图
CREATE OR REPLACE VIEW table_size_stats AS
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY size_bytes DESC;

-- 索引创建完成日志
DO $$
BEGIN
    RAISE NOTICE 'PostgreSQL索引创建完成!';
    RAISE NOTICE '- 基础索引: 文件名、类型、时间等';
    RAISE NOTICE '- JSONB索引: GIN索引支持高效标签查询';
    RAISE NOTICE '- 全文搜索: 支持文件名和标签内容搜索';
    RAISE NOTICE '- 复合索引: 优化复杂查询场景';
    RAISE NOTICE '- 性能监控: 索引使用统计视图已创建';
END
$$;
