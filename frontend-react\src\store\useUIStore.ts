// Project Novak - Global UI State Store
// 严格的全局客户端状态管理，只管理UI状态，不管理服务器数据

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// ============================================================================
// UI State Interface
// ============================================================================

interface UIState {
  // ========================================
  // 面板显示状态
  // ========================================
  showCatalogPanel: boolean;
  showInfoPanel: boolean;
  showWorkbench: boolean;
  isFullscreenGallery: boolean;

  // ========================================
  // 面板尺寸
  // ========================================
  catalogPanelWidth: number;
  infoPanelWidth: number;
  workbenchHeight: number;

  // ========================================
  // 当前选中状态
  // ========================================
  selectedCaseId: number | null;
  selectedFileIds: number[];

  // ========================================
  // 搜索和筛选状态
  // ========================================
  searchQuery: string;
  activeFilters: Record<string, string>;

  // ========================================
  // 画廊视图状态
  // ========================================
  galleryLayout: 'grid' | 'list';
  galleryZoomLevel: number;
  gallerySortBy: 'name' | 'date' | 'size' | 'type';
  gallerySortOrder: 'asc' | 'desc';
  showFileName: boolean;
  showFileInfo: boolean;

  // 新UI画廊状态
  galleryViewMode: 'adaptive' | 'masonry' | 'grid' | 'list';
  galleryZoomLevelNew: number[];
  showImageInfo: boolean;

  // ========================================
  // 工作台状态
  // ========================================
  activeWorkbench: 'clipboard' | 'cluster' | null;
  clipboardFiles: Array<{
    id: number;
    position: { x: number; y: number };
  }>;

  // ========================================
  // 信息栏状态
  // ========================================
  activePanels: Array<'metadata' | 'rules' | 'cv' | 'ai'>;
}

// ============================================================================
// UI Actions Interface
// ============================================================================

interface UIActions {
  // ========================================
  // 面板控制
  // ========================================
  toggleCatalogPanel: () => void;
  toggleInfoPanel: () => void;
  toggleWorkbench: () => void;
  toggleFullscreenGallery: () => void;

  setCatalogPanelWidth: (width: number) => void;
  setInfoPanelWidth: (width: number) => void;
  setWorkbenchHeight: (height: number) => void;

  // ========================================
  // 选择控制
  // ========================================
  setSelectedCase: (caseId: number | null) => void;
  setSelectedFiles: (fileIds: number[]) => void;
  addSelectedFile: (fileId: number) => void;
  removeSelectedFile: (fileId: number) => void;
  toggleFileSelection: (fileId: number) => void;
  clearSelection: () => void;

  // ========================================
  // 搜索和筛选
  // ========================================
  setSearchQuery: (query: string) => void;
  setFilter: (key: string, value: string) => void;
  removeFilter: (key: string) => void;
  clearFilters: () => void;

  // ========================================
  // 画廊视图控制
  // ========================================
  setGalleryLayout: (layout: 'grid' | 'list') => void;
  setGalleryZoom: (level: number) => void;
  setGallerySort: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  toggleShowFileName: () => void;
  toggleShowFileInfo: () => void;

  // 新UI画廊控制
  setGalleryViewMode: (mode: 'adaptive' | 'masonry' | 'grid' | 'list') => void;
  setGalleryZoomNew: (level: number[]) => void;
  toggleShowImageInfo: () => void;

  // ========================================
  // 工作台控制
  // ========================================
  setActiveWorkbench: (workbench: 'clipboard' | 'cluster' | null) => void;
  addToClipboard: (fileId: number, position?: { x: number; y: number }) => void;
  removeFromClipboard: (fileId: number) => void;
  updateClipboardPosition: (fileId: number, position: { x: number; y: number }) => void;
  clearClipboard: () => void;

  // ========================================
  // 信息栏控制
  // ========================================
  toggleInfoPanelSection: (panel: 'metadata' | 'rules' | 'cv' | 'ai') => void;

  // ========================================
  // 重置和初始化
  // ========================================
  resetUI: () => void;
}

// ============================================================================
// Initial State
// ============================================================================

const initialState: UIState = {
  // 面板显示状态
  showCatalogPanel: true,
  showInfoPanel: true,
  showWorkbench: false,
  isFullscreenGallery: false,

  // 面板尺寸
  catalogPanelWidth: 280,
  infoPanelWidth: 320,
  workbenchHeight: 300,

  // 选中状态
  selectedCaseId: null,
  selectedFileIds: [],

  // 搜索筛选
  searchQuery: '',
  activeFilters: {},

  // 画廊视图
  galleryLayout: 'grid',
  galleryZoomLevel: 50,
  gallerySortBy: 'name',
  gallerySortOrder: 'asc',
  showFileName: true,
  showFileInfo: false,

  // 新UI画廊状态
  galleryViewMode: 'grid',
  galleryZoomLevelNew: [5],
  showImageInfo: true,

  // 工作台
  activeWorkbench: null,
  clipboardFiles: [],

  // 信息栏
  activePanels: ['metadata'],
};

// ============================================================================
// Zustand Store Implementation
// ============================================================================

export const useUIStore = create<UIState & UIActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // ========================================
      // 面板控制实现
      // ========================================
      toggleCatalogPanel: () =>
        set((state) => ({ showCatalogPanel: !state.showCatalogPanel })),

      toggleInfoPanel: () =>
        set((state) => ({ showInfoPanel: !state.showInfoPanel })),

      toggleWorkbench: () =>
        set((state) => ({
          showWorkbench: !state.showWorkbench,
          activeWorkbench: !state.showWorkbench ? 'clipboard' : null,
        })),

      toggleFullscreenGallery: () =>
        set((state) => ({ isFullscreenGallery: !state.isFullscreenGallery })),

      setCatalogPanelWidth: (width) => set({ catalogPanelWidth: width }),
      setInfoPanelWidth: (width) => set({ infoPanelWidth: width }),
      setWorkbenchHeight: (height) => set({ workbenchHeight: height }),

      // ========================================
      // 选择控制实现
      // ========================================
      setSelectedCase: (caseId) => set({ selectedCaseId: caseId }),

      setSelectedFiles: (fileIds) => set({ selectedFileIds: fileIds }),

      addSelectedFile: (fileId) =>
        set((state) => ({
          selectedFileIds: state.selectedFileIds.includes(fileId)
            ? state.selectedFileIds
            : [...state.selectedFileIds, fileId],
        })),

      removeSelectedFile: (fileId) =>
        set((state) => ({
          selectedFileIds: state.selectedFileIds.filter(id => id !== fileId),
        })),

      toggleFileSelection: (fileId) => {
        const { selectedFileIds } = get();
        if (selectedFileIds.includes(fileId)) {
          get().removeSelectedFile(fileId);
        } else {
          get().addSelectedFile(fileId);
        }
      },

      clearSelection: () => set({ selectedFileIds: [] }),

      // ========================================
      // 搜索筛选实现
      // ========================================
      setSearchQuery: (query) => set({ searchQuery: query }),

      setFilter: (key, value) =>
        set((state) => ({
          activeFilters: { ...state.activeFilters, [key]: value },
        })),

      removeFilter: (key) =>
        set((state) => {
          const { [key]: removed, ...rest } = state.activeFilters;
          return { activeFilters: rest };
        }),

      clearFilters: () => set({ activeFilters: {} }),

      // ========================================
      // 画廊视图控制实现
      // ========================================
      setGalleryLayout: (layout) => set({ galleryLayout: layout }),
      setGalleryZoom: (level) => set({ galleryZoomLevel: level }),
      setGallerySort: (sortBy, sortOrder) =>
        set({ gallerySortBy: sortBy as any, gallerySortOrder: sortOrder }),
      toggleShowFileName: () =>
        set((state) => ({ showFileName: !state.showFileName })),
      toggleShowFileInfo: () =>
        set((state) => ({ showFileInfo: !state.showFileInfo })),

      // 新UI画廊控制实现
      setGalleryViewMode: (mode) => set({ galleryViewMode: mode }),

      setGalleryZoomNew: (level) => set({ galleryZoomLevelNew: level }),

      toggleShowImageInfo: () =>
        set((state) => ({ showImageInfo: !state.showImageInfo })),

      // ========================================
      // 工作台控制实现
      // ========================================
      setActiveWorkbench: (workbench) => set({ activeWorkbench: workbench }),

      addToClipboard: (fileId, position = { x: 0, y: 0 }) =>
        set((state) => ({
          clipboardFiles: state.clipboardFiles.find(f => f.id === fileId)
            ? state.clipboardFiles
            : [...state.clipboardFiles, { id: fileId, position }],
        })),

      removeFromClipboard: (fileId) =>
        set((state) => ({
          clipboardFiles: state.clipboardFiles.filter(f => f.id !== fileId),
        })),

      updateClipboardPosition: (fileId, position) =>
        set((state) => ({
          clipboardFiles: state.clipboardFiles.map(f =>
            f.id === fileId ? { ...f, position } : f
          ),
        })),

      clearClipboard: () => set({ clipboardFiles: [] }),

      // ========================================
      // 信息栏控制实现
      // ========================================
      toggleInfoPanelSection: (panel: 'metadata' | 'rules' | 'cv' | 'ai') =>
        set((state) => ({
          activePanels: state.activePanels.includes(panel)
            ? state.activePanels.filter(p => p !== panel)
            : [...state.activePanels, panel],
        })),

      // ========================================
      // 重置实现
      // ========================================
      resetUI: () => set(initialState),
    }),
    {
      name: 'mizzy-star-ui-store',
    }
  )
);

// ============================================================================
// Selector Hooks (性能优化)
// ============================================================================

// 面板状态选择器
export const usePanelState = () => useUIStore((state) => ({
  showCatalogPanel: state.showCatalogPanel,
  showInfoPanel: state.showInfoPanel,
  showWorkbench: state.showWorkbench,
  isFullscreenGallery: state.isFullscreenGallery,
}));

// 性能优化的选择器 - 只选择需要的状态
export const useGallerySettings = () => useUIStore((state) => ({
  galleryViewMode: state.galleryViewMode,
  galleryZoomLevelNew: state.galleryZoomLevelNew,
  showImageInfo: state.showImageInfo,
  setGalleryViewMode: state.setGalleryViewMode,
  setGalleryZoomNew: state.setGalleryZoomNew,
  toggleShowImageInfo: state.toggleShowImageInfo,
}));

export const useFileSelection = () => useUIStore((state) => ({
  selectedFileIds: state.selectedFileIds,
  toggleFileSelection: state.toggleFileSelection,
  setSelectedFiles: state.setSelectedFiles,
  clearSelection: state.clearSelection,
}));

export const useSearchAndFilters = () => useUIStore((state) => ({
  searchQuery: state.searchQuery,
  activeFilters: state.activeFilters,
  setSearchQuery: state.setSearchQuery,
  setFilter: state.setFilter,
  removeFilter: state.removeFilter,
  clearFilters: state.clearFilters,
}));

export const useWorkspaceState = () => useUIStore((state) => ({
  activeWorkbench: state.activeWorkbench,
  clipboardFiles: state.clipboardFiles,
  setActiveWorkbench: state.setActiveWorkbench,
  addToClipboard: state.addToClipboard,
  removeFromClipboard: state.removeFromClipboard,
  updateClipboardPosition: state.updateClipboardPosition,
  clearClipboard: state.clearClipboard,
}));

// 选择状态选择器
export const useSelectionState = () => useUIStore((state) => ({
  selectedCaseId: state.selectedCaseId,
  selectedFileIds: state.selectedFileIds,
}));

// 画廊状态选择器
export const useGalleryState = () => useUIStore((state) => ({
  layout: state.galleryLayout,
  zoomLevel: state.galleryZoomLevel,
  sortBy: state.gallerySortBy,
  sortOrder: state.gallerySortOrder,
  showFileName: state.showFileName,
  showFileInfo: state.showFileInfo,
}));

// 搜索状态选择器
export const useSearchState = () => useUIStore((state) => ({
  searchQuery: state.searchQuery,
  activeFilters: state.activeFilters,
}));
