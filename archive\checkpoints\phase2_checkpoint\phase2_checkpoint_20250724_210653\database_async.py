# src/database_async.py
"""
异步数据库配置模块 - PostgreSQL单一数据库架构
支持异步SQLAlchemy和连接池，提升并发性能
"""
import os
import asyncio
from typing import Optional, Dict, AsyncGenerator
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import (
    create_async_engine,
    AsyncSession,
    async_sessionmaker,
    AsyncEngine
)
from sqlalchemy.orm import declarative_base
from sqlalchemy.pool import QueuePool, StaticPool

# 加载.env文件
try:
    from dotenv import load_dotenv
    from pathlib import Path
    project_root = Path(__file__).resolve().parents[2]
    env_path = project_root / ".env"
    if env_path.exists():
        load_dotenv(env_path)
except ImportError:
    pass

# --- Configuration ---
from .database_config import db_config

# 使用统一的数据库配置
MASTER_DATABASE_URL = db_config.get_async_master_database_url()

# 确保数据目录存在
DATA_DIR = db_config.data_dir
TRASH_DIR = DATA_DIR / "trash"
DATA_DIR.mkdir(exist_ok=True)
TRASH_DIR.mkdir(exist_ok=True)

# --- Base Model ---
Base = declarative_base()

# --- 异步PostgreSQL数据库引擎配置 ---
# 统一使用PostgreSQL + asyncpg配置
master_engine = create_async_engine(
    MASTER_DATABASE_URL,
    pool_size=db_config.pool_size,
    max_overflow=db_config.max_overflow,
    pool_timeout=db_config.pool_timeout,
    pool_recycle=db_config.pool_recycle,
    pool_pre_ping=True,
    echo=db_config.echo_sql,
    # asyncpg特定配置
    connect_args={
        "server_settings": {
            "application_name": "mizzy_star_async",
            "jit": "off",  # 禁用JIT以提高连接速度
            "timezone": "UTC",
            "statement_timeout": "60s",
            "idle_in_transaction_session_timeout": "300s"
        }
    }
)

# 异步会话工厂
AsyncMasterSessionLocal = async_sessionmaker(
    master_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

async def get_master_db() -> AsyncGenerator[AsyncSession, None]:
    """获取主数据库异步会话的依赖注入函数"""
    async with AsyncMasterSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# --- 批量操作支持 ---
class AsyncBatchOperation:
    """支持批量异步数据库操作的上下文管理器"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.operations = []
    
    async def add_operation(self, operation):
        """添加操作到批次"""
        self.operations.append(operation)
    
    async def execute_batch(self):
        """执行所有批次操作"""
        try:
            # 执行所有操作
            for operation in self.operations:
                await operation(self.session)
            
            # 批量提交
            await self.session.commit()
            return True
        except Exception as e:
            await self.session.rollback()
            raise e

# --- 连接池监控 ---
async def get_pool_status() -> Dict[str, any]:
    """获取连接池状态信息，用于监控 - PostgreSQL单一数据库架构"""
    status = {
        "master_engine": {
            "pool_size": master_engine.pool.size() if hasattr(master_engine.pool, 'size') else 0,
            "checked_in": master_engine.pool.checkedin() if hasattr(master_engine.pool, 'checkedin') else 0,
            "checked_out": master_engine.pool.checkedout() if hasattr(master_engine.pool, 'checkedout') else 0,
        },
        "database_type": "PostgreSQL",
        "database_url": MASTER_DATABASE_URL.replace(db_config.postgres_password, '***')
    }
    return status

# --- 清理函数 ---
async def cleanup_connections():
    """清理所有数据库连接，应用关闭时调用 - PostgreSQL单一数据库架构"""
    # 清理主数据库连接
    await master_engine.dispose()

    # PostgreSQL单一数据库架构 - 不再需要清理案例数据库连接


# --- 实用工具函数 ---
async def test_async_connection() -> bool:
    """测试异步数据库连接"""
    try:
        from sqlalchemy import text
        async with master_engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            test_value = result.fetchone()[0]
            return test_value == 1
    except Exception:
        return False


async def get_database_info() -> Dict[str, str]:
    """获取数据库信息"""
    try:
        from sqlalchemy import text
        async with master_engine.begin() as conn:
            # PostgreSQL版本查询
            result = await conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            return {
                "type": "PostgreSQL",
                "version": version.split(',')[0],
                "driver": "asyncpg"
            }
    except Exception as e:
        return {
            "type": "Unknown",
            "version": "Unknown",
            "error": str(e)
        }