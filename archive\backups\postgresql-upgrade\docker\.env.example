# PostgreSQL配置
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=mizzy_main
POSTGRES_USER=mizzy_user

# 数据库连接URL
DATABASE_URL=postgresql://mizzy_user:your_secure_password_here@localhost:5432/mizzy_main

# pgAdmin配置 (可选)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123

# 只读用户配置
READONLY_USER=mizzy_readonly
READONLY_PASSWORD=readonly_pass_123

# 备份用户配置
BACKUP_USER=mizzy_backup
BACKUP_PASSWORD=backup_pass_123

# 应用配置
APP_ENV=development
DEBUG=true
LOG_LEVEL=INFO

# 性能配置
MAX_CONNECTIONS=200
SHARED_BUFFERS=256MB
EFFECTIVE_CACHE_SIZE=1GB
WORK_MEM=4MB

# 安全配置
SSL_MODE=prefer
SSL_CERT_PATH=
SSL_KEY_PATH=
SSL_CA_PATH=

# 备份配置
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *

# 监控配置
ENABLE_MONITORING=true
METRICS_PORT=9187
ALERT_EMAIL=<EMAIL>
