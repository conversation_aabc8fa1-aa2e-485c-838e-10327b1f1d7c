# 🚀 异步CRUD重构规划 - PostgreSQL单一数据库架构

## 📋 重构概述

**目标**: 基于纯PostgreSQL架构重构异步CRUD操作  
**架构**: 单一数据库 + 异步SQLAlchemy + FastAPI  
**优势**: 高并发、统一数据管理、企业级性能  

## 🏗️ 数据库架构

### 核心表结构
```sql
-- 案例表 (cases) - 1对多关系
cases {
  id: SERIAL PRIMARY KEY
  case_name: VARCHAR NOT NULL
  description: VARCHAR
  created_at: TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  status: VARCHAR DEFAULT 'active'
  deleted_at: TIMESTAMP WITH TIME ZONE
  cover_image_url: VARCHAR
  cover_type: VARCHAR DEFAULT 'placeholder'
  cover_source_file_id: INTEGER REFERENCES files(id)
  cover_needs_attention: BOOLEAN DEFAULT FALSE
  cover_updated_at: TIMESTAMP WITH TIME ZONE
}

-- 文件表 (files) - 多对1关系
files {
  id: SERIAL PRIMARY KEY
  case_id: INTEGER REFERENCES cases(id) ON DELETE CASCADE
  file_name: VARCHAR
  file_path: VARCHAR
  file_type: VARCHAR
  thumbnail_small_path: VARCHAR
  width: INTEGER
  height: INTEGER
  created_at: TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  taken_at: TIMESTAMP WITH TIME ZONE
  
  -- 质量分析字段
  quality_score: FLOAT
  sharpness: FLOAT
  brightness: FLOAT
  dynamic_range: FLOAT
  num_faces: INTEGER
  face_sharpness: FLOAT
  face_quality: FLOAT
  
  -- 分组和聚类
  cluster_id: INTEGER
  phash: VARCHAR
  group_id: VARCHAR
  frame_number: INTEGER
  
  -- 标签系统 (JSONB)
  tags: JSONB
  
  -- AI向量字段 (预留)
  vector_image: TEXT
  vector_text: TEXT
}

-- 其他支持表
custom_tags, file_custom_tags, case_processing_rules, etc.
```

### 关系映射
```
cases (1) ←→ (N) files
cases.id ← files.case_id (外键)
cases.cover_source_file_id → files.id (封面关联)
```

## 🔧 异步CRUD重构任务

### 阶段1: 基础异步CRUD重构

#### 1.1 异步数据库配置优化
```python
# src/database_async.py (已部分完成)
- ✅ PostgreSQL异步连接配置
- ✅ 移除旧架构函数
- 🔄 添加asyncpg驱动支持
- 🔄 优化连接池配置
- 🔄 添加事务管理
```

#### 1.2 异步案例CRUD重构
```python
# src/crud/case_crud_async.py (需要重构)
async def create_case_async(db: AsyncSession, case: CaseCreate) -> Case
async def get_cases_async(db: AsyncSession, skip: int, limit: int) -> List[Case]
async def get_case_async(db: AsyncSession, case_id: int) -> Optional[Case]
async def update_case_async(db: AsyncSession, case_id: int, case_update: CaseUpdate) -> Optional[Case]
async def delete_case_async(db: AsyncSession, case_id: int) -> bool
async def restore_case_async(db: AsyncSession, case_id: int) -> bool
```

#### 1.3 异步文件CRUD重构
```python
# src/crud/file_crud_async.py (需要重构)
async def create_file_async(db: AsyncSession, case_id: int, file: FileCreate) -> File
async def get_files_async(db: AsyncSession, case_id: int) -> List[File]
async def get_file_async(db: AsyncSession, file_id: int, case_id: int) -> Optional[File]
async def update_file_async(db: AsyncSession, file_id: int, file_update: dict) -> Optional[File]
async def delete_file_async(db: AsyncSession, file_id: int, case_id: int) -> bool
async def batch_import_files_async(db: AsyncSession, case_id: int, files: List[FileCreate]) -> List[File]
```

### 阶段2: 高级功能重构

#### 2.1 标签系统异步化
```python
# src/crud/tag_crud_async.py (新建)
async def add_tags_to_file_async(db: AsyncSession, file_id: int, tags: List[str]) -> File
async def remove_tags_from_file_async(db: AsyncSession, file_id: int, tags: List[str]) -> File
async def search_files_by_tags_async(db: AsyncSession, case_id: int, tags: List[str]) -> List[File]
async def get_popular_tags_async(db: AsyncSession, case_id: int, limit: int) -> List[dict]
```

#### 2.2 批量操作优化
```python
# src/crud/batch_operations_async.py (新建)
async def batch_update_files_async(db: AsyncSession, updates: List[dict]) -> List[File]
async def batch_delete_files_async(db: AsyncSession, file_ids: List[int]) -> dict
async def batch_tag_files_async(db: AsyncSession, file_ids: List[int], tags: List[str]) -> List[File]
```

### 阶段3: 异步路由重构

#### 3.1 异步案例路由
```python
# src/routers/cases_async.py (重构)
@router.post("/cases/", response_model=Case)
async def create_case_async_endpoint(case: CaseCreate, db: AsyncSession = Depends(get_async_db))

@router.get("/cases/", response_model=List[Case])
async def get_cases_async_endpoint(skip: int = 0, limit: int = 100, db: AsyncSession = Depends(get_async_db))

@router.get("/cases/{case_id}", response_model=Case)
async def get_case_async_endpoint(case_id: int, db: AsyncSession = Depends(get_async_db))
```

#### 3.2 异步文件路由
```python
# src/routers/files_async.py (新建)
@router.post("/cases/{case_id}/files/", response_model=File)
async def create_file_async_endpoint(case_id: int, file: FileCreate, db: AsyncSession = Depends(get_async_db))

@router.post("/cases/{case_id}/files/batch", response_model=List[File])
async def batch_import_files_async_endpoint(case_id: int, files: List[FileCreate], db: AsyncSession = Depends(get_async_db))
```

## 💡 功能流程示例

### 示例1: 新增案例流程

#### 前端界面操作
```typescript
// 用户在界面中填写案例信息
const newCase = {
  case_name: "婚礼摄影 - 张三李四",
  description: "2025年春季婚礼摄影项目"
}

// 调用异步API
const response = await fetch('/api/v1/async/cases/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(newCase)
})
```

#### 后端异步处理
```python
@router.post("/async/cases/", response_model=Case)
async def create_case_async_endpoint(
    case: CaseCreate, 
    db: AsyncSession = Depends(get_async_db),
    background_tasks: BackgroundTasks
):
    # 1. 异步创建案例记录
    new_case = await create_case_async(db, case)
    
    # 2. 后台异步创建文件夹结构
    background_tasks.add_task(create_case_directories, new_case.id)
    
    # 3. 后台异步初始化封面
    background_tasks.add_task(initialize_case_cover, new_case.id)
    
    return new_case

async def create_case_async(db: AsyncSession, case: CaseCreate) -> Case:
    # PostgreSQL单一数据库操作
    db_case = Case(
        case_name=case.case_name,
        description=case.description,
        status=CaseStatus.ACTIVE,
        cover_type=CoverType.PLACEHOLDER
    )
    
    db.add(db_case)
    await db.commit()
    await db.refresh(db_case)
    
    return db_case
```

#### 数据库操作
```sql
-- 执行的SQL (由SQLAlchemy生成)
INSERT INTO cases (case_name, description, created_at, status, cover_type, cover_needs_attention)
VALUES ('婚礼摄影 - 张三李四', '2025年春季婚礼摄影项目', NOW(), 'active', 'placeholder', FALSE)
RETURNING id, case_name, description, created_at, status, cover_type;
```

#### 文件系统操作
```python
async def create_case_directories(case_id: int):
    """后台任务：创建案例目录结构"""
    case_dir = Path(f"data/case_{case_id}")
    case_dir.mkdir(exist_ok=True)
    (case_dir / "uploads").mkdir(exist_ok=True)
    (case_dir / "thumbnails").mkdir(exist_ok=True)
    (case_dir / "exports").mkdir(exist_ok=True)
```

### 示例2: 批量导入文件流程

#### 前端界面操作
```typescript
// 用户选择多个文件进行批量导入
const fileList = [
  { path: "D:/photos/IMG_001.jpg", name: "IMG_001.jpg" },
  { path: "D:/photos/IMG_002.jpg", name: "IMG_002.jpg" },
  // ... 更多文件
]

// 调用批量导入API
const response = await fetch(`/api/v1/async/cases/${caseId}/files/batch`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ files: fileList })
})
```

#### 后端异步处理
```python
@router.post("/async/cases/{case_id}/files/batch", response_model=List[File])
async def batch_import_files_async_endpoint(
    case_id: int,
    files_data: BatchFileImport,
    db: AsyncSession = Depends(get_async_db),
    background_tasks: BackgroundTasks
):
    # 1. 验证案例存在
    case = await get_case_async(db, case_id)
    if not case:
        raise HTTPException(404, "案例不存在")
    
    # 2. 异步批量创建文件记录
    imported_files = await batch_import_files_async(db, case_id, files_data.files)
    
    # 3. 后台异步处理任务
    for file in imported_files:
        background_tasks.add_task(process_file_async, file.id)
    
    return imported_files

async def batch_import_files_async(db: AsyncSession, case_id: int, files: List[FileCreate]) -> List[File]:
    """批量导入文件 - 优化的数据库操作"""
    db_files = []
    
    # 批量创建文件对象
    for file_data in files:
        db_file = File(
            case_id=case_id,
            file_name=file_data.file_name,
            file_path=file_data.file_path,
            file_type=file_data.file_type,
            width=file_data.width,
            height=file_data.height,
            tags=file_data.tags or {}
        )
        db_files.append(db_file)
    
    # 批量插入数据库
    db.add_all(db_files)
    await db.commit()
    
    # 批量刷新对象
    for db_file in db_files:
        await db.refresh(db_file)
    
    return db_files
```

#### 数据库批量操作
```sql
-- 优化的批量插入SQL
INSERT INTO files (case_id, file_name, file_path, file_type, width, height, created_at, tags)
VALUES 
  (1, 'IMG_001.jpg', 'data/case_1/uploads/IMG_001.jpg', 'image/jpeg', 1920, 1080, NOW(), '{}'),
  (1, 'IMG_002.jpg', 'data/case_1/uploads/IMG_002.jpg', 'image/jpeg', 1920, 1080, NOW(), '{}'),
  -- ... 更多记录
RETURNING id, case_id, file_name, file_path, created_at;
```

#### 后台异步处理
```python
async def process_file_async(file_id: int):
    """后台任务：处理单个文件"""
    async with get_async_db() as db:
        file = await get_file_async(db, file_id)
        if not file:
            return
        
        # 1. 生成缩略图
        thumbnail_path = await generate_thumbnail_async(file.file_path)
        
        # 2. 分析图像质量
        quality_data = await analyze_image_quality_async(file.file_path)
        
        # 3. 提取EXIF信息
        exif_data = await extract_exif_async(file.file_path)
        
        # 4. 更新文件记录
        await update_file_async(db, file_id, {
            'thumbnail_small_path': thumbnail_path,
            'quality_score': quality_data.score,
            'sharpness': quality_data.sharpness,
            'brightness': quality_data.brightness,
            'taken_at': exif_data.taken_at
        })
```

### 示例3: 标签管理流程

#### 前端界面操作
```typescript
// 用户为文件添加标签
const fileId = 123
const newTags = ["人像", "室外", "自然光"]

// 调用标签API
await fetch(`/api/v1/async/files/${fileId}/tags`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ tags: newTags })
})

// 搜索带有特定标签的文件
const searchResults = await fetch(`/api/v1/async/cases/${caseId}/files/search?tags=人像,室外`)
```

#### 后端异步处理
```python
@router.post("/async/files/{file_id}/tags", response_model=File)
async def add_tags_to_file_async_endpoint(
    file_id: int,
    tags_data: TagsUpdate,
    db: AsyncSession = Depends(get_async_db)
):
    return await add_tags_to_file_async(db, file_id, tags_data.tags)

async def add_tags_to_file_async(db: AsyncSession, file_id: int, new_tags: List[str]) -> File:
    """为文件添加标签 - JSONB操作"""
    # 获取文件
    file = await db.get(File, file_id)
    if not file:
        raise ValueError("文件不存在")
    
    # 合并标签 (利用JSONB功能)
    current_tags = file.tags or {}
    for tag in new_tags:
        current_tags[tag] = {
            "added_at": datetime.utcnow().isoformat(),
            "confidence": 1.0
        }
    
    # 更新文件
    file.tags = current_tags
    await db.commit()
    await db.refresh(file)
    
    return file

@router.get("/async/cases/{case_id}/files/search", response_model=List[File])
async def search_files_by_tags_async_endpoint(
    case_id: int,
    tags: str,
    db: AsyncSession = Depends(get_async_db)
):
    tag_list = tags.split(',')
    return await search_files_by_tags_async(db, case_id, tag_list)

async def search_files_by_tags_async(db: AsyncSession, case_id: int, tags: List[str]) -> List[File]:
    """基于标签搜索文件 - JSONB查询"""
    from sqlalchemy import select, and_
    
    # 构建JSONB查询条件
    conditions = [File.case_id == case_id]
    for tag in tags:
        conditions.append(File.tags.has_key(tag))  # JSONB has_key操作
    
    # 执行查询
    stmt = select(File).where(and_(*conditions))
    result = await db.execute(stmt)
    return result.scalars().all()
```

#### 数据库JSONB操作
```sql
-- 添加标签的SQL
UPDATE files 
SET tags = tags || '{"人像": {"added_at": "2025-07-22T10:30:00", "confidence": 1.0}}'::jsonb
WHERE id = 123;

-- 基于标签搜索的SQL
SELECT * FROM files 
WHERE case_id = 1 
  AND tags ? '人像' 
  AND tags ? '室外';

-- 获取热门标签的SQL
SELECT jsonb_object_keys(tags) as tag, COUNT(*) as count
FROM files 
WHERE case_id = 1
GROUP BY tag
ORDER BY count DESC
LIMIT 10;
```

## 📈 性能优化策略

### 数据库层面
```sql
-- 关键索引
CREATE INDEX idx_files_case_id ON files(case_id);
CREATE INDEX idx_files_created_at ON files(created_at DESC);
CREATE INDEX idx_files_tags_gin ON files USING GIN(tags);
CREATE INDEX idx_cases_status ON cases(status);

-- 分区表 (大数据量时)
CREATE TABLE files_partitioned (LIKE files) PARTITION BY RANGE (created_at);
```

### 应用层面
```python
# 连接池优化
engine = create_async_engine(
    DATABASE_URL,
    pool_size=20,
    max_overflow=30,
    pool_timeout=30,
    pool_recycle=3600,
    pool_pre_ping=True
)

# 批量操作优化
async def bulk_insert_files(db: AsyncSession, files: List[dict]):
    await db.execute(insert(File), files)
    await db.commit()

# 缓存策略
from functools import lru_cache

@lru_cache(maxsize=100)
async def get_popular_tags_cached(case_id: int):
    # 缓存热门标签查询结果
    pass
```

## 🎯 后续任务清单

### 立即任务 (本周)
- [ ] 安装asyncpg驱动: `pip install asyncpg`
- [ ] 重构database_async.py配置
- [ ] 重构case_crud_async.py基础CRUD
- [ ] 重构file_crud_async.py基础CRUD
- [ ] 创建异步路由测试

### 短期任务 (2周内)
- [ ] 实现标签系统异步CRUD
- [ ] 实现批量操作优化
- [ ] 添加异步文件处理任务
- [ ] 创建性能监控接口
- [ ] 编写异步API测试

### 中期任务 (1月内)
- [ ] 实现AI向量搜索异步化
- [ ] 添加实时通知系统
- [ ] 实现分布式任务队列
- [ ] 优化大文件处理流程
- [ ] 添加缓存层

### 长期任务 (3月内)
- [ ] 实现数据库读写分离
- [ ] 添加分布式存储支持
- [ ] 实现智能预加载
- [ ] 添加性能分析工具
- [ ] 实现自动扩缩容

## 🚀 开始重构

准备好开始异步CRUD重构了吗？我们可以从以下任一任务开始：

1. **安装asyncpg驱动并测试连接**
2. **重构case_crud_async.py**
3. **创建异步路由示例**
4. **实现批量文件导入**

请告诉我您想从哪个任务开始！
