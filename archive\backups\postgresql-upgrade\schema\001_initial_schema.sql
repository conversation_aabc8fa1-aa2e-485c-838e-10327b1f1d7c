-- PostgreSQL Schema for Mizzy Star - Initial Schema
-- 基于现有SQLite结构优化设计，支持JSONB和高性能查询

-- =============================================================================
-- 主数据库表 (相当于 mizzy_star.db)
-- =============================================================================

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 案例状态枚举
CREATE TYPE case_status AS ENUM ('active', 'deleted', 'permanently_deleted');

-- 封面类型枚举  
CREATE TYPE cover_type AS ENUM ('manual', 'automatic', 'placeholder');

-- 案例表
CREATE TABLE IF NOT EXISTS cases (
    id SERIAL PRIMARY KEY,
    case_name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status case_status DEFAULT 'active' NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE,
    db_path VARCHAR(500) UNIQUE,
    
    -- 封面相关字段
    cover_image_url VARCHAR(500),
    cover_type cover_type DEFAULT 'placeholder' NOT NULL,
    cover_source_file_id INTEGER,
    cover_needs_attention BOOLEAN DEFAULT FALSE NOT NULL,
    cover_updated_at TIMESTAMP WITH TIME ZONE
);

-- 规则类型枚举
CREATE TYPE rule_type AS ENUM ('FILENAME_PARSING', 'DATE_TAGGING_FORMAT');

-- 案例处理规则表
CREATE TABLE IF NOT EXISTS case_processing_rules (
    id SERIAL PRIMARY KEY,
    case_id INTEGER NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
    rule_type rule_type NOT NULL,
    rule_config JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- 案例数据库表 (相当于 case_X.db)
-- =============================================================================

-- 文件表 - 核心表，使用JSONB优化标签存储
CREATE TABLE IF NOT EXISTS files (
    id SERIAL PRIMARY KEY,
    file_name VARCHAR(255),
    file_type VARCHAR(100),
    file_path TEXT NOT NULL,
    thumbnail_small_path TEXT,
    width INTEGER,
    height INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    taken_at TIMESTAMP WITH TIME ZONE,
    
    -- 图像质量分析字段
    quality_score DECIMAL(5,2),
    sharpness DECIMAL(8,4),
    brightness DECIMAL(8,4),
    dynamic_range DECIMAL(8,4),
    num_faces INTEGER,
    face_sharpness DECIMAL(8,4),
    face_quality DECIMAL(8,4),
    cluster_id INTEGER,
    phash VARCHAR(64),
    group_id VARCHAR(100),
    frame_number INTEGER,
    
    -- 标签系统字段 - 使用JSONB优化
    tags JSONB,
    
    -- 为AI向量搜索预留字段 (暂时使用TEXT类型，未来可升级为VECTOR)
    embedding_vector TEXT, -- 预留OpenAI embedding维度，JSON格式存储
    
    -- 全文搜索字段 (自动生成)
    search_vector TSVECTOR GENERATED ALWAYS AS (
        to_tsvector('english', 
            COALESCE(file_name, '') || ' ' ||
            COALESCE(tags::text, '')
        )
    ) STORED
);

-- 标签缓存表 - 优化标签查询性能
CREATE TABLE IF NOT EXISTS tag_cache (
    id SERIAL PRIMARY KEY,
    tag_category VARCHAR(50) NOT NULL,
    tag_name VARCHAR(100) NOT NULL,
    tag_value VARCHAR(500) NOT NULL,
    file_ids INTEGER[] NOT NULL, -- 使用PostgreSQL数组类型
    file_count INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 删除文件记录表
CREATE TABLE IF NOT EXISTS deleted_files (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_permanent BOOLEAN DEFAULT FALSE NOT NULL
);

-- 自定义标签表
CREATE TABLE IF NOT EXISTS custom_tags (
    id SERIAL PRIMARY KEY,
    tag_name VARCHAR(100) NOT NULL UNIQUE,
    tag_color VARCHAR(7) DEFAULT '#3B82F6' NOT NULL,
    display_order INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 文件自定义标签关联表
CREATE TABLE IF NOT EXISTS file_custom_tags (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    custom_tag_id INTEGER NOT NULL REFERENCES custom_tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 防止重复关联
    UNIQUE(file_id, custom_tag_id)
);

-- =============================================================================
-- 触发器函数 - 自动更新时间戳
-- =============================================================================

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =============================================================================
-- 注释说明
-- =============================================================================

-- 表注释
COMMENT ON TABLE files IS '文件表 - 存储文件基本信息和JSONB标签数据';
COMMENT ON TABLE tag_cache IS '标签缓存表 - 优化标签查询性能';
COMMENT ON TABLE custom_tags IS '自定义标签表 - 用户定义的标签';
COMMENT ON TABLE file_custom_tags IS '文件自定义标签关联表';

-- 字段注释
COMMENT ON COLUMN files.tags IS 'JSONB格式的标签数据，包含properties和tags两个主要结构';
COMMENT ON COLUMN files.embedding_vector IS '预留的向量字段，用于未来AI搜索功能';
COMMENT ON COLUMN files.search_vector IS '全文搜索向量，自动从文件名和标签生成';
COMMENT ON COLUMN tag_cache.file_ids IS 'PostgreSQL数组类型，存储包含该标签的文件ID列表';

-- Schema创建完成标记
INSERT INTO system_config (config_key, config_value, description) 
VALUES ('schema_version', '1.0.0', 'PostgreSQL Schema版本') 
ON CONFLICT (config_key) DO UPDATE SET 
    config_value = EXCLUDED.config_value,
    updated_at = NOW();
