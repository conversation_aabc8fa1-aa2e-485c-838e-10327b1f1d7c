/**
 * 异步API客户端
 * 专门用于调用后端异步API的增强版客户端
 */

class AsyncApiClient {
    constructor(baseURL = 'http://localhost:8000') {
        this.baseURL = baseURL;
        this.client = axios.create({
            baseURL: this.baseURL,
            timeout: 15000, // 异步API可能需要更长时间
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        // 请求拦截器
        this.client.interceptors.request.use(
            (config) => {
                console.log(`🚀 异步API请求: ${config.method?.toUpperCase()} ${config.url}`);
                // 添加请求时间戳
                config.metadata = { startTime: Date.now() };
                return config;
            },
            (error) => {
                console.error('❌ 异步API请求错误:', error);
                return Promise.reject(error);
            }
        );
        
        // 响应拦截器
        this.client.interceptors.response.use(
            (response) => {
                const duration = Date.now() - response.config.metadata.startTime;
                console.log(`✅ 异步API响应: ${response.status} ${response.config.url} (${duration}ms)`);
                return response;
            },
            (error) => {
                const duration = error.config?.metadata ? Date.now() - error.config.metadata.startTime : 0;
                console.error(`❌ 异步API响应错误: ${error.response?.status || 'Network'} ${error.config?.url} (${duration}ms)`);
                this.handleError(error);
                return Promise.reject(error);
            }
        );
    }
    
    /**
     * 错误处理
     */
    handleError(error) {
        if (error.response) {
            const { status, data } = error.response;
            let message = `异步API错误 (${status})`;
            
            if (data && data.detail) {
                message = Array.isArray(data.detail) ? data.detail[0].msg : data.detail;
            }
            
            // 根据状态码显示不同类型的通知
            switch (status) {
                case 400:
                    this.showNotification('请求参数错误', 'warning');
                    break;
                case 401:
                    this.showNotification('未授权访问', 'error');
                    break;
                case 403:
                    this.showNotification('访问被禁止', 'error');
                    break;
                case 404:
                    this.showNotification('资源不存在', 'warning');
                    break;
                case 500:
                    this.showNotification('服务器内部错误', 'error');
                    break;
                default:
                    this.showNotification(message, 'error');
            }
        } else if (error.request) {
            this.showNotification('网络连接失败，请检查服务器状态', 'error');
        } else {
            this.showNotification('请求配置错误', 'error');
        }
    }
    
    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 如果存在全局通知函数，使用它
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
    
    // ==================== 异步案例管理API ====================
    
    /**
     * 获取案例列表 (异步)
     */
    async getCasesAsync(params = {}) {
        try {
            const response = await this.client.get('/api/v1/async/cases/', { params });
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * 获取单个案例 (异步)
     */
    async getCaseAsync(caseId, includeFiles = true) {
        try {
            const response = await this.client.get(`/api/v1/async/cases/${caseId}`, {
                params: { include_files: includeFiles }
            });
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * 创建案例 (异步)
     */
    async createCaseAsync(caseData) {
        try {
            const response = await this.client.post('/api/v1/async/cases/', caseData);
            this.showNotification('案例创建成功', 'success');
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * 更新案例 (异步)
     */
    async updateCaseAsync(caseId, caseData) {
        try {
            const response = await this.client.put(`/api/v1/async/cases/${caseId}`, caseData);
            this.showNotification('案例更新成功', 'success');
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * 删除案例 (异步)
     */
    async deleteCaseAsync(caseId) {
        try {
            const response = await this.client.delete(`/api/v1/async/cases/${caseId}`);
            this.showNotification('案例删除成功', 'success');
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    // ==================== 系统监控API ====================
    
    /**
     * 健康检查 (异步)
     */
    async healthCheckAsync() {
        try {
            const response = await this.client.get('/api/v1/async/health');
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * 系统状态 (异步)
     */
    async getSystemStatusAsync() {
        try {
            const response = await this.client.get('/api/v1/async/system/status');
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * 性能指标 (异步)
     */
    async getPerformanceMetricsAsync() {
        try {
            const response = await this.client.get('/api/v1/async/system/performance');
            return response.data;
        } catch (error) {
            throw error;
        }
    }
    
    // ==================== 批量操作API ====================
    
    /**
     * 批量获取案例
     */
    async batchGetCasesAsync(caseIds) {
        try {
            const promises = caseIds.map(id => this.getCaseAsync(id));
            const results = await Promise.allSettled(promises);
            
            const successful = results
                .filter(result => result.status === 'fulfilled')
                .map(result => result.value);
            
            const failed = results
                .filter(result => result.status === 'rejected')
                .map(result => result.reason);
            
            return { successful, failed };
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * 批量删除案例
     */
    async batchDeleteCasesAsync(caseIds) {
        try {
            const promises = caseIds.map(id => this.deleteCaseAsync(id));
            const results = await Promise.allSettled(promises);
            
            const successful = results.filter(result => result.status === 'fulfilled').length;
            const failed = results.filter(result => result.status === 'rejected').length;
            
            this.showNotification(`批量删除完成: ${successful}成功, ${failed}失败`, 'info');
            return { successful, failed };
        } catch (error) {
            throw error;
        }
    }
    
    // ==================== 性能测试方法 ====================
    
    /**
     * 性能测试 - 单请求
     */
    async performanceSingleRequest() {
        const startTime = performance.now();
        try {
            await this.getCasesAsync();
            const duration = performance.now() - startTime;
            console.log(`🚀 异步API单请求性能: ${duration.toFixed(2)}ms`);
            return duration;
        } catch (error) {
            console.error('❌ 性能测试失败:', error);
            throw error;
        }
    }
    
    /**
     * 性能测试 - 并发请求
     */
    async performanceConcurrentRequests(count = 5) {
        const startTime = performance.now();
        try {
            const promises = Array(count).fill().map(() => this.getCasesAsync());
            await Promise.all(promises);
            const duration = performance.now() - startTime;
            console.log(`🚀 异步API并发性能 (${count}个): ${duration.toFixed(2)}ms`);
            return duration;
        } catch (error) {
            console.error('❌ 并发性能测试失败:', error);
            throw error;
        }
    }
    
    // ==================== 实用工具方法 ====================
    
    /**
     * 检查API可用性
     */
    async checkApiAvailability() {
        try {
            const health = await this.healthCheckAsync();
            return {
                available: true,
                status: health.status,
                database: health.database,
                async_system: health.async_system
            };
        } catch (error) {
            return {
                available: false,
                error: error.message
            };
        }
    }
    
    /**
     * 获取API统计信息
     */
    async getApiStats() {
        try {
            const [health, status] = await Promise.all([
                this.healthCheckAsync(),
                this.getSystemStatusAsync()
            ]);
            
            return {
                health: health.status,
                database_type: status.database.type,
                connection_pool_size: status.connection_pool.master_engine.pool_size,
                active_cases: status.statistics.active_cases,
                system_status: status.status
            };
        } catch (error) {
            console.error('❌ 获取API统计失败:', error);
            throw error;
        }
    }
}

// 创建全局异步API客户端实例
const asyncApiClient = new AsyncApiClient();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AsyncApiClient;
}
