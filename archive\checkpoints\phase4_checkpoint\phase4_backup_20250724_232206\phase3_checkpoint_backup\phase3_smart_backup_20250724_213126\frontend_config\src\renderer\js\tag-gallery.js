/**
 * 标签即画廊系统前端逻辑
 * 实现高性能搜索、资产处理、智能导出功能
 */

class TagGalleryManager {
    constructor() {
        this.currentCaseId = this.getCaseIdFromUrl();
        this.currentQuery = {};
        this.selectedFiles = new Set();
        this.currentPage = 1;
        this.pageSize = 50;
        this.totalFiles = 0;
        this.searchCache = new Map();
        
        this.initializeEventListeners();
        this.loadInitialData();
    }

    getCaseIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('case_id')) || 1;
    }

    initializeEventListeners() {
        // 快速搜索
        document.getElementById('quickSearchBtn').addEventListener('click', () => {
            this.performQuickSearch();
        });

        document.getElementById('quickSearch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performQuickSearch();
            }
        });

        // 高级搜索
        document.getElementById('advancedSearchBtn').addEventListener('click', () => {
            this.performAdvancedSearch();
        });

        document.getElementById('clearFiltersBtn').addEventListener('click', () => {
            this.clearAllFilters();
        });

        // 用户标签输入
        document.getElementById('userTagsInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addUserTag(e.target.value.trim());
                e.target.value = '';
            }
        });

        // 自定义标签
        document.getElementById('addCustomTagBtn').addEventListener('click', () => {
            this.addCustomTagInput();
        });

        // 质量范围滑块
        document.getElementById('qualityMin').addEventListener('input', (e) => {
            document.getElementById('qualityMinLabel').textContent = e.target.value;
        });

        document.getElementById('qualityMax').addEventListener('input', (e) => {
            document.getElementById('qualityMaxLabel').textContent = e.target.value;
        });

        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchView(e.target.dataset.view);
            });
        });

        // 处理器选择
        document.querySelectorAll('.processor-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectProcessor(e.target.dataset.processor);
            });
        });

        // 批量处理
        document.getElementById('batchProcessBtn').addEventListener('click', () => {
            this.startBatchProcessing();
        });

        // 导出功能
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.showExportModal();
        });

        document.getElementById('closeExportModal').addEventListener('click', () => {
            this.hideExportModal();
        });

        document.getElementById('startExport').addEventListener('click', () => {
            this.startExport();
        });

        // 分页
        document.getElementById('prevPageBtn').addEventListener('click', () => {
            this.goToPage(this.currentPage - 1);
        });

        document.getElementById('nextPageBtn').addEventListener('click', () => {
            this.goToPage(this.currentPage + 1);
        });

        // 搜索建议
        document.getElementById('quickSearch').addEventListener('input', (e) => {
            this.showSearchSuggestions(e.target.value);
        });
    }

    async loadInitialData() {
        this.showLoading();
        await this.performSearch({});
    }

    async performQuickSearch() {
        const query = document.getElementById('quickSearch').value.trim();
        if (!query) return;

        const searchQuery = {
            case_id: this.currentCaseId,
            text_query: query,
            limit: this.pageSize,
            offset: (this.currentPage - 1) * this.pageSize
        };

        await this.performSearch(searchQuery);
    }

    async performAdvancedSearch() {
        const searchQuery = this.buildAdvancedSearchQuery();
        await this.performSearch(searchQuery);
    }

    buildAdvancedSearchQuery() {
        const userTags = this.getSelectedUserTags();
        const customTags = this.getCustomTags();
        const qualityMin = parseInt(document.getElementById('qualityMin').value);
        const qualityMax = parseInt(document.getElementById('qualityMax').value);
        const dateFrom = document.getElementById('dateFrom').value;
        const dateTo = document.getElementById('dateTo').value;

        const query = {
            case_id: this.currentCaseId,
            limit: this.pageSize,
            offset: (this.currentPage - 1) * this.pageSize
        };

        if (userTags.length > 0) {
            query.user_tags = userTags;
        }

        if (Object.keys(customTags).length > 0) {
            query.custom_tags = customTags;
        }

        if (qualityMin > 0 || qualityMax < 100) {
            query.quality_min = qualityMin / 100;
            query.quality_max = qualityMax / 100;
        }

        if (dateFrom || dateTo) {
            query.date_range = [dateFrom, dateTo];
        }

        return query;
    }

    async performSearch(searchQuery) {
        try {
            this.showLoading();
            this.currentQuery = searchQuery;

            // 检查缓存
            const cacheKey = JSON.stringify(searchQuery);
            if (this.searchCache.has(cacheKey)) {
                const cachedResult = this.searchCache.get(cacheKey);
                this.displaySearchResults(cachedResult);
                return;
            }

            const startTime = performance.now();
            
            const response = await fetch('/api/v1/gallery/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(searchQuery)
            });

            if (!response.ok) {
                throw new Error(`搜索失败: ${response.status}`);
            }

            const result = await response.json();
            const endTime = performance.now();
            
            result.searchTime = Math.round(endTime - startTime);

            // 缓存结果
            this.searchCache.set(cacheKey, result);

            this.displaySearchResults(result);
            this.updateActiveFilters(searchQuery);

        } catch (error) {
            console.error('搜索失败:', error);
            this.showError('搜索失败，请稍后重试');
        }
    }

    displaySearchResults(result) {
        this.hideLoading();

        const { results, total, searchTime } = result;
        this.totalFiles = total;

        // 更新搜索信息
        document.getElementById('resultCount').textContent = total;
        document.getElementById('searchTime').textContent = `${searchTime}ms`;

        // 显示文件网格
        const fileGrid = document.getElementById('fileGrid');
        
        if (results.length === 0) {
            this.showEmptyState();
            return;
        }

        fileGrid.innerHTML = '';
        
        results.forEach(file => {
            const fileCard = this.createFileCard(file);
            fileGrid.appendChild(fileCard);
        });

        this.updatePagination();
        this.hideEmptyState();
    }

    createFileCard(file) {
        const card = document.createElement('div');
        card.className = 'file-card';
        card.dataset.fileId = file.file_id;

        const qualityClass = this.getQualityClass(file.quality_score);
        const tags = this.extractDisplayTags(file.tags);

        card.innerHTML = `
            <img src="${file.thumbnail_path || '/placeholder.jpg'}" 
                 alt="${file.file_name}" 
                 class="file-thumbnail"
                 onerror="this.src='/placeholder.jpg'">
            <div class="file-info">
                <div class="file-name" title="${file.file_name}">${this.truncateText(file.file_name, 30)}</div>
                <div class="file-tags">
                    ${tags.map(tag => `<span class="file-tag">${tag}</span>`).join('')}
                </div>
                <div class="file-meta">
                    <span class="relevance">相关性: ${file.relevance_score}</span>
                    <span class="quality-score ${qualityClass}">
                        质量: ${Math.round((file.quality_score || 0) * 100)}%
                    </span>
                </div>
            </div>
        `;

        // 添加点击事件
        card.addEventListener('click', (e) => {
            if (e.ctrlKey || e.metaKey) {
                this.toggleFileSelection(file.file_id);
            } else {
                this.selectSingleFile(file.file_id);
            }
        });

        return card;
    }

    extractDisplayTags(tags) {
        const displayTags = [];
        
        if (tags?.tags?.user) {
            displayTags.push(...tags.tags.user.slice(0, 3));
        }
        
        if (tags?.tags?.custom) {
            const customTags = Object.entries(tags.tags.custom)
                .slice(0, 2)
                .map(([key, value]) => `${key}:${value}`);
            displayTags.push(...customTags);
        }

        return displayTags.slice(0, 4); // 最多显示4个标签
    }

    getQualityClass(score) {
        if (!score) return 'quality-low';
        if (score >= 0.8) return 'quality-high';
        if (score >= 0.5) return 'quality-medium';
        return 'quality-low';
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength - 3) + '...';
    }

    toggleFileSelection(fileId) {
        const card = document.querySelector(`[data-file-id="${fileId}"]`);
        
        if (this.selectedFiles.has(fileId)) {
            this.selectedFiles.delete(fileId);
            card.classList.remove('selected');
        } else {
            this.selectedFiles.add(fileId);
            card.classList.add('selected');
        }

        this.updateBatchProcessButton();
    }

    selectSingleFile(fileId) {
        // 清除所有选择
        this.selectedFiles.clear();
        document.querySelectorAll('.file-card.selected').forEach(card => {
            card.classList.remove('selected');
        });

        // 选择当前文件
        this.selectedFiles.add(fileId);
        document.querySelector(`[data-file-id="${fileId}"]`).classList.add('selected');
        
        this.updateBatchProcessButton();
    }

    updateBatchProcessButton() {
        const batchBtn = document.getElementById('batchProcessBtn');
        const selectedCount = this.selectedFiles.size;
        
        batchBtn.disabled = selectedCount === 0;
        batchBtn.textContent = selectedCount > 0 
            ? `批量处理 (${selectedCount})` 
            : '批量处理';
    }

    addUserTag(tagValue) {
        if (!tagValue) return;

        const container = document.getElementById('selectedUserTags');
        const tagChip = document.createElement('span');
        tagChip.className = 'tag-chip';
        tagChip.innerHTML = `
            ${tagValue}
            <span class="remove" onclick="this.parentElement.remove()">×</span>
        `;
        
        container.appendChild(tagChip);
    }

    getSelectedUserTags() {
        const tags = [];
        document.querySelectorAll('#selectedUserTags .tag-chip').forEach(chip => {
            const text = chip.textContent.replace('×', '').trim();
            if (text) tags.push(text);
        });
        return tags;
    }

    addCustomTagInput() {
        const container = document.getElementById('customTagsContainer');
        const inputGroup = document.createElement('div');
        inputGroup.className = 'custom-tag-input';
        
        inputGroup.innerHTML = `
            <input type="text" placeholder="标签名" class="custom-tag-name">
            <input type="text" placeholder="标签值" class="custom-tag-value">
            <button type="button" class="btn-remove-custom-tag" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        container.appendChild(inputGroup);
    }

    getCustomTags() {
        const customTags = {};
        document.querySelectorAll('.custom-tag-input').forEach(input => {
            const name = input.querySelector('.custom-tag-name').value.trim();
            const value = input.querySelector('.custom-tag-value').value.trim();
            if (name && value) {
                customTags[name] = value;
            }
        });
        return customTags;
    }

    clearAllFilters() {
        // 清除快速搜索
        document.getElementById('quickSearch').value = '';
        
        // 清除用户标签
        document.getElementById('selectedUserTags').innerHTML = '';
        
        // 清除自定义标签
        document.getElementById('customTagsContainer').innerHTML = '';
        
        // 重置质量范围
        document.getElementById('qualityMin').value = 0;
        document.getElementById('qualityMax').value = 100;
        document.getElementById('qualityMinLabel').textContent = '0';
        document.getElementById('qualityMaxLabel').textContent = '100';
        
        // 清除日期范围
        document.getElementById('dateFrom').value = '';
        document.getElementById('dateTo').value = '';
        
        // 清除选择的文件
        this.selectedFiles.clear();
        document.querySelectorAll('.file-card.selected').forEach(card => {
            card.classList.remove('selected');
        });
        
        // 重新搜索
        this.currentPage = 1;
        this.performSearch({});
    }

    async showSearchSuggestions(query) {
        if (!query || query.length < 2) {
            document.getElementById('searchSuggestions').style.display = 'none';
            return;
        }

        try {
            const response = await fetch(`/api/v1/gallery/search/suggestions?case_id=${this.currentCaseId}&query=${encodeURIComponent(query)}&limit=10`);
            
            if (!response.ok) return;
            
            const result = await response.json();
            this.displaySearchSuggestions(result.suggestions);
            
        } catch (error) {
            console.error('获取搜索建议失败:', error);
        }
    }

    displaySearchSuggestions(suggestions) {
        const container = document.getElementById('suggestionsList');
        const suggestionsDiv = document.getElementById('searchSuggestions');
        
        if (suggestions.length === 0) {
            suggestionsDiv.style.display = 'none';
            return;
        }

        container.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.textContent = `${suggestion.tag_name}: ${suggestion.tag_value} (${suggestion.file_count})`;
            
            item.addEventListener('click', () => {
                this.applySuggestion(suggestion);
                suggestionsDiv.style.display = 'none';
            });
            
            container.appendChild(item);
        });
        
        suggestionsDiv.style.display = 'block';
    }

    applySuggestion(suggestion) {
        if (suggestion.tag_category === 'user') {
            this.addUserTag(suggestion.tag_value);
        } else if (suggestion.tag_category === 'custom') {
            this.addCustomTagInput();
            const inputs = document.querySelectorAll('.custom-tag-input');
            const lastInput = inputs[inputs.length - 1];
            lastInput.querySelector('.custom-tag-name').value = suggestion.tag_name;
            lastInput.querySelector('.custom-tag-value').value = suggestion.tag_value;
        }
    }

    showLoading() {
        document.getElementById('loadingState').style.display = 'flex';
        document.getElementById('fileGrid').style.display = 'none';
        document.getElementById('emptyState').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('fileGrid').style.display = 'grid';
    }

    showEmptyState() {
        document.getElementById('emptyState').style.display = 'flex';
        document.getElementById('fileGrid').style.display = 'none';
    }

    hideEmptyState() {
        document.getElementById('emptyState').style.display = 'none';
    }

    showError(message) {
        // 可以实现一个通知系统
        alert(message);
    }

    updatePagination() {
        const totalPages = Math.ceil(this.totalFiles / this.pageSize);
        
        document.getElementById('currentPage').textContent = this.currentPage;
        document.getElementById('totalPages').textContent = totalPages;
        
        document.getElementById('prevPageBtn').disabled = this.currentPage <= 1;
        document.getElementById('nextPageBtn').disabled = this.currentPage >= totalPages;
        
        document.getElementById('pagination').style.display = totalPages > 1 ? 'flex' : 'none';
    }

    goToPage(page) {
        if (page < 1 || page > Math.ceil(this.totalFiles / this.pageSize)) return;
        
        this.currentPage = page;
        this.currentQuery.offset = (page - 1) * this.pageSize;
        this.performSearch(this.currentQuery);
    }

    switchView(viewType) {
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        document.querySelector(`[data-view="${viewType}"]`).classList.add('active');
        
        const fileGrid = document.getElementById('fileGrid');
        if (viewType === 'list') {
            fileGrid.classList.add('list-view');
        } else {
            fileGrid.classList.remove('list-view');
        }
    }

    selectProcessor(processorName) {
        document.querySelectorAll('.processor-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        document.querySelector(`[data-processor="${processorName}"]`).classList.add('active');
        this.selectedProcessor = processorName;
    }

    async startBatchProcessing() {
        if (this.selectedFiles.size === 0 || !this.selectedProcessor) {
            alert('请选择文件和处理器');
            return;
        }

        const fileIds = Array.from(this.selectedFiles);
        
        try {
            this.showProgressModal('正在批量处理文件...');
            
            const response = await fetch('/api/v1/gallery/process/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    file_ids: fileIds,
                    processor_name: this.selectedProcessor,
                    stage: 'on_demand',
                    priority: 'normal',
                    max_concurrent: 4
                })
            });

            if (!response.ok) {
                throw new Error(`处理失败: ${response.status}`);
            }

            const result = await response.json();
            this.hideProgressModal();
            
            alert(`批量处理完成！成功: ${result.success}, 失败: ${result.failed}`);
            
            // 刷新当前搜索结果
            this.performSearch(this.currentQuery);
            
        } catch (error) {
            console.error('批量处理失败:', error);
            this.hideProgressModal();
            alert('批量处理失败，请稍后重试');
        }
    }

    showExportModal() {
        document.getElementById('exportModal').style.display = 'flex';
    }

    hideExportModal() {
        document.getElementById('exportModal').style.display = 'none';
    }

    async startExport() {
        const presetId = document.getElementById('exportPreset').value;
        const outputPath = document.getElementById('exportPath').value;
        
        if (!outputPath) {
            alert('请选择输出路径');
            return;
        }

        try {
            this.hideExportModal();
            this.showProgressModal('正在创建导出任务...');
            
            const response = await fetch('/api/v1/gallery/export/create-task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    case_id: this.currentCaseId,
                    preset_id: presetId,
                    search_query: this.currentQuery,
                    output_path: outputPath
                })
            });

            if (!response.ok) {
                throw new Error(`导出失败: ${response.status}`);
            }

            const result = await response.json();
            this.hideProgressModal();
            
            alert(`导出任务已创建！任务ID: ${result.task_id}`);
            
        } catch (error) {
            console.error('导出失败:', error);
            this.hideProgressModal();
            alert('导出失败，请稍后重试');
        }
    }

    showProgressModal(text) {
        document.getElementById('progressText').textContent = text;
        document.getElementById('progressModal').style.display = 'flex';
    }

    hideProgressModal() {
        document.getElementById('progressModal').style.display = 'none';
    }

    updateActiveFilters(query) {
        const filtersDiv = document.getElementById('activeFilters');
        const tagsDiv = document.getElementById('filterTags');
        
        const filters = [];
        
        if (query.user_tags?.length > 0) {
            query.user_tags.forEach(tag => {
                filters.push({ type: 'user', value: tag });
            });
        }
        
        if (query.custom_tags) {
            Object.entries(query.custom_tags).forEach(([key, value]) => {
                filters.push({ type: 'custom', value: `${key}:${value}` });
            });
        }
        
        if (query.quality_min || query.quality_max) {
            filters.push({ 
                type: 'quality', 
                value: `质量: ${Math.round((query.quality_min || 0) * 100)}-${Math.round((query.quality_max || 1) * 100)}%` 
            });
        }
        
        if (filters.length === 0) {
            filtersDiv.style.display = 'none';
            return;
        }
        
        tagsDiv.innerHTML = '';
        filters.forEach(filter => {
            const tag = document.createElement('span');
            tag.className = 'filter-tag';
            tag.innerHTML = `
                ${filter.value}
                <span class="remove" onclick="this.parentElement.remove()">×</span>
            `;
            tagsDiv.appendChild(tag);
        });
        
        filtersDiv.style.display = 'block';
    }
}

// 初始化标签画廊管理器
document.addEventListener('DOMContentLoaded', () => {
    window.tagGallery = new TagGalleryManager();
});
