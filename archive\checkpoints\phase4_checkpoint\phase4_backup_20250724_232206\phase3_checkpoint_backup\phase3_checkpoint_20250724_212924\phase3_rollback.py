#!/usr/bin/env python3
# 第三阶段回滚脚本
# 检查点路径: C:\Users\<USER>\mizzy_star_v0.3\phase3_checkpoint\phase3_checkpoint_20250724_212924
# 生成时间: 2025-07-24T21:29:27.630612

import shutil
from pathlib import Path

def rollback_phase3():
    print("🔄 开始回滚到第三阶段检查点...")
    
    project_root = Path(__file__).parent.parent
    checkpoint_path = Path("C:\Users\<USER>\mizzy_star_v0.3\phase3_checkpoint\phase3_checkpoint_20250724_212924")
    
    try:
        # 回滚前端目录
        backup_frontend = checkpoint_path / "frontend_backup"
        target_frontend = project_root / "frontend"
        
        if backup_frontend.exists():
            if target_frontend.exists():
                shutil.rmtree(target_frontend)
                print("🗑️ 删除现有frontend目录")
            
            if (backup_frontend / "package.json").exists():
                # 完整备份恢复
                shutil.copytree(backup_frontend, target_frontend)
                print("✅ 恢复完整frontend目录")
            else:
                # 关键文件恢复
                target_frontend.mkdir(exist_ok=True)
                for backup_file in backup_frontend.glob("*"):
                    if backup_file.is_file():
                        shutil.copy2(backup_file, target_frontend / backup_file.name)
                print("✅ 恢复frontend关键文件")
        else:
            print("⚠️ 前端备份不存在")
        
        print("🎉 第三阶段回滚完成！")
        
    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    rollback_phase3()
