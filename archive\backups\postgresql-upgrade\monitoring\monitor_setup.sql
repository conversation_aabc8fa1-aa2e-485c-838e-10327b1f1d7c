-- PostgreSQL监控配置
-- 创建监控视图和函数

-- 1. 数据库连接监控视图
CREATE OR REPLACE VIEW v_connection_stats AS
SELECT 
    datname as database_name,
    numbackends as active_connections,
    xact_commit as transactions_committed,
    xact_rollback as transactions_rolled_back,
    blks_read as blocks_read,
    blks_hit as blocks_hit,
    CASE 
        WHEN blks_read + blks_hit > 0 
        THEN ROUND((blks_hit::float / (blks_read + blks_hit)) * 100, 2)
        ELSE 0 
    END as cache_hit_ratio,
    tup_returned as tuples_returned,
    tup_fetched as tuples_fetched,
    tup_inserted as tuples_inserted,
    tup_updated as tuples_updated,
    tup_deleted as tuples_deleted,
    stats_reset
FROM pg_stat_database 
WHERE datname = 'mizzy_main';

-- 2. 表统计监控视图
CREATE OR REPLACE VIEW v_table_stats AS
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    CASE 
        WHEN n_live_tup + n_dead_tup > 0 
        THEN ROUND((n_dead_tup::float / (n_live_tup + n_dead_tup)) * 100, 2)
        ELSE 0 
    END as dead_tuple_ratio,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze,
    vacuum_count,
    autovacuum_count,
    analyze_count,
    autoanalyze_count
FROM pg_stat_user_tables
ORDER BY n_live_tup DESC;

-- 3. 索引使用统计视图
CREATE OR REPLACE VIEW v_index_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read as index_tuples_read,
    idx_tup_fetch as index_tuples_fetched,
    CASE 
        WHEN idx_tup_read > 0 
        THEN ROUND((idx_tup_fetch::float / idx_tup_read) * 100, 2)
        ELSE 0 
    END as index_efficiency,
    idx_scan as index_scans
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 4. 查询性能监控视图（需要pg_stat_statements扩展）
-- 注意：这需要在postgresql.conf中启用pg_stat_statements
CREATE OR REPLACE VIEW v_slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    max_time,
    min_time,
    stddev_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 10  -- 只显示平均执行时间超过10ms的查询
ORDER BY mean_time DESC
LIMIT 20;

-- 5. 数据库大小监控函数
CREATE OR REPLACE FUNCTION get_database_size_info()
RETURNS TABLE(
    database_name text,
    size_bytes bigint,
    size_pretty text,
    table_count bigint,
    index_count bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        current_database()::text,
        pg_database_size(current_database()),
        pg_size_pretty(pg_database_size(current_database())),
        (SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public'),
        (SELECT count(*) FROM pg_indexes WHERE schemaname = 'public');
END;
$$ LANGUAGE plpgsql;

-- 6. 表空间使用监控函数
CREATE OR REPLACE FUNCTION get_table_sizes()
RETURNS TABLE(
    table_name text,
    table_size_bytes bigint,
    table_size_pretty text,
    index_size_bytes bigint,
    index_size_pretty text,
    total_size_bytes bigint,
    total_size_pretty text,
    row_count bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.tablename::text,
        pg_table_size(t.tablename::regclass),
        pg_size_pretty(pg_table_size(t.tablename::regclass)),
        pg_indexes_size(t.tablename::regclass),
        pg_size_pretty(pg_indexes_size(t.tablename::regclass)),
        pg_total_relation_size(t.tablename::regclass),
        pg_size_pretty(pg_total_relation_size(t.tablename::regclass)),
        (SELECT n_live_tup FROM pg_stat_user_tables WHERE tablename = t.tablename)
    FROM pg_tables t
    WHERE t.schemaname = 'public'
    ORDER BY pg_total_relation_size(t.tablename::regclass) DESC;
END;
$$ LANGUAGE plpgsql;

-- 7. 系统健康检查函数
CREATE OR REPLACE FUNCTION system_health_check()
RETURNS TABLE(
    check_name text,
    status text,
    value text,
    recommendation text
) AS $$
DECLARE
    conn_count int;
    cache_hit_ratio numeric;
    dead_tuple_ratio numeric;
    db_size_gb numeric;
BEGIN
    -- 检查连接数
    SELECT numbackends INTO conn_count FROM pg_stat_database WHERE datname = current_database();
    
    IF conn_count > 80 THEN
        RETURN QUERY SELECT 'Connection Count'::text, 'WARNING'::text, conn_count::text, 'Consider connection pooling'::text;
    ELSE
        RETURN QUERY SELECT 'Connection Count'::text, 'OK'::text, conn_count::text, 'Normal'::text;
    END IF;
    
    -- 检查缓存命中率
    SELECT ROUND((blks_hit::float / (blks_read + blks_hit)) * 100, 2) 
    INTO cache_hit_ratio 
    FROM pg_stat_database 
    WHERE datname = current_database() AND blks_read + blks_hit > 0;
    
    IF cache_hit_ratio < 95 THEN
        RETURN QUERY SELECT 'Cache Hit Ratio'::text, 'WARNING'::text, cache_hit_ratio::text || '%', 'Consider increasing shared_buffers'::text;
    ELSE
        RETURN QUERY SELECT 'Cache Hit Ratio'::text, 'OK'::text, cache_hit_ratio::text || '%', 'Good performance'::text;
    END IF;
    
    -- 检查死元组比例
    SELECT MAX(CASE WHEN n_live_tup + n_dead_tup > 0 THEN (n_dead_tup::float / (n_live_tup + n_dead_tup)) * 100 ELSE 0 END)
    INTO dead_tuple_ratio
    FROM pg_stat_user_tables;
    
    IF dead_tuple_ratio > 20 THEN
        RETURN QUERY SELECT 'Dead Tuple Ratio'::text, 'WARNING'::text, ROUND(dead_tuple_ratio, 2)::text || '%', 'Consider running VACUUM'::text;
    ELSE
        RETURN QUERY SELECT 'Dead Tuple Ratio'::text, 'OK'::text, ROUND(dead_tuple_ratio, 2)::text || '%', 'Normal'::text;
    END IF;
    
    -- 检查数据库大小
    SELECT ROUND(pg_database_size(current_database()) / 1024.0 / 1024.0 / 1024.0, 2) INTO db_size_gb;
    
    IF db_size_gb > 10 THEN
        RETURN QUERY SELECT 'Database Size'::text, 'INFO'::text, db_size_gb::text || ' GB', 'Monitor disk space'::text;
    ELSE
        RETURN QUERY SELECT 'Database Size'::text, 'OK'::text, db_size_gb::text || ' GB', 'Normal'::text;
    END IF;
    
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- 8. 创建监控日志表
CREATE TABLE IF NOT EXISTS monitoring_log (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value NUMERIC,
    metric_unit VARCHAR(20),
    status VARCHAR(20),
    details TEXT
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_monitoring_log_timestamp ON monitoring_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_monitoring_log_metric ON monitoring_log(metric_name);

-- 9. 监控数据收集函数
CREATE OR REPLACE FUNCTION collect_monitoring_data()
RETURNS void AS $$
DECLARE
    conn_count int;
    cache_hit_ratio numeric;
    db_size_mb numeric;
    active_queries int;
BEGIN
    -- 收集连接数
    SELECT numbackends INTO conn_count FROM pg_stat_database WHERE datname = current_database();
    INSERT INTO monitoring_log (metric_name, metric_value, metric_unit, status)
    VALUES ('active_connections', conn_count, 'count', CASE WHEN conn_count > 80 THEN 'warning' ELSE 'ok' END);
    
    -- 收集缓存命中率
    SELECT ROUND((blks_hit::float / (blks_read + blks_hit)) * 100, 2) 
    INTO cache_hit_ratio 
    FROM pg_stat_database 
    WHERE datname = current_database() AND blks_read + blks_hit > 0;
    
    INSERT INTO monitoring_log (metric_name, metric_value, metric_unit, status)
    VALUES ('cache_hit_ratio', cache_hit_ratio, 'percent', CASE WHEN cache_hit_ratio < 95 THEN 'warning' ELSE 'ok' END);
    
    -- 收集数据库大小
    SELECT ROUND(pg_database_size(current_database()) / 1024.0 / 1024.0, 2) INTO db_size_mb;
    INSERT INTO monitoring_log (metric_name, metric_value, metric_unit, status)
    VALUES ('database_size', db_size_mb, 'MB', 'info');
    
    -- 收集活跃查询数
    SELECT count(*) INTO active_queries FROM pg_stat_activity WHERE state = 'active' AND datname = current_database();
    INSERT INTO monitoring_log (metric_name, metric_value, metric_unit, status)
    VALUES ('active_queries', active_queries, 'count', CASE WHEN active_queries > 50 THEN 'warning' ELSE 'ok' END);
    
END;
$$ LANGUAGE plpgsql;

-- 10. 清理旧监控数据的函数
CREATE OR REPLACE FUNCTION cleanup_monitoring_data(days_to_keep int DEFAULT 30)
RETURNS int AS $$
DECLARE
    deleted_count int;
BEGIN
    DELETE FROM monitoring_log 
    WHERE timestamp < NOW() - INTERVAL '1 day' * days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建监控数据收集的定时任务（需要pg_cron扩展）
-- SELECT cron.schedule('collect-monitoring', '*/5 * * * *', 'SELECT collect_monitoring_data();');
-- SELECT cron.schedule('cleanup-monitoring', '0 2 * * *', 'SELECT cleanup_monitoring_data(30);');

COMMENT ON VIEW v_connection_stats IS '数据库连接统计监控视图';
COMMENT ON VIEW v_table_stats IS '表统计监控视图';
COMMENT ON VIEW v_index_stats IS '索引使用统计视图';
COMMENT ON FUNCTION get_database_size_info() IS '获取数据库大小信息';
COMMENT ON FUNCTION get_table_sizes() IS '获取表大小信息';
COMMENT ON FUNCTION system_health_check() IS '系统健康检查';
COMMENT ON FUNCTION collect_monitoring_data() IS '收集监控数据';
COMMENT ON FUNCTION cleanup_monitoring_data(int) IS '清理旧监控数据';
