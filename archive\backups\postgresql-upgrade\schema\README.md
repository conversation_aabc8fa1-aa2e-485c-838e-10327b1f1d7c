# 数据库Schema设计

## 概述
本目录包含PostgreSQL数据库的Schema定义文件，针对Mizzy Star项目进行了优化。

## 文件说明
- `001_initial_schema.sql` - 主要表结构定义
- `002_indexes.sql` - 索引定义和优化
- `003_constraints.sql` - 约束和触发器
- `004_functions.sql` - 存储过程和函数
- `schema_design_doc.md` - 详细设计文档

## 核心设计原则
1. **JSONB优化**: 使用JSONB字段存储标签数据，配合GIN索引
2. **性能优先**: 针对查询模式优化的索引策略
3. **扩展性**: 为未来AI向量搜索预留字段
4. **兼容性**: 与现有SQLite数据结构保持兼容

## 主要表结构
- `files` - 核心文件表，包含JSONB标签字段
- `custom_tags` - 自定义标签表
- `file_custom_tags` - 文件标签关联表
- `tag_cache` - 标签缓存表（性能优化）

## 性能特性
- GIN索引支持高效JSONB查询
- 复合索引优化常用查询模式
- 全文搜索支持
- 为大数据量设计的分区策略
