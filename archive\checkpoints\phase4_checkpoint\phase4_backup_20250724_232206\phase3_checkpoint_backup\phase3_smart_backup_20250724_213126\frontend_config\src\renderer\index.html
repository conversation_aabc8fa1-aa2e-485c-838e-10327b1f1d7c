<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>迷星 Mizzy Star</title>
    <link rel="stylesheet" href="../styles/output.css">
    <link rel="stylesheet" href="https://unpkg.com/filepond/dist/filepond.css">
    <link rel="stylesheet" href="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css">
    <link rel="icon" type="image/png" href="../../assets/icon.png">
</head>
<body class="h-screen overflow-hidden">
    <div id="app" class="h-full flex">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="p-4 border-b border-secondary-200">
                <h1 class="text-xl font-bold text-primary-700">迷星 Mizzy Star</h1>
                <p class="text-sm text-secondary-600">智能标签数据管理</p>
            </div>
            
            <nav class="flex-1 p-4">
                <ul class="space-y-2">
                    <li>
                        <button id="nav-cases" class="nav-item active w-full text-left px-3 py-2 rounded-lg hover:bg-secondary-100 transition-colors">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                案例管理
                            </span>
                        </button>
                    </li>
                    <li>
                        <button id="nav-trash" class="nav-item w-full text-left px-3 py-2 rounded-lg hover:bg-secondary-100 transition-colors">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                回收站
                            </span>
                        </button>
                    </li>
                </ul>
            </nav>
            
            <div class="p-4 border-t border-secondary-200">
                <button id="new-case-btn" class="btn-primary w-full">
                    <span class="flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新建案例
                    </span>
                </button>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 案例管理页面 -->
            <div id="cases-page" class="page active h-full flex flex-col">
                <div class="p-6 border-b border-secondary-200 bg-white">
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-bold text-secondary-900">案例管理</h2>
                        <div class="flex items-center space-x-3">
                            <div class="relative">
                                <input type="text" id="search-input" placeholder="搜索案例..." class="input w-64 pl-10">
                                <svg class="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <button id="refresh-btn" class="btn-secondary">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="flex-1 p-6 overflow-auto">
                    <div id="cases-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- 案例卡片将在这里动态生成 -->
                    </div>
                    
                    <div id="loading" class="hidden text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                        <p class="mt-2 text-secondary-600">加载中...</p>
                    </div>
                    
                    <div id="empty-state" class="hidden text-center py-16">
                        <svg class="w-16 h-16 mx-auto text-secondary-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-secondary-900 mb-2">暂无案例</h3>
                        <p class="text-secondary-600 mb-4">点击"新建案例"按钮创建您的第一个案例</p>
                    </div>
                </div>
            </div>
            
            <!-- 回收站页面 -->
            <div id="trash-page" class="page hidden h-full flex flex-col">
                <div class="p-6 border-b border-secondary-200 bg-white">
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-bold text-secondary-900">回收站</h2>
                        <button id="empty-trash-btn" class="btn-danger">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                清空回收站
                            </span>
                        </button>
                    </div>
                </div>
                
                <div class="flex-1 p-6 overflow-auto">
                    <div id="trash-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- 回收站案例卡片将在这里动态生成 -->
                    </div>
                    
                    <div id="trash-empty-state" class="hidden text-center py-16">
                        <svg class="w-16 h-16 mx-auto text-secondary-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-secondary-900 mb-2">回收站为空</h3>
                        <p class="text-secondary-600">已删除的案例将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框 -->
    <div id="modal-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div id="modal-content" class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-auto">
                <!-- 模态框内容将在这里动态生成 -->
            </div>
        </div>
    </div>
    
    <!-- 通知容器 -->
    <div id="notifications" class="fixed top-4 right-4 z-50 space-y-2">
        <!-- 通知将在这里动态生成 -->
    </div>
    
    <!-- 脚本 -->
    <script src="https://unpkg.com/filepond-plugin-file-validate-type/dist/filepond-plugin-file-validate-type.js"></script>
    <script src="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.js"></script>
    <script src="https://unpkg.com/filepond-plugin-image-resize/dist/filepond-plugin-image-resize.js"></script>
    <script src="https://unpkg.com/filepond/dist/filepond.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/components.js"></script>
    <script src="js/app.js"></script>
</body>
</html> 