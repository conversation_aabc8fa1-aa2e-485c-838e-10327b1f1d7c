# PostgreSQL自动备份脚本
# 用于Mizzy Star生产环境

param(
    [string]$BackupDir = "C:\mizzy_star_production\backups",
    [string]$Host = "127.0.0.1",
    [string]$Port = "5432",
    [string]$Database = "mizzy_main",
    [string]$User = "mizzy_user",
    [string]$Password = "MizzyStarProd2024!",
    [int]$RetentionDays = 7
)

# 设置环境变量
$env:PGPASSWORD = $Password

# 创建备份目录
if (!(Test-Path $BackupDir)) {
    New-Item -ItemType Directory -Path $BackupDir -Force
    Write-Host "✅ 创建备份目录: $BackupDir"
}

# 生成备份文件名
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupFile = Join-Path $BackupDir "mizzy_main_backup_$Timestamp.sql"
$CompressedFile = "$BackupFile.gz"

Write-Host "🚀 开始数据库备份..."
Write-Host "数据库: $Database"
Write-Host "备份文件: $BackupFile"

try {
    # 执行pg_dump备份
    $pg_dump_args = @(
        "--host=$Host",
        "--port=$Port", 
        "--username=$User",
        "--dbname=$Database",
        "--verbose",
        "--clean",
        "--if-exists",
        "--create",
        "--format=custom",
        "--file=$BackupFile"
    )
    
    # 使用Docker执行备份
    $docker_args = @(
        "exec",
        "mizzy_postgres",
        "pg_dump"
    ) + $pg_dump_args
    
    & docker $docker_args
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 数据库备份成功"
        
        # 获取备份文件大小
        $FileSize = (Get-Item $BackupFile).Length
        $FileSizeMB = [math]::Round($FileSize / 1MB, 2)
        Write-Host "📁 备份文件大小: $FileSizeMB MB"
        
        # 压缩备份文件（如果有gzip）
        if (Get-Command gzip -ErrorAction SilentlyContinue) {
            Write-Host "🗜️ 压缩备份文件..."
            & gzip $BackupFile
            
            if (Test-Path $CompressedFile) {
                $CompressedSize = (Get-Item $CompressedFile).Length
                $CompressedSizeMB = [math]::Round($CompressedSize / 1MB, 2)
                Write-Host "✅ 压缩完成: $CompressedSizeMB MB"
                
                # 计算压缩比
                $CompressionRatio = [math]::Round((1 - $CompressedSize / $FileSize) * 100, 1)
                Write-Host "📊 压缩比: $CompressionRatio%"
            }
        } else {
            Write-Host "⚠️ gzip未找到，跳过压缩"
        }
        
        # 验证备份完整性
        Write-Host "🔍 验证备份完整性..."
        $verify_args = @(
            "exec",
            "mizzy_postgres",
            "pg_restore",
            "--list",
            $BackupFile
        )
        
        $verification = & docker $verify_args 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 备份文件完整性验证通过"
        } else {
            Write-Host "❌ 备份文件完整性验证失败"
            Write-Host $verification
        }
        
    } else {
        Write-Host "❌ 数据库备份失败"
        exit 1
    }
    
} catch {
    Write-Host "❌ 备份过程中发生错误: $($_.Exception.Message)"
    exit 1
}

# 清理旧备份文件
Write-Host "🧹 清理旧备份文件..."
$CutoffDate = (Get-Date).AddDays(-$RetentionDays)
$OldFiles = Get-ChildItem -Path $BackupDir -Filter "mizzy_main_backup_*.sql*" | Where-Object { $_.CreationTime -lt $CutoffDate }

if ($OldFiles.Count -gt 0) {
    Write-Host "删除 $($OldFiles.Count) 个旧备份文件:"
    foreach ($file in $OldFiles) {
        Write-Host "  - $($file.Name)"
        Remove-Item $file.FullName -Force
    }
    Write-Host "✅ 旧备份文件清理完成"
} else {
    Write-Host "✅ 没有需要清理的旧备份文件"
}

# 生成备份报告
$ReportFile = Join-Path $BackupDir "backup_report_$Timestamp.txt"
$Report = @"
================================================================================
Mizzy Star PostgreSQL备份报告
================================================================================
备份时间: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
数据库: $Database
主机: $Host:$Port
用户: $User

备份文件: $BackupFile
文件大小: $FileSizeMB MB
$(if (Test-Path $CompressedFile) { "压缩文件: $CompressedFile`n压缩大小: $CompressedSizeMB MB`n压缩比: $CompressionRatio%" } else { "未压缩" })

备份状态: 成功
验证状态: $(if ($LASTEXITCODE -eq 0) { "通过" } else { "失败" })

保留策略: $RetentionDays 天
清理文件: $($OldFiles.Count) 个

备份完成时间: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
================================================================================
"@

$Report | Out-File -FilePath $ReportFile -Encoding UTF8
Write-Host "📋 备份报告已保存: $ReportFile"

Write-Host "🎉 备份流程完成!"

# 清理环境变量
Remove-Item Env:PGPASSWORD
