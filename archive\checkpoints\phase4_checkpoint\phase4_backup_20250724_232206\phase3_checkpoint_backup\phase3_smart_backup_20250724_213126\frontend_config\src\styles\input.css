@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  * {
    @apply box-border;
  }
  
  body {
    @apply font-sans text-secondary-900 bg-secondary-50;
  }
  
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-200 text-secondary-800 hover:bg-secondary-300 focus:ring-secondary-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-sm border border-secondary-200 p-6;
  }
  
  .input {
    @apply w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .sidebar {
    @apply w-64 bg-white border-r border-secondary-200 h-full flex flex-col;
  }
  
  .main-content {
    @apply flex-1 bg-secondary-50 overflow-auto;
  }
}

/* FilePond样式定制 */
.filepond--root {
  @apply font-sans;
}

.filepond--drop-label {
  @apply text-secondary-600;
}

.filepond--label-action {
  @apply text-primary-600 hover:text-primary-700;
}

.filepond--panel-root {
  @apply bg-secondary-50 border border-secondary-200;
}

.filepond--item-panel {
  @apply bg-white border border-secondary-200;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(10px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
} 