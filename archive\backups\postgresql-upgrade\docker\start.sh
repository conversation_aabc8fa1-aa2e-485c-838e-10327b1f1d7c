#!/bin/bash

# Mizzy Star PostgreSQL启动脚本
# 用于快速启动和配置PostgreSQL环境

set -e

echo "🚀 Mizzy Star PostgreSQL环境启动脚本"
echo "=================================="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件设置安全的密码"
    echo "   默认密码仅用于开发环境！"
fi

# 检查是否需要重新构建
if [ "$1" = "--rebuild" ]; then
    echo "🔄 重新构建容器..."
    docker-compose down -v
    docker-compose build --no-cache
fi

# 启动服务
echo "🐘 启动PostgreSQL服务..."
docker-compose up -d postgres

# 等待PostgreSQL启动
echo "⏳ 等待PostgreSQL启动..."
timeout=60
counter=0

while ! docker-compose exec -T postgres pg_isready -U mizzy_user -d mizzy_main > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ 超时: PostgreSQL启动失败"
        docker-compose logs postgres
        exit 1
    fi
    echo "   等待中... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

echo "✅ PostgreSQL启动成功!"

# 显示连接信息
echo ""
echo "📊 连接信息:"
echo "   主机: localhost"
echo "   端口: 5432"
echo "   数据库: mizzy_main"
echo "   用户: mizzy_user"
echo ""

# 测试连接
echo "🔍 测试数据库连接..."
if docker-compose exec -T postgres psql -U mizzy_user -d mizzy_main -c "SELECT version();" > /dev/null 2>&1; then
    echo "✅ 数据库连接测试成功!"
else
    echo "❌ 数据库连接测试失败"
    exit 1
fi

# 显示扩展信息
echo ""
echo "🔧 已安装的扩展:"
docker-compose exec -T postgres psql -U mizzy_user -d mizzy_main -c "SELECT name, default_version, installed_version FROM pg_available_extensions WHERE installed_version IS NOT NULL;"

# 显示配置信息
echo ""
echo "⚙️  关键配置:"
docker-compose exec -T postgres psql -U mizzy_user -d mizzy_main -c "SELECT name, setting, unit FROM pg_settings WHERE name IN ('shared_buffers', 'effective_cache_size', 'work_mem', 'max_connections');"

# 可选启动pgAdmin
if [ "$1" = "--with-admin" ]; then
    echo ""
    echo "🖥️  启动pgAdmin..."
    docker-compose --profile admin up -d pgadmin
    echo "   pgAdmin访问地址: http://localhost:8080"
    echo "   默认邮箱: <EMAIL>"
    echo "   默认密码: admin123"
fi

echo ""
echo "🎉 PostgreSQL环境启动完成!"
echo ""
echo "📋 常用命令:"
echo "   连接数据库: docker-compose exec postgres psql -U mizzy_user -d mizzy_main"
echo "   查看日志:   docker-compose logs -f postgres"
echo "   停止服务:   docker-compose down"
echo "   重启服务:   docker-compose restart postgres"
echo ""
echo "📚 更多信息请查看 README.md"
