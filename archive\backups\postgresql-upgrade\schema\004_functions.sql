-- PostgreSQL存储过程和函数定义
-- 提供高性能的标签查询和数据操作功能

-- =============================================================================
-- 标签查询函数
-- =============================================================================

-- 高性能标签搜索函数
CREATE OR REPLACE FUNCTION search_files_by_tags(
    p_metadata_filters JSONB DEFAULT NULL,
    p_user_tags TEXT[] DEFAULT NULL,
    p_ai_tags TEXT[] DEFAULT NULL,
    p_quality_min DECIMAL DEFAULT NULL,
    p_quality_max DECIMAL DEFAULT NULL,
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE(
    file_id INTEGER,
    file_name VARCHAR,
    file_path TEXT,
    quality_score DECIMAL,
    created_at TIMESTAMP WITH TIME ZONE,
    tags JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.id,
        f.file_name,
        f.file_path,
        f.quality_score,
        f.created_at,
        f.tags
    FROM files f
    WHERE 
        -- 元数据过滤
        (p_metadata_filters IS NULL OR f.tags->'tags'->'metadata' @> p_metadata_filters)
        -- 用户标签过滤
        AND (p_user_tags IS NULL OR f.tags->'tags'->'user' ?| p_user_tags)
        -- AI标签过滤
        AND (p_ai_tags IS NULL OR f.tags->'tags'->'ai' ?| p_ai_tags)
        -- 质量分数过滤
        AND (p_quality_min IS NULL OR f.quality_score >= p_quality_min)
        AND (p_quality_max IS NULL OR f.quality_score <= p_quality_max)
    ORDER BY f.quality_score DESC NULLS LAST, f.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- 全文搜索函数
CREATE OR REPLACE FUNCTION search_files_fulltext(
    p_search_query TEXT,
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE(
    file_id INTEGER,
    file_name VARCHAR,
    file_path TEXT,
    rank REAL,
    tags JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.id,
        f.file_name,
        f.file_path,
        ts_rank(f.search_vector, plainto_tsquery('english', p_search_query)) as rank,
        f.tags
    FROM files f
    WHERE f.search_vector @@ plainto_tsquery('english', p_search_query)
    ORDER BY rank DESC, f.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- 获取标签统计信息
CREATE OR REPLACE FUNCTION get_tag_statistics()
RETURNS TABLE(
    category VARCHAR,
    tag_name VARCHAR,
    tag_value VARCHAR,
    file_count INTEGER,
    percentage DECIMAL
) AS $$
DECLARE
    total_files INTEGER;
BEGIN
    -- 获取总文件数
    SELECT COUNT(*) INTO total_files FROM files;
    
    RETURN QUERY
    SELECT 
        tc.tag_category::VARCHAR,
        tc.tag_name::VARCHAR,
        tc.tag_value::VARCHAR,
        tc.file_count,
        ROUND((tc.file_count::DECIMAL / NULLIF(total_files, 0)) * 100, 2) as percentage
    FROM tag_cache tc
    ORDER BY tc.file_count DESC, tc.tag_category, tc.tag_name;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- 数据迁移和维护函数
-- =============================================================================

-- 重建标签缓存函数
CREATE OR REPLACE FUNCTION rebuild_tag_cache()
RETURNS INTEGER AS $$
DECLARE
    processed_count INTEGER := 0;
    file_record RECORD;
BEGIN
    -- 清空现有标签缓存
    DELETE FROM tag_cache;
    
    -- 重新构建标签缓存
    FOR file_record IN SELECT id, tags FROM files WHERE tags IS NOT NULL LOOP
        -- 触发标签缓存维护
        UPDATE files SET tags = tags WHERE id = file_record.id;
        processed_count := processed_count + 1;
    END LOOP;
    
    RETURN processed_count;
END;
$$ LANGUAGE plpgsql;

-- 批量更新文件标签函数
CREATE OR REPLACE FUNCTION batch_update_file_tags(
    p_file_ids INTEGER[],
    p_tag_updates JSONB
)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
BEGIN
    UPDATE files 
    SET tags = tags || p_tag_updates,
        updated_at = NOW()
    WHERE id = ANY(p_file_ids);
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- 智能标签建议函数
CREATE OR REPLACE FUNCTION suggest_tags_for_file(
    p_file_id INTEGER,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE(
    suggested_tag VARCHAR,
    confidence_score DECIMAL,
    reason TEXT
) AS $$
DECLARE
    file_tags JSONB;
    camera_make TEXT;
    camera_model TEXT;
BEGIN
    -- 获取文件标签
    SELECT tags INTO file_tags FROM files WHERE id = p_file_id;
    
    IF file_tags IS NULL THEN
        RETURN;
    END IF;
    
    -- 提取相机信息
    camera_make := file_tags->'tags'->'metadata'->>'camera_make';
    camera_model := file_tags->'tags'->'metadata'->>'camera_model';
    
    -- 基于相机型号建议标签
    IF camera_make IS NOT NULL THEN
        RETURN QUERY
        SELECT 
            DISTINCT tc.tag_value::VARCHAR,
            0.8::DECIMAL,
            ('基于相机品牌: ' || camera_make)::TEXT
        FROM tag_cache tc
        JOIN files f ON f.id = ANY(tc.file_ids)
        WHERE tc.tag_category = 'metadata' 
        AND tc.tag_name = 'camera_make'
        AND tc.tag_value = camera_make
        AND f.id != p_file_id
        LIMIT p_limit;
    END IF;
    
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- 性能分析函数
-- =============================================================================

-- 查询性能分析函数
CREATE OR REPLACE FUNCTION analyze_query_performance(
    p_query_type VARCHAR DEFAULT 'all'
)
RETURNS TABLE(
    query_type VARCHAR,
    avg_duration_ms DECIMAL,
    total_calls BIGINT,
    cache_hit_ratio DECIMAL
) AS $$
BEGIN
    -- 这里可以添加查询性能分析逻辑
    -- 目前返回示例数据
    RETURN QUERY
    SELECT 
        'tag_search'::VARCHAR,
        15.5::DECIMAL,
        1000::BIGINT,
        95.2::DECIMAL
    UNION ALL
    SELECT 
        'fulltext_search'::VARCHAR,
        8.3::DECIMAL,
        500::BIGINT,
        98.1::DECIMAL;
END;
$$ LANGUAGE plpgsql;

-- 数据库健康检查函数
CREATE OR REPLACE FUNCTION database_health_check()
RETURNS TABLE(
    check_name VARCHAR,
    status VARCHAR,
    details TEXT,
    recommendation TEXT
) AS $$
DECLARE
    total_files INTEGER;
    cached_tags INTEGER;
    orphaned_tags INTEGER;
BEGIN
    -- 检查文件数量
    SELECT COUNT(*) INTO total_files FROM files;
    
    -- 检查标签缓存
    SELECT COUNT(*) INTO cached_tags FROM tag_cache;
    
    -- 检查孤立标签
    SELECT COUNT(*) INTO orphaned_tags 
    FROM tag_cache tc 
    WHERE NOT EXISTS (
        SELECT 1 FROM files f WHERE f.id = ANY(tc.file_ids)
    );
    
    -- 返回检查结果
    RETURN QUERY
    SELECT 
        'total_files'::VARCHAR,
        CASE WHEN total_files > 0 THEN 'OK' ELSE 'WARNING' END::VARCHAR,
        ('总文件数: ' || total_files)::TEXT,
        CASE WHEN total_files = 0 THEN '建议导入文件数据' ELSE '文件数量正常' END::TEXT
    UNION ALL
    SELECT 
        'tag_cache'::VARCHAR,
        CASE WHEN cached_tags > 0 THEN 'OK' ELSE 'WARNING' END::VARCHAR,
        ('缓存标签数: ' || cached_tags)::TEXT,
        CASE WHEN cached_tags = 0 THEN '建议重建标签缓存' ELSE '标签缓存正常' END::TEXT
    UNION ALL
    SELECT 
        'orphaned_tags'::VARCHAR,
        CASE WHEN orphaned_tags = 0 THEN 'OK' ELSE 'WARNING' END::VARCHAR,
        ('孤立标签数: ' || orphaned_tags)::TEXT,
        CASE WHEN orphaned_tags > 0 THEN '建议清理孤立标签' ELSE '无孤立标签' END::TEXT;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- 数据导出函数
-- =============================================================================

-- 导出文件标签为JSON
CREATE OR REPLACE FUNCTION export_file_tags_json(
    p_file_ids INTEGER[] DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_agg(
        json_build_object(
            'id', f.id,
            'file_name', f.file_name,
            'file_path', f.file_path,
            'tags', f.tags,
            'created_at', f.created_at
        )
    ) INTO result
    FROM files f
    WHERE p_file_ids IS NULL OR f.id = ANY(p_file_ids);
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- 权限和安全函数
-- =============================================================================

-- 创建只读用户视图
CREATE OR REPLACE VIEW files_readonly AS
SELECT 
    id,
    file_name,
    file_type,
    file_path,
    width,
    height,
    created_at,
    taken_at,
    quality_score,
    tags
FROM files;

-- 函数创建完成日志
DO $$
BEGIN
    RAISE NOTICE 'PostgreSQL函数和存储过程创建完成!';
    RAISE NOTICE '- 标签查询函数: 高性能标签搜索和全文搜索';
    RAISE NOTICE '- 维护函数: 标签缓存重建和批量更新';
    RAISE NOTICE '- 分析函数: 性能分析和健康检查';
    RAISE NOTICE '- 工具函数: 数据导出和标签建议';
    RAISE NOTICE '- 安全视图: 只读访问视图';
END
$$;
