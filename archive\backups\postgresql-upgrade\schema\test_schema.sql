-- 测试Schema创建 - 简化版本用于验证
-- 测试JSONB和基本表结构

-- 测试枚举类型创建
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'case_status') THEN
        CREATE TYPE case_status AS ENUM ('active', 'deleted', 'permanently_deleted');
    END IF;
END $$;

-- 测试基本表创建
CREATE TABLE IF NOT EXISTS test_files (
    id SERIAL PRIMARY KEY,
    file_name VARCHAR(255),
    file_path TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    quality_score DECIMAL(5,2),
    tags JSONB
);

-- 测试JSONB索引创建
CREATE INDEX IF NOT EXISTS idx_test_files_tags_gin ON test_files USING GIN (tags);

-- 测试插入JSONB数据
INSERT INTO test_files (file_name, file_path, quality_score, tags) VALUES 
(
    'test_image.jpg',
    '/path/to/test_image.jpg',
    85.5,
    '{
        "properties": {
            "filename": "test_image.jpg",
            "qualityScore": 85.5,
            "fileSize": 2048576
        },
        "tags": {
            "metadata": {
                "fileType": "image/jpeg",
                "camera_make": "SONY",
                "camera_model": "ILCE-7RM4"
            },
            "user": ["测试", "重要"],
            "ai": ["高质量"]
        }
    }'::jsonb
);

-- 测试JSONB查询
SELECT 
    id,
    file_name,
    tags->'properties'->>'qualityScore' as quality_from_json,
    tags->'tags'->'metadata'->>'camera_make' as camera_make,
    tags->'tags'->'user' as user_tags
FROM test_files;

-- 测试GIN索引查询
SELECT id, file_name 
FROM test_files 
WHERE tags->'tags'->'user' ? '重要';

-- 测试元数据查询
SELECT id, file_name 
FROM test_files 
WHERE tags->'tags'->'metadata'->>'camera_make' = 'SONY';

-- 显示测试结果
SELECT 'Schema测试完成!' as result;
