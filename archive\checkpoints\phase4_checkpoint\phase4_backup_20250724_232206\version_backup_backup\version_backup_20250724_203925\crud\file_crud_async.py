# src/crud/file_crud_async.py
"""
异步文件CRUD操作
支持并发文件上传和处理，提升性能
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError

from .. import models, schemas


async def create_file_for_case_async(
    db: AsyncSession, 
    case_id: int, 
    file: schemas.FileCreate
) -> Optional[models.File]:
    """
    异步为指定的活跃案例创建文件记录：
    1. 在主数据库中找到活跃案例
    2. 在主数据库中创建文件记录，关联到案例
    """
    # 步骤1: 查找活跃案例
    result = await db.execute(
        select(models.Case)
        .where(models.Case.id == case_id)
        .where(models.Case.status == models.CaseStatus.ACTIVE)
    )
    db_case = result.scalar_one_or_none()

    if not db_case:
        return None

    # 步骤2: 在主数据库中创建文件记录
    try:
        # 创建文件记录并关联到案例
        file_data = file.model_dump()
        file_data['case_id'] = case_id  # 设置外键关系
        
        db_file = models.File(**file_data)
        db.add(db_file)
        await db.commit()
        await db.refresh(db_file)
        
        return db_file
    except SQLAlchemyError as e:
        await db.rollback()
        raise Exception(f"无法保存文件: {e}")


async def get_files_for_case_async(db: AsyncSession, case_id: int) -> List[models.File]:
    """
    异步获取指定案例的所有文件
    """
    # 查找活跃案例的所有文件
    try:
        result = await db.execute(
            select(models.File)
            .where(models.File.case_id == case_id)
        )
        files = result.scalars().all()
        return list(files)
    except Exception:
        return []


async def get_file_async(db: AsyncSession, case_id: int, file_id: int) -> Optional[models.File]:
    """
    异步获取指定案例的特定文件
    """
    # 查找特定案例的特定文件
    try:
        result = await db.execute(
            select(models.File)
            .where(models.File.case_id == case_id)
            .where(models.File.id == file_id)
        )
        file = result.scalar_one_or_none()
        return file
    except Exception:
        return None


async def delete_file_async(db: AsyncSession, case_id: int, file_id: int) -> Optional[models.File]:
    """
    异步删除指定案例的特定文件
    """
    # 查找特定案例的特定文件
    try:
        result = await db.execute(
            select(models.File)
            .where(models.File.case_id == case_id)
            .where(models.File.id == file_id)
        )
        file = result.scalar_one_or_none()

        if not file:
            return None

        # 在删除前保存文件信息
        deleted_file = models.File(
            id=file.id,
            case_id=file.case_id,
            file_name=file.file_name,
            file_type=file.file_type,
            file_path=file.file_path,
            thumbnail_small_path=file.thumbnail_small_path,
            width=file.width,
            height=file.height,
            created_at=file.created_at,
            taken_at=file.taken_at
        )

        await db.delete(file)
        await db.commit()
        return deleted_file
    except SQLAlchemyError:
        await db.rollback()
        return None


async def batch_create_files_async(
    db: AsyncSession, 
    case_id: int, 
    files: List[schemas.FileCreate]
) -> List[models.File]:
    """
    批量异步创建文件记录，提升批量上传性能
    """
    # 查找活跃案例
    result = await db.execute(
        select(models.Case)
        .where(models.Case.id == case_id)
        .where(models.Case.status == models.CaseStatus.ACTIVE)
    )
    db_case = result.scalar_one_or_none()

    if not db_case:
        return []

    created_files = []
    
    try:
        # 批量创建文件记录
        for file_data in files:
            try:
                # 准备文件数据并添加案例ID
                file_dict = file_data.model_dump()
                file_dict['case_id'] = case_id
                
                db_file = models.File(**file_dict)
                db.add(db_file)
                created_files.append(db_file)
            except Exception as e:
                print(f"批量创建文件失败: {file_data.file_name}, 错误: {e}")
                continue
        
        # 批量提交
        await db.commit()
        
        # 批量刷新对象以获取ID
        for db_file in created_files:
            await db.refresh(db_file)
            
    except SQLAlchemyError as e:
        await db.rollback()
        print(f"批量创建文件时发生数据库错误: {e}")
        return []

    return created_files


async def get_file_statistics_async(db: AsyncSession, case_id: int) -> dict:
    """
    异步获取指定案例的文件统计信息
    """
    try:
        # 总文件数
        total_result = await db.execute(
            select(models.File.id)
            .where(models.File.case_id == case_id)
        )
        total_files = len(total_result.scalars().all())

        # 图像文件数
        image_result = await db.execute(
            select(models.File.id)
            .where(models.File.case_id == case_id)
            .where(models.File.file_type.like('image/%'))
        )
        image_files = len(image_result.scalars().all())

        other_files = total_files - image_files

        return {
            "total_files": total_files,
            "image_files": image_files,
            "other_files": other_files
        }
    except Exception:
        return {"total_files": 0, "image_files": 0, "other_files": 0} 