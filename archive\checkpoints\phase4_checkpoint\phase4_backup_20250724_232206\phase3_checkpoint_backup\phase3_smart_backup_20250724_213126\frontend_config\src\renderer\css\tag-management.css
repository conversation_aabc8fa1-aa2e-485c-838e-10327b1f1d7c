/* 标签管理页面样式 */

/* 🆕 Phase 1: 基础双向链接样式 */

/* 可点击标签样式 */
.tag-clickable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.tag-clickable:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 清除筛选按钮样式 */
#clear-filter-btn {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 分栏调整器 */
#panel-divider {
    position: relative;
    transition: background-color 0.2s ease;
}

#panel-divider:hover {
    background-color: #3b82f6;
}

#panel-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 20px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 2px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

#panel-divider:hover::after {
    opacity: 1;
}

/* 标签类别头部 */
.tag-category-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.tag-category-header:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}

.tag-category-header.expanded svg {
    transform: rotate(90deg);
}

.tag-category-header span:first-of-type {
    flex: 1;
    margin-left: 8px;
    font-weight: 500;
    color: #374151;
}

.tag-count {
    background: #e5e7eb;
    color: #6b7280;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 标签项 */
.tag-item {
    display: flex;
    align-items: center;
    justify-content: between;
    padding: 6px 12px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.tag-item:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

.tag-item.selected {
    background: #dbeafe;
    border-color: #3b82f6;
    color: #1d4ed8;
}

.tag-item .tag-name {
    flex: 1;
    font-size: 14px;
    color: #374151;
}

.tag-item.selected .tag-name {
    color: #1d4ed8;
    font-weight: 500;
}

.tag-item .tag-count {
    background: #f3f4f6;
    color: #6b7280;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
}

.tag-item.selected .tag-count {
    background: #3b82f6;
    color: white;
}

/* 自定义标签 */
.custom-tag-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.custom-tag-item:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

.custom-tag-item.selected {
    background: #dbeafe;
    border-color: #3b82f6;
}

.custom-tag-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.custom-tag-name {
    flex: 1;
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

.custom-tag-item.selected .custom-tag-name {
    color: #1d4ed8;
}

.custom-tag-count {
    background: #f3f4f6;
    color: #6b7280;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
    margin-right: 8px;
}

.custom-tag-item.selected .custom-tag-count {
    background: #3b82f6;
    color: white;
}

.custom-tag-actions {
    display: flex;
    align-items: center;
    space-x: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.custom-tag-item:hover .custom-tag-actions {
    opacity: 1;
}

.custom-tag-actions button {
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.custom-tag-actions button:hover {
    background: #f3f4f6;
}

/* 画廊网格 */
.gallery-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.gallery-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.gallery-item.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 源图片高亮效果 */
.gallery-item.source-highlight {
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.4);
    transform: scale(1.05);
    z-index: 10;
    position: relative;
}

.gallery-item.source-highlight::after {
    content: '来源';
    position: absolute;
    top: -8px;
    right: -8px;
    background: #f59e0b;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
    z-index: 11;
}

/* 可点击标签样式 */
.clickable-tag {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 2px 4px;
}

.clickable-tag:hover {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    text-decoration: underline;
}

/* 自定义标签容器 */
.custom-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

/* 自定义标签项样式 */
.custom-tag-item.clickable-tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid;
    transition: all 0.2s ease;
}

.custom-tag-item.clickable-tag:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

/* 加载和错误状态样式 */
.loading {
    color: #6b7280;
    font-style: italic;
    padding: 10px;
    text-align: center;
}

.error {
    color: #ef4444;
    font-style: italic;
    padding: 10px;
    text-align: center;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-item-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    opacity: 0;
    transition: opacity 0.2s ease;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 12px;
}

.gallery-item:hover .gallery-item-overlay {
    opacity: 1;
}

.gallery-item-title {
    color: white;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 4px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.gallery-item-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.gallery-item-tag {
    background: rgba(255, 255, 255, 0.9);
    color: #374151;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
}

/* 选择框 */
.gallery-item-checkbox {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #d1d5db;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s ease;
    cursor: pointer;
}

/* 默认情况下隐藏选择框 */
body:not(.selection-mode) .gallery-item-checkbox {
    display: none;
}

/* 选择模式下显示选择框 */
body.selection-mode .gallery-item:hover .gallery-item-checkbox,
body.selection-mode .gallery-item.selected .gallery-item-checkbox {
    opacity: 1;
}

.gallery-item.selected .gallery-item-checkbox {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .flex {
        flex-direction: column;
    }
    
    #tag-panel {
        width: 100% !important;
        max-height: 40vh;
        min-width: unset !important;
        max-width: unset !important;
    }
    
    #panel-divider {
        display: none;
    }
    
    #gallery-panel {
        flex: 1;
    }
}

@media (max-width: 768px) {
    #tag-panel {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80vw;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }
    
    #tag-panel.open {
        left: 0;
    }
    
    .mobile-overlay {
        position: fixed;
        inset: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
    }
}

/* 动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 拖拽状态 */
.drag-over {
    background: #dbeafe !important;
    border-color: #3b82f6 !important;
}

.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

/* 加载状态 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
}

/* 图片查看模态框响应式样式 */
#image-modal {
    padding: 0;
    overflow: hidden;
}

#image-modal .flex {
    padding: 8px;
    min-height: 100vh;
    box-sizing: border-box;
}

#image-modal .relative {
    display: flex;
    flex-direction: column;
    max-width: calc(100vw - 16px);
    max-height: calc(100vh - 16px);
    background: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    overflow: hidden;
}

/* 关闭按钮 */
#close-image-modal {
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 20;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    transition: all 0.2s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

#close-image-modal:hover {
    background: rgba(0, 0, 0, 0.9);
    border-color: rgba(255, 255, 255, 0.6);
    transform: scale(1.1);
}

#close-image-modal svg {
    width: 20px;
    height: 20px;
}

/* 图片容器 */
#modal-image {
    flex: 1;
    width: 100%;
    height: auto;
    max-width: 100%;
    max-height: calc(100vh - 200px);
    object-fit: contain;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

/* 图片信息面板 */
#image-info-panel {
    position: relative;
    bottom: auto;
    left: auto;
    right: auto;
    margin: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 0 0 8px 8px;
    padding: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

#image-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #ffffff;
    word-break: break-word;
    line-height: 1.4;
}

#image-tags {
    margin-bottom: 12px;
    max-height: 60px;
    overflow-y: auto;
}

#image-tags::-webkit-scrollbar {
    width: 4px;
}

#image-tags::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

#image-tags::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

#image-tags .inline-block {
    font-size: 12px;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 12px;
    background: rgba(59, 130, 246, 0.8);
    color: white;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

/* 底部信息和按钮区域 */
#image-info-panel .flex.items-center.justify-between {
    flex-wrap: wrap;
    gap: 8px;
}

#image-info {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    flex: 1;
    min-width: 0;
    word-break: break-word;
}

#add-tag-to-image-btn {
    padding: 8px 16px;
    font-size: 13px;
    font-weight: 500;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

#add-tag-to-image-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* 响应式设计 - 平板设备 */
@media (max-width: 1024px) {
    #image-modal .flex {
        padding: 4px;
    }

    #image-modal .relative {
        max-width: calc(100vw - 8px);
        max-height: calc(100vh - 8px);
    }

    #modal-image {
        max-height: calc(100vh - 180px);
    }

    #image-info-panel {
        padding: 12px;
    }

    #image-title {
        font-size: 15px;
    }

    #close-image-modal {
        width: 36px;
        height: 36px;
        top: 8px;
        right: 8px;
    }

    #close-image-modal svg {
        width: 18px;
        height: 18px;
    }
}

/* 响应式设计 - 手机设备 */
@media (max-width: 768px) {
    #image-modal {
        padding: 0;
    }

    #image-modal .flex {
        padding: 0;
        align-items: stretch;
    }

    #image-modal .relative {
        max-width: 100vw;
        max-height: 100vh;
        border-radius: 0;
        flex-direction: column;
    }

    #modal-image {
        max-height: calc(100vh - 160px);
        border-radius: 0;
    }

    #image-info-panel {
        position: relative;
        border-radius: 0;
        padding: 16px;
        background: rgba(0, 0, 0, 0.95);
        max-height: 160px;
        overflow-y: auto;
    }

    #image-title {
        font-size: 14px;
        margin-bottom: 6px;
    }

    #image-tags {
        margin-bottom: 8px;
        max-height: 40px;
    }

    #image-info-panel .flex.items-center.justify-between {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    #image-info {
        font-size: 12px;
        text-align: center;
        margin-bottom: 4px;
    }

    #add-tag-to-image-btn {
        width: 100%;
        padding: 10px 16px;
        font-size: 14px;
        text-align: center;
    }

    #close-image-modal {
        width: 32px;
        height: 32px;
        top: 8px;
        right: 8px;
        background: rgba(0, 0, 0, 0.8);
    }

    #close-image-modal svg {
        width: 16px;
        height: 16px;
    }
}

/* 响应式设计 - 小屏手机 */
@media (max-width: 480px) {
    #modal-image {
        max-height: calc(100vh - 140px);
    }

    #image-info-panel {
        padding: 12px;
        max-height: 140px;
    }

    #image-title {
        font-size: 13px;
        line-height: 1.3;
    }

    #image-tags .inline-block {
        font-size: 11px;
        padding: 3px 6px;
    }

    #image-info {
        font-size: 11px;
    }

    #add-tag-to-image-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* 横屏模式优化 */
@media (max-height: 600px) and (orientation: landscape) {
    #modal-image {
        max-height: calc(100vh - 120px);
    }

    #image-info-panel {
        max-height: 120px;
        padding: 8px 16px;
    }

    #image-title {
        font-size: 13px;
        margin-bottom: 4px;
    }

    #image-tags {
        margin-bottom: 6px;
        max-height: 30px;
    }

    #image-tags .inline-block {
        font-size: 11px;
        padding: 2px 6px;
        margin: 1px;
    }

    #image-info {
        font-size: 11px;
    }

    #add-tag-to-image-btn {
        padding: 6px 12px;
        font-size: 12px;
    }
}