// Integration Test Component - Phase 2 Testing
// Phase 2集成测试组件

import React, { useState } from 'react';
import { useFeatureFlags } from '@/utils/hooks/useFeatureFlags';
import { useUIStore } from '@/store';
import { useCases, useFiles } from '@/hooks/useApi';
import { Button } from '@/components/ui/button';

// ============================================================================
// IntegrationTest 组件
// ============================================================================

export const IntegrationTest: React.FC = () => {
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [isRunning, setIsRunning] = useState(false);

  // 获取功能开关和状态
  const { flags, enableNewUI, disableNewUI } = useFeatureFlags();
  const { selectedCaseId, setSelectedCase, searchQuery, setSearchQuery } = useUIStore();
  const { data: cases, isLoading: casesLoading } = useCases();
  const { data: filesData, isLoading: filesLoading } = useFiles(selectedCaseId || undefined);

  // ========================================
  // 测试函数
  // ========================================

  const runTests = async () => {
    setIsRunning(true);
    const results: Record<string, boolean> = {};

    try {
      // 测试1: 功能开关系统
      results['featureFlags'] = typeof flags === 'object' && flags !== null;

      // 测试2: 案例数据获取
      results['casesAPI'] = !casesLoading && Array.isArray(cases);

      // 测试3: 文件数据获取
      if (selectedCaseId) {
        results['filesAPI'] = !filesLoading && (filesData?.files ? Array.isArray(filesData.files) : true);
      } else {
        results['filesAPI'] = true; // 没有选中案例时跳过
      }

      // 测试4: 状态管理
      results['stateManagement'] = typeof selectedCaseId === 'number' || selectedCaseId === null;

      // 测试5: 搜索功能
      const originalQuery = searchQuery;
      setSearchQuery('test');
      await new Promise(resolve => setTimeout(resolve, 100));
      results['searchFunction'] = searchQuery === 'test';
      setSearchQuery(originalQuery);

      // 测试6: 新UI组件开关
      const originalFlags = { ...flags };
      enableNewUI();
      await new Promise(resolve => setTimeout(resolve, 100));
      results['newUIToggle'] = flags.useNewNavigationSidebar && flags.useNewGallery;
      
      // 恢复原始状态
      if (!originalFlags.useNewNavigationSidebar) disableNewUI();

      setTestResults(results);
    } catch (error) {
      console.error('Integration test error:', error);
      results['error'] = false;
      setTestResults(results);
    } finally {
      setIsRunning(false);
    }
  };

  const selectFirstCase = () => {
    if (cases && cases.length > 0) {
      setSelectedCase(cases[0].id);
    }
  };

  // ========================================
  // 渲染
  // ========================================

  return (
    <div className="p-6 bg-card border rounded-lg">
      <h2 className="text-lg font-semibold mb-4">Phase 2 Integration Test</h2>
      
      {/* 测试控制 */}
      <div className="flex gap-2 mb-4">
        <Button onClick={runTests} disabled={isRunning}>
          {isRunning ? '测试中...' : '运行集成测试'}
        </Button>
        <Button onClick={selectFirstCase} variant="outline">
          选择第一个案例
        </Button>
        <Button onClick={enableNewUI} variant="outline">
          启用新UI
        </Button>
        <Button onClick={disableNewUI} variant="outline">
          禁用新UI
        </Button>
      </div>

      {/* 当前状态 */}
      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div>
          <strong>功能开关状态:</strong>
          <ul className="mt-1 space-y-1">
            <li>新导航栏: {flags.useNewNavigationSidebar ? '✅' : '❌'}</li>
            <li>新画廊: {flags.useNewGallery ? '✅' : '❌'}</li>
            <li>数据适配器: {flags.enableNewDataAdapters ? '✅' : '❌'}</li>
            <li>新样式: {flags.enableNewStyling ? '✅' : '❌'}</li>
          </ul>
        </div>
        <div>
          <strong>数据状态:</strong>
          <ul className="mt-1 space-y-1">
            <li>案例数量: {cases?.length || 0}</li>
            <li>选中案例: {selectedCaseId || '无'}</li>
            <li>文件数量: {filesData?.files?.length || 0}</li>
            <li>搜索查询: "{searchQuery}"</li>
          </ul>
        </div>
      </div>

      {/* 测试结果 */}
      {Object.keys(testResults).length > 0 && (
        <div className="border-t pt-4">
          <h3 className="font-medium mb-2">测试结果:</h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            {Object.entries(testResults).map(([test, passed]) => (
              <div key={test} className={`flex items-center gap-2 ${passed ? 'text-green-600' : 'text-red-600'}`}>
                <span>{passed ? '✅' : '❌'}</span>
                <span>{test}</span>
              </div>
            ))}
          </div>
          
          {/* 总体结果 */}
          <div className="mt-4 p-3 rounded bg-muted">
            <strong>
              总体结果: {Object.values(testResults).every(Boolean) ? '✅ 所有测试通过' : '❌ 部分测试失败'}
            </strong>
          </div>
        </div>
      )}

      {/* API数据预览 */}
      {selectedCaseId && (
        <div className="border-t pt-4 mt-4">
          <h3 className="font-medium mb-2">API数据预览:</h3>
          <div className="text-xs bg-muted p-2 rounded overflow-auto max-h-32">
            <div><strong>案例:</strong> {cases?.find(c => c.id === selectedCaseId)?.case_name}</div>
            <div><strong>文件:</strong> {filesData?.files?.slice(0, 3).map(f => f.file_name).join(', ')}</div>
          </div>
        </div>
      )}
    </div>
  );
};

// ============================================================================
// 导出
// ============================================================================

export default IntegrationTest;
