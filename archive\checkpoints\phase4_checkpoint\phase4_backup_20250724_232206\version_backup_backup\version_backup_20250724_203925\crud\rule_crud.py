# src/crud/rule_crud.py
"""
案例处理规则的CRUD操作
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List, Optional, Dict, Any
from datetime import datetime

from .. import models, schemas


def create_rule(db: Session, case_id: int, rule: schemas.CreateRuleRequest) -> models.CaseProcessingRule:
    """
    创建案例处理规则
    
    Args:
        db: 数据库会话
        case_id: 案例ID
        rule: 规则创建请求
        
    Returns:
        创建的规则对象
    """
    db_rule = models.CaseProcessingRule(
        case_id=case_id,
        rule_type=rule.rule_type,
        rule_config=rule.rule_config,
        is_active=rule.is_active
    )
    db.add(db_rule)
    db.commit()
    db.refresh(db_rule)
    return db_rule


def get_rules_by_case(
    db: Session, 
    case_id: int, 
    is_active: Optional[bool] = None,
    rule_type: Optional[schemas.RuleType] = None
) -> List[models.CaseProcessingRule]:
    """
    获取指定案例的所有处理规则
    
    Args:
        db: 数据库会话
        case_id: 案例ID
        is_active: 筛选激活状态的规则
        rule_type: 筛选特定类型的规则
        
    Returns:
        规则列表
    """
    query = db.query(models.CaseProcessingRule).filter(
        models.CaseProcessingRule.case_id == case_id
    )
    
    if is_active is not None:
        query = query.filter(models.CaseProcessingRule.is_active == is_active)
    
    if rule_type is not None:
        query = query.filter(models.CaseProcessingRule.rule_type == rule_type)
    
    return query.order_by(models.CaseProcessingRule.created_at).all()


def get_rule(db: Session, rule_id: int) -> Optional[models.CaseProcessingRule]:
    """
    获取单条规则详情
    
    Args:
        db: 数据库会话
        rule_id: 规则ID
        
    Returns:
        规则对象或None
    """
    return db.query(models.CaseProcessingRule).filter(
        models.CaseProcessingRule.id == rule_id
    ).first()


def update_rule(
    db: Session, 
    rule_id: int, 
    rule_update: schemas.UpdateRuleRequest
) -> Optional[models.CaseProcessingRule]:
    """
    更新处理规则
    
    Args:
        db: 数据库会话
        rule_id: 规则ID
        rule_update: 更新请求
        
    Returns:
        更新后的规则对象或None
    """
    db_rule = db.query(models.CaseProcessingRule).filter(
        models.CaseProcessingRule.id == rule_id
    ).first()
    
    if not db_rule:
        return None
    
    # 更新字段
    if rule_update.rule_config is not None:
        db_rule.rule_config = rule_update.rule_config
    
    if rule_update.is_active is not None:
        db_rule.is_active = rule_update.is_active
    
    # 更新时间戳
    db_rule.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(db_rule)
    return db_rule


def delete_rule(db: Session, rule_id: int) -> bool:
    """
    删除处理规则
    
    Args:
        db: 数据库会话
        rule_id: 规则ID
        
    Returns:
        是否删除成功
    """
    db_rule = db.query(models.CaseProcessingRule).filter(
        models.CaseProcessingRule.id == rule_id
    ).first()
    
    if not db_rule:
        return False
    
    db.delete(db_rule)
    db.commit()
    return True


def get_active_rules_for_case(db: Session, case_id: int) -> List[models.CaseProcessingRule]:
    """
    获取指定案例的所有激活规则，用于文件处理
    
    Args:
        db: 数据库会话
        case_id: 案例ID
        
    Returns:
        激活的规则列表，按创建时间排序
    """
    return db.query(models.CaseProcessingRule).filter(
        and_(
            models.CaseProcessingRule.case_id == case_id,
            models.CaseProcessingRule.is_active == True
        )
    ).order_by(models.CaseProcessingRule.created_at).all()


def validate_rule_config(rule_type: schemas.RuleType, rule_config: Dict[str, Any]) -> tuple[bool, Optional[str]]:
    """
    验证规则配置的有效性
    
    Args:
        rule_type: 规则类型
        rule_config: 规则配置
        
    Returns:
        (是否有效, 错误信息)
    """
    if rule_type == schemas.RuleType.FILENAME_PARSING:
        # 验证文件名解析规则配置
        required_fields = ['pattern', 'delimiter', 'fields']
        for field in required_fields:
            if field not in rule_config:
                return False, f"缺少必填字段: {field}"
        
        # 验证fields结构
        fields = rule_config.get('fields', [])
        if not isinstance(fields, list) or len(fields) == 0:
            return False, "fields必须是非空数组"
        
        for i, field in enumerate(fields):
            if not isinstance(field, dict):
                return False, f"fields[{i}]必须是对象"
            
            required_field_keys = ['name', 'position', 'tagCategory']
            for key in required_field_keys:
                if key not in field:
                    return False, f"fields[{i}]缺少必填字段: {key}"
    
    elif rule_type == schemas.RuleType.DATE_TAGGING_FORMAT:
        # 验证日期标签格式化规则配置
        required_fields = ['sourceField', 'outputFormat', 'tagCategory', 'tagName']
        for field in required_fields:
            if field not in rule_config:
                return False, f"缺少必填字段: {field}"
    
    return True, None
