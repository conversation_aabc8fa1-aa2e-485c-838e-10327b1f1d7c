<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签管理 - 迷星 Mizzy Star</title>
    <link href="../styles/output.css" rel="stylesheet">
    <link href="css/tag-management.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
</head>
<body class="bg-gray-50">
    <!-- 通知容器 -->
    <div id="notifications" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- 主容器 -->
    <div class="flex h-screen">
        <!-- 左侧标签管理面板 -->
        <div id="tag-panel" class="bg-white border-r border-gray-200 flex flex-col" style="width: 400px; min-width: 300px; max-width: 600px;">
            <!-- 标签面板头部 -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <h1 class="text-xl font-bold text-gray-900">标签管理</h1>
                    <div class="flex items-center space-x-2">
                        <button id="back-btn" class="text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100" title="返回案例">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                        </button>
                        <button id="refresh-btn" class="text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100" title="刷新">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 案例信息 -->
                <div id="case-info" class="mb-4 p-3 bg-blue-50 rounded-lg">
                    <div class="text-sm text-blue-600">
                        <span id="case-name">加载中...</span>
                        <span class="mx-2">•</span>
                        <span id="case-id">ID: --</span>
                    </div>
                </div>

                <!-- 搜索框 -->
                <div class="relative">
                    <input type="text" id="tag-search" placeholder="搜索标签..." 
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>

            <!-- 标签统计 -->
            <div id="tag-stats" class="p-4 border-b border-gray-200 bg-gray-50">
                <div class="grid grid-cols-3 gap-2 text-center text-sm">
                    <div>
                        <div class="font-semibold text-blue-600" id="total-tags">0</div>
                        <div class="text-gray-500">总标签</div>
                    </div>
                    <div>
                        <div class="font-semibold text-green-600" id="custom-tags">0</div>
                        <div class="text-gray-500">自定义</div>
                    </div>
                    <div>
                        <div class="font-semibold text-purple-600" id="active-files">0</div>
                        <div class="text-gray-500">文件数</div>
                    </div>
                </div>
            </div>

            <!-- 标签树容器 -->
            <div class="flex-1 overflow-y-auto">
                <!-- 自定义标签区域 -->
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-900">自定义标签</h3>
                        <button id="add-custom-tag-btn" class="text-blue-600 hover:text-blue-800 p-1" title="添加自定义标签">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                    </div>
                    <div id="custom-tags-list" class="space-y-2">
                        <!-- 自定义标签将在这里动态生成 -->
                    </div>
                </div>

                <!-- 系统标签区域 -->
                <div class="p-4">
                    <h3 class="font-semibold text-gray-900 mb-3">系统标签</h3>
                    
                    <!-- Properties 标签 -->
                    <div class="mb-4">
                        <div class="tag-category-header" data-category="properties">
                            <svg class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            <span>属性标签</span>
                            <span class="tag-count">0</span>
                        </div>
                        <div id="properties-tags" class="tag-category-content hidden ml-4 mt-2 space-y-1">
                            <!-- Properties 标签将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- Metadata 标签 -->
                    <div class="mb-4">
                        <div class="tag-category-header" data-category="metadata">
                            <svg class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            <span>元数据标签</span>
                            <span class="tag-count">0</span>
                        </div>
                        <div id="metadata-tags" class="tag-category-content hidden ml-4 mt-2 space-y-1">
                            <!-- Metadata 标签将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- CV 标签 -->
                    <div class="mb-4">
                        <div class="tag-category-header" data-category="cv">
                            <svg class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            <span>计算机视觉</span>
                            <span class="tag-count">0</span>
                        </div>
                        <div id="cv-tags" class="tag-category-content hidden ml-4 mt-2 space-y-1">
                            <!-- CV 标签将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- User 标签 -->
                    <div class="mb-4">
                        <div class="tag-category-header" data-category="user">
                            <svg class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            <span>用户标签</span>
                            <span class="tag-count">0</span>
                        </div>
                        <div id="user-tags" class="tag-category-content hidden ml-4 mt-2 space-y-1">
                            <!-- User 标签将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- AI 标签 -->
                    <div class="mb-4">
                        <div class="tag-category-header" data-category="ai">
                            <svg class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            <span>AI标签</span>
                            <span class="tag-count">0</span>
                        </div>
                        <div id="ai-tags" class="tag-category-content hidden ml-4 mt-2 space-y-1">
                            <!-- AI 标签将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分栏调整器 -->
        <div id="panel-divider" class="w-1 bg-gray-200 hover:bg-blue-400 cursor-col-resize transition-colors"></div>

        <!-- 右侧标签画廊面板 -->
        <div id="gallery-panel" class="flex-1 flex flex-col bg-white">
            <!-- 画廊头部 -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">标签画廊</h2>
                        <p id="gallery-subtitle" class="text-sm text-gray-500">选择标签查看相关文件</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="text-sm text-gray-500">
                            <span id="selected-files-count">0</span> / <span id="total-files-count">0</span> 个文件
                        </div>
                        <!-- 选择按钮组 -->
                        <div id="selection-controls">
                            <!-- 初始状态：选择按钮 -->
                            <button id="select-mode-btn" class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200" title="进入选择模式">
                                选择
                            </button>

                            <!-- 选择模式：全选和清除按钮 -->
                            <div id="selection-actions" class="hidden space-x-2">
                                <button id="select-all-btn" class="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200" title="全选">
                                    全选
                                </button>
                                <button id="clear-selection-btn" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200" title="清除选择">
                                    清除
                                </button>
                                <button id="exit-selection-btn" class="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200" title="退出选择模式">
                                    退出
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量操作栏 -->
                <div id="batch-actions" class="mt-3 p-3 bg-blue-50 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-blue-700">
                            已选择 <span id="batch-selected-count">0</span> 个文件
                        </span>
                        <div class="flex items-center space-x-2">
                            <select id="batch-tag-select" class="text-sm border border-blue-300 rounded px-2 py-1">
                                <option value="">选择标签...</option>
                            </select>
                            <button id="batch-add-tag-btn" class="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700">
                                添加标签
                            </button>
                            <button id="batch-remove-tag-btn" class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                                移除标签
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 画廊内容 -->
            <div class="flex-1 overflow-y-auto p-4">
                <div id="gallery-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
                    <!-- 文件缩略图将在这里动态生成 -->
                </div>

                <!-- 空状态 -->
                <div id="gallery-empty" class="hidden text-center py-12">
                    <svg class="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a2 2 0 01-2-2V5a2 2 0 012-2z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">没有找到文件</h3>
                    <p class="text-gray-500">选择左侧的标签来查看相关文件</p>
                </div>

                <!-- 加载状态 -->
                <div id="gallery-loading" class="hidden text-center py-12">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p class="mt-2 text-gray-500">加载中...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div id="modal-content" class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
                <!-- 模态框内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 图片查看模态框 -->
    <div id="image-modal" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="relative">
                <!-- 关闭按钮 -->
                <button id="close-image-modal" class="text-white hover:text-gray-300" title="关闭 (ESC)">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>

                <!-- 图片容器 -->
                <img id="modal-image" src="" alt="" loading="lazy">

                <!-- 图片信息面板 -->
                <div id="image-info-panel" class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-4">
                    <h3 id="image-title" class="text-lg font-semibold mb-2"></h3>

                    <!-- 标签区域 -->
                    <div class="mb-3">
                        <div class="text-sm text-gray-300 mb-2">相关标签</div>
                        <div id="image-tags" class="max-h-20 overflow-y-auto">
                            <!-- 标签将在这里显示 -->
                        </div>
                    </div>

                    <div class="flex items-center justify-between text-sm">
                        <span id="image-info" class="text-gray-300"></span>
                        <div class="flex items-center space-x-2">
                            <button id="add-tag-to-image-btn"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
                                    title="为此图片添加标签">
                                <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a2 2 0 01-2-2V5a2 2 0 012-2z"></path>
                                </svg>
                                添加标签
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notifications" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- 脚本 -->
    <script src="js/api.js"></script>
    <script src="js/tag-management.js"></script>
    <script src="js/filename-extraction.js"></script>
    <!-- 在页面底部其他脚本之后添加统一数据流架构 -->
    <script src="js/unified_data_flow_architecture.js"></script>
    <script>
        // 在页面加载完成后初始化统一数据流架构
        document.addEventListener('DOMContentLoaded', function() {
            // 等待tagApp初始化完成后再启动统一架构
            setTimeout(function() {
                if (window.tagApp) {
                    console.log('🚀 标签管理页面已加载，初始化统一数据流架构...');
                    if (window.initUnifiedArchitecture) {
                        window.initUnifiedArchitecture();
                    }
                }
            }, 500);
        });
    </script>
</body>
</html>