-- PostgreSQL初始化脚本 for Mizzy Star
-- 创建扩展、用户和基础配置

-- =============================================================================
-- 扩展安装
-- =============================================================================

-- 性能监控扩展
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- JSONB和全文搜索优化扩展
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- 未来AI向量搜索准备 (如果需要)
-- CREATE EXTENSION IF NOT EXISTS vector;

-- =============================================================================
-- 数据库配置
-- =============================================================================

-- 设置默认搜索路径
ALTER DATABASE mizzy_main SET search_path TO public;

-- 设置默认时区
ALTER DATABASE mizzy_main SET timezone TO 'UTC';

-- 启用JSONB操作符类
-- (PostgreSQL 15默认已启用)

-- =============================================================================
-- 性能优化配置
-- =============================================================================

-- 为当前数据库设置特定配置
ALTER DATABASE mizzy_main SET work_mem TO '8MB';
ALTER DATABASE mizzy_main SET maintenance_work_mem TO '128MB';
ALTER DATABASE mizzy_main SET effective_cache_size TO '1GB';

-- =============================================================================
-- 用户和权限配置
-- =============================================================================

-- 创建只读用户 (用于报表和监控)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'mizzy_readonly') THEN
        CREATE ROLE mizzy_readonly LOGIN PASSWORD 'readonly_pass_123';
    END IF;
END
$$;

-- 创建备份用户
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'mizzy_backup') THEN
        CREATE ROLE mizzy_backup LOGIN PASSWORD 'backup_pass_123';
    END IF;
END
$$;

-- 为主用户授予必要权限
GRANT ALL PRIVILEGES ON DATABASE mizzy_main TO mizzy_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO mizzy_user;

-- 为只读用户授予查询权限
GRANT CONNECT ON DATABASE mizzy_main TO mizzy_readonly;
GRANT USAGE ON SCHEMA public TO mizzy_readonly;

-- 为备份用户授予必要权限
GRANT CONNECT ON DATABASE mizzy_main TO mizzy_backup;
GRANT USAGE ON SCHEMA public TO mizzy_backup;

-- =============================================================================
-- 监控和统计配置
-- =============================================================================

-- 重置统计信息
SELECT pg_stat_reset();
SELECT pg_stat_statements_reset();

-- =============================================================================
-- 初始化完成日志
-- =============================================================================

-- 记录初始化完成
DO $$
BEGIN
    RAISE NOTICE 'Mizzy Star PostgreSQL initialization completed successfully!';
    RAISE NOTICE 'Database: mizzy_main';
    RAISE NOTICE 'Main user: mizzy_user';
    RAISE NOTICE 'Extensions installed: pg_stat_statements, btree_gin';
    RAISE NOTICE 'Ready for schema creation and data migration.';
END
$$;
