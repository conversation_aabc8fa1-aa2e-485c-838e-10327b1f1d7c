// GalleryWrapper - Feature flag controlled gallery component wrapper
// 功能开关控制的画廊组件包装器

import React from 'react';
import { useFeatureFlag } from '@/utils/hooks/useFeatureFlags';
import { Gallery } from './Gallery';
import { GalleryPanel } from '@/components/panels/GalleryPanel';
import { useUIStore } from '@/store';
import { useFiles } from '@/hooks/useApi';

// ============================================================================
// 接口定义
// ============================================================================

interface GalleryWrapperProps {
  className?: string;
  isVisible?: boolean;
}

// ============================================================================
// GalleryWrapper 组件
// ============================================================================

export const GalleryWrapper: React.FC<GalleryWrapperProps> = (props) => {
  // 检查功能开关
  const useNewGallery = useFeatureFlag('useNewGallery');
  
  // 获取状态和数据
  const {
    selectedCaseId,
    selectedFileIds,
    galleryLayout,
    galleryZoomLevel,
    gallerySortBy,
    gallerySortOrder,
    searchQuery,
    showFileName,
    showFileInfo,
    toggleFileSelection,
    setSearchQuery,
  } = useUIStore();

  const { data: filesData, isLoading: filesLoading } = useFiles(selectedCaseId || undefined);

  // 如果启用新画廊，使用新组件
  if (useNewGallery) {
    return <Gallery {...props} />;
  }

  // 否则使用原有的GalleryPanel
  const files = filesData?.files?.map(file => ({
    id: file.id,
    fileName: file.file_name,
    filePath: file.file_path,
    fileType: file.file_type,
    fileSize: file.file_size,
    width: file.width || undefined,
    height: file.height || undefined,
    thumbnailPath: file.thumbnail_small_path || undefined,
  })) || [];

  const handleFileSelect = (fileId: number, selected: boolean) => {
    toggleFileSelection(fileId);
  };

  const handleFileDoubleClick = (file: any) => {
    // TODO: 实现文件双击逻辑
    console.log('Double clicked file:', file);
  };

  const handleFileDelete = (file: any) => {
    // TODO: 实现文件删除逻辑
    console.log('Delete file:', file);
  };

  return (
    <GalleryPanel
      files={files}
      selectedFileIds={selectedFileIds}
      layout={galleryLayout}
      zoomLevel={galleryZoomLevel}
      sortBy={gallerySortBy}
      sortOrder={gallerySortOrder}
      searchQuery={searchQuery}
      showFileName={showFileName}
      showFileInfo={showFileInfo}
      loading={filesLoading}
      onFileSelect={handleFileSelect}
      onFileDoubleClick={handleFileDoubleClick}
      onFileDelete={handleFileDelete}
      onSearchChange={setSearchQuery}
      {...props}
    />
  );
};

// ============================================================================
// 导出
// ============================================================================

export default GalleryWrapper;
