#!/usr/bin/env python3
# 第三阶段智能清理回滚脚本
# 备份时间: 2025-07-24T21:31:27.046671

import shutil
from pathlib import Path

def rollback_smart_cleanup():
    print("🔄 开始回滚第三阶段智能清理...")
    
    project_root = Path(__file__).parent.parent.parent
    backup_dir = Path(__file__).parent / "frontend_config"
    target_dir = project_root / "frontend"
    
    try:
        if backup_dir.exists():
            target_dir.mkdir(exist_ok=True)
            
            # 恢复配置文件
            for backup_file in backup_dir.glob("*"):
                if backup_file.is_file():
                    shutil.copy2(backup_file, target_dir / backup_file.name)
                    print(f"✅ 恢复配置文件: {backup_file.name}")
                elif backup_file.is_dir() and backup_file.name == "src":
                    target_src = target_dir / "src"
                    if target_src.exists():
                        shutil.rmtree(target_src)
                    shutil.copytree(backup_file, target_src)
                    print("✅ 恢复src目录")
            
            print("🎉 智能清理回滚完成！")
            print("⚠️ 注意：需要重新运行 npm install 安装依赖")
            return True
        else:
            print("❌ 备份目录不存在")
            return False
    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        return False

if __name__ == "__main__":
    rollback_smart_cleanup()
