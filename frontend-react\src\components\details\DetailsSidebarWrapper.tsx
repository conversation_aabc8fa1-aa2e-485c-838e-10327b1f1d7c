// DetailsSidebarWrapper - Feature flag controlled details sidebar component wrapper
// 功能开关控制的详情侧边栏包装器

import React from 'react';
import { useFeatureFlag } from '@/utils/hooks/useFeatureFlags';
import { DetailsSidebar } from './DetailsSidebar';
import { InfoPanel } from '@/components/panels/InfoPanel';
import { useUIStore } from '@/store';
import { useFiles } from '@/hooks/useApi';

// ============================================================================
// 接口定义
// ============================================================================

interface DetailsSidebarWrapperProps {
  className?: string;
}

// ============================================================================
// DetailsSidebarWrapper 组件
// ============================================================================

export const DetailsSidebarWrapper: React.FC<DetailsSidebarWrapperProps> = (props) => {
  // 检查功能开关
  const useNewDetailsSidebar = useFeatureFlag('useNewDetailsSidebar');
  
  // 获取状态和数据
  const {
    selectedCaseId,
    selectedFileIds,
    activePanels,
    toggleInfoPanelSection,
  } = useUIStore();

  const { data: filesData } = useFiles(selectedCaseId || undefined);

  // 如果启用新详情栏，使用新组件
  if (useNewDetailsSidebar) {
    return <DetailsSidebar {...props} />;
  }

  // 否则使用原有的InfoPanel
  const selectedFile = filesData?.files?.find(file => 
    selectedFileIds.length === 1 && selectedFileIds.includes(file.id)
  );

  const handleTagOperation = (operation: 'add' | 'remove', category: string, tag: string) => {
    // TODO: 实现标签操作逻辑
    console.log('Tag operation:', operation, category, tag);
  };

  return (
    <InfoPanel
      selectedFile={selectedFile ? {
        id: selectedFile.id,
        fileName: selectedFile.file_name,
        filePath: selectedFile.file_path,
        fileType: selectedFile.file_type,
        fileSize: selectedFile.file_size,
        width: selectedFile.width || undefined,
        height: selectedFile.height || undefined,
        thumbnailPath: selectedFile.thumbnail_small_path || undefined,
        createdAt: selectedFile.created_at,
        tags: selectedFile.tags?.tags || undefined,
      } : undefined}
      selectedCount={selectedFileIds.length}
      activePanels={activePanels}
      onPanelToggle={toggleInfoPanelSection}
      onTagOperation={handleTagOperation}
      {...props}
    />
  );
};

// ============================================================================
// 导出
// ============================================================================

export default DetailsSidebarWrapper;
