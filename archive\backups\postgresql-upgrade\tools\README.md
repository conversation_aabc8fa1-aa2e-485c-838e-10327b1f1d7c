# 开发和测试工具

## 概述
本目录包含PostgreSQL升级项目的开发和测试工具。

## 工具说明
- `test_data_generator.py` - 测试数据生成器
- `db_initializer.py` - 数据库初始化工具
- `performance_tester.py` - 性能测试工具
- `dev_setup.py` - 开发环境配置脚本
- `migration_validator.py` - 迁移验证工具

## 测试数据生成器
生成大量模拟数据用于性能测试：
- 支持生成50,000张照片记录
- 支持生成500,000个标签
- 模拟真实的EXIF数据和标签分布
- 支持不同的数据规模配置

## 性能测试工具
全面的性能测试套件：
- 查询性能基准测试
- 并发压力测试
- 内存和CPU使用率监控
- 与SQLite性能对比

## 开发环境配置
自动化开发环境设置：
- 依赖包安装
- 数据库连接配置
- 测试数据初始化
- 开发工具配置

## 使用方法
```bash
# 初始化开发环境
python dev_setup.py

# 生成测试数据
python test_data_generator.py --photos 10000 --tags 100000

# 运行性能测试
python performance_tester.py --test-suite full
```
