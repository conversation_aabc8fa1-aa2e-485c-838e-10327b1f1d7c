// 主应用程序类
class App {
    constructor() {
        this.currentPage = 'cases';
        this.cases = [];
        this.trashCases = [];
        this.searchTerm = '';
        
        this.init();
    }
    
    // 初始化应用
    init() {
        this.setupEventListeners();
        this.loadCases();
        this.checkBackendConnection();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        try {
            // 导航事件
            const navCases = document.getElementById('nav-cases');
            if (navCases) {
                navCases.addEventListener('click', () => {
                    this.showPage('cases');
                });
            } else {
                console.error('nav-cases元素未找到');
            }

            const navTrash = document.getElementById('nav-trash');
            if (navTrash) {
                navTrash.addEventListener('click', () => {
                    this.showPage('trash');
                });
            } else {
                console.error('nav-trash元素未找到');
            }

            // 新建案例按钮
            const newCaseBtn = document.getElementById('new-case-btn');
            if (newCaseBtn) {
                newCaseBtn.addEventListener('click', () => {
                    console.log('新建案例按钮被点击');
                    if (window.components && window.components.showEditCaseModal) {
                        window.components.showEditCaseModal();
                    } else {
                        console.error('components对象或showEditCaseModal方法不存在');
                        alert('新建案例功能暂时不可用，请刷新页面重试');
                    }
                });
            } else {
                console.error('new-case-btn元素未找到');
            }

            // 搜索功能
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.addEventListener('input', (e) => {
                    this.searchTerm = e.target.value.toLowerCase();
                    this.filterAndRenderCases();
                });
            } else {
                console.error('search-input元素未找到');
            }

            // 刷新按钮
            const refreshBtn = document.getElementById('refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    if (this.currentPage === 'cases') {
                        this.loadCases();
                    } else if (this.currentPage === 'trash') {
                        this.loadTrashCases();
                    }
                });
            } else {
                console.error('refresh-btn元素未找到');
            }

            // 清空回收站按钮
            const emptyTrashBtn = document.getElementById('empty-trash-btn');
            if (emptyTrashBtn) {
                emptyTrashBtn.addEventListener('click', () => {
                    this.emptyTrash();
                });
            } else {
                console.error('empty-trash-btn元素未找到');
            }

        } catch (error) {
            console.error('设置事件监听器时发生错误:', error);
            alert('页面初始化失败，请刷新页面重试');
        }

        // 模态框点击外部关闭
        const modalOverlay = document.getElementById('modal-overlay');
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === e.currentTarget) {
                    if (window.components && window.components.hideModal) {
                        window.components.hideModal();
                    }
                }
            });
        } else {
            console.error('modal-overlay元素未找到');
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'n':
                        e.preventDefault();
                        components.showEditCaseModal();
                        break;
                    case 'r':
                        e.preventDefault();
                        if (this.currentPage === 'cases') {
                            this.loadCases();
                        } else if (this.currentPage === 'trash') {
                            this.loadTrashCases();
                        }
                        break;
                    case 'f':
                        e.preventDefault();
                        document.getElementById('search-input').focus();
                        break;
                }
            }
            
            if (e.key === 'Escape') {
                components.hideModal();
            }
        });
        
        // 菜单事件监听
        const { ipcRenderer } = require('electron');
        
        ipcRenderer.on('menu-new-case', () => {
            components.showEditCaseModal();
        });
        
        ipcRenderer.on('menu-open-case', () => {
            // 可以实现打开案例的功能
            console.log('打开案例菜单点击');
        });
    }
    
    // 显示页面
    showPage(page) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active', 'bg-primary-100', 'text-primary-700');
        });
        
        const activeNav = document.getElementById(`nav-${page}`);
        activeNav.classList.add('active', 'bg-primary-100', 'text-primary-700');
        
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.add('hidden');
            page.classList.remove('active');
        });
        
        // 显示当前页面
        const currentPageElement = document.getElementById(`${page}-page`);
        currentPageElement.classList.remove('hidden');
        currentPageElement.classList.add('active');
        
        this.currentPage = page;
        
        // 加载对应数据
        if (page === 'cases') {
            this.loadCases();
        } else if (page === 'trash') {
            this.loadTrashCases();
        }
        
        // 清空搜索
        document.getElementById('search-input').value = '';
        this.searchTerm = '';
    }
    
    // 加载案例列表
    async loadCases() {
        this.showLoading();

        try {
            console.log('🔄 开始加载案例...');
            let cases;

            try {
                // 首先尝试使用axios
                cases = await api.getCases();
            } catch (axiosError) {
                console.warn('⚠️ axios获取案例失败，尝试备用方法:', axiosError);
                // 如果axios失败，使用fetch备用方法
                cases = await api.getCasesFallback();
            }

            this.cases = cases;
            console.log('✅ 案例加载成功:', cases.length, '条记录');
            this.renderCases();
        } catch (error) {
            console.error('❌ 加载案例失败:', error);
            this.showError(`加载案例失败: ${error.message}`);
        }
    }
    
    // 加载回收站案例
    async loadTrashCases() {
        this.showLoading('trash');
        
        try {
            this.trashCases = await api.getTrashCases();
            this.renderTrashCases();
        } catch (error) {
            console.error('加载回收站失败:', error);
            this.showError('加载回收站失败，请检查网络连接', 'trash');
        }
    }
    
    // 渲染案例列表
    renderCases() {
        const container = document.getElementById('cases-grid');
        const loading = document.getElementById('loading');
        const emptyState = document.getElementById('empty-state');
        
        loading.classList.add('hidden');
        
        if (this.cases.length === 0) {
            container.innerHTML = '';
            emptyState.classList.remove('hidden');
            return;
        }
        
        emptyState.classList.add('hidden');
        this.filterAndRenderCases();
    }
    
    // 过滤和渲染案例（优化版本）
    filterAndRenderCases() {
        const container = document.getElementById('cases-grid');
        let filteredCases = this.cases;

        // 应用搜索过滤
        if (this.searchTerm) {
            filteredCases = this.cases.filter(case_ =>
                case_.case_name.toLowerCase().includes(this.searchTerm) ||
                (case_.description && case_.description.toLowerCase().includes(this.searchTerm))
            );
        }

        if (filteredCases.length === 0) {
            container.innerHTML = `
                <div class="col-span-full text-center py-16">
                    <svg class="w-16 h-16 mx-auto text-secondary-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-secondary-900 mb-2">未找到匹配的案例</h3>
                    <p class="text-secondary-600">尝试使用不同的搜索词</p>
                </div>
            `;
            return;
        }

        // 优化：使用DocumentFragment减少DOM操作
        const fragment = document.createDocumentFragment();
        filteredCases.forEach(case_ => {
            const card = components.createCaseCard(case_, false);
            fragment.appendChild(card);
        });

        // 一次性清空并添加所有元素
        container.innerHTML = '';
        container.appendChild(fragment);

        console.log(`✅ 渲染了 ${filteredCases.length} 个案例`);
    }
    
    // 渲染回收站案例
    renderTrashCases() {
        const container = document.getElementById('trash-grid');
        const emptyState = document.getElementById('trash-empty-state');
        
        if (this.trashCases.length === 0) {
            container.innerHTML = '';
            emptyState.classList.remove('hidden');
            return;
        }
        
        emptyState.classList.add('hidden');
        container.innerHTML = '';
        
        this.trashCases.forEach(case_ => {
            const card = components.createCaseCard(case_, true);
            container.appendChild(card);
        });
    }
    
    // 显示加载状态
    showLoading(page = 'cases') {
        if (page === 'cases') {
            document.getElementById('loading').classList.remove('hidden');
            document.getElementById('empty-state').classList.add('hidden');
            document.getElementById('cases-grid').innerHTML = '';
        } else if (page === 'trash') {
            document.getElementById('trash-grid').innerHTML = `
                <div class="col-span-full text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <p class="mt-2 text-secondary-600">加载中...</p>
                </div>
            `;
            document.getElementById('trash-empty-state').classList.add('hidden');
        }
    }

    // 隐藏加载状态
    hideLoading(page = 'cases') {
        if (page === 'cases') {
            document.getElementById('loading').classList.add('hidden');
        } else if (page === 'trash') {
            // 回收站的loading状态通过renderTrashCases方法处理
            // 这里不需要特殊处理，因为renderTrashCases会重新渲染内容
        }
    }

    // 显示错误状态
    showError(message, page = 'cases') {
        if (page === 'cases') {
            document.getElementById('loading').classList.add('hidden');
            document.getElementById('empty-state').classList.add('hidden');
            document.getElementById('cases-grid').innerHTML = `
                <div class="col-span-full text-center py-16">
                    <svg class="w-16 h-16 mx-auto text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-secondary-900 mb-2">加载失败</h3>
                    <p class="text-secondary-600 mb-4">${message}</p>
                    <button onclick="app.loadCases()" class="btn-primary">重试</button>
                </div>
            `;
        } else if (page === 'trash') {
            document.getElementById('trash-empty-state').classList.add('hidden');
            document.getElementById('trash-grid').innerHTML = `
                <div class="col-span-full text-center py-16">
                    <svg class="w-16 h-16 mx-auto text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-secondary-900 mb-2">加载失败</h3>
                    <p class="text-secondary-600 mb-4">${message}</p>
                    <button onclick="app.loadTrashCases()" class="btn-primary">重试</button>
                </div>
            `;
        }
    }
    
    // 清空回收站
    async emptyTrash() {
        const confirmed = await components.showConfirmDialog(
            '确定要清空回收站吗？',
            '此操作将永久删除回收站中的所有案例，不可撤销！'
        );

        if (confirmed) {
            try {
                this.showLoading('trash');
                const result = await api.emptyTrash();

                if (result.success) {
                    showNotification(result.message || '回收站已清空', 'success');
                    // 强制刷新回收站UI
                    await this.loadTrashCases();
                    // 同时刷新活跃案例列表，因为可能有案例从回收站彻底删除
                    await this.loadCases();
                } else {
                    showNotification(result.message || '清空回收站失败', 'error');
                }
            } catch (error) {
                console.error('清空回收站失败:', error);
                showNotification('清空回收站失败，请检查网络连接', 'error');
                this.showError('清空回收站失败，请检查网络连接', 'trash');
            } finally {
                this.hideLoading('trash');
            }
        }
    }
    
    // 检查后端连接
    async checkBackendConnection() {
        try {
            console.log('🔍 检查后端连接...');
            const result = await api.testConnection();

            if (result.success) {
                console.log('✅ 后端连接成功');
                this.showConnectionStatus(true);
                return true;
            } else {
                console.error('❌ 后端连接失败:', result);
                this.showConnectionStatus(false, result.error);
                return false;
            }
        } catch (error) {
            console.error('❌ 连接检查异常:', error);
            this.showConnectionStatus(false, error.message);
            return false;
        }
    }
    
    // 显示连接状态
    showConnectionStatus(connected, errorMessage = null) {
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            statusElement.remove();
        }

        const status = document.createElement('div');
        status.id = 'connection-status';
        status.className = `fixed bottom-4 right-4 px-3 py-2 rounded-lg text-sm font-medium ${
            connected
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
        }`;

        if (connected) {
            status.textContent = '已连接到后端';
        } else {
            status.textContent = errorMessage ? `后端连接失败: ${errorMessage}` : '后端连接失败';
        }

        document.body.appendChild(status);

        // 连接失败时显示更长时间
        const hideDelay = connected ? 3000 : 8000;
        setTimeout(() => {
            if (status.parentNode) {
                status.parentNode.removeChild(status);
            }
        }, hideDelay);
    }
    
    // 获取应用统计信息
    getStats() {
        return {
            totalCases: this.cases.length,
            activeCases: this.cases.filter(c => c.status === 'active').length,
            archivedCases: this.cases.filter(c => c.status === 'archived').length,
            trashCases: this.trashCases.length
        };
    }
}

// 应用程序启动
document.addEventListener('DOMContentLoaded', () => {
    // 创建全局应用实例
    window.app = new App();
    
    // 添加一些全局样式
    const style = document.createElement('style');
    style.textContent = `
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .nav-item.active {
            background-color: rgb(219 234 254);
            color: rgb(29 78 216);
        }
        
        .notification {
            animation: slideInRight 0.3s ease-out;
        }
        
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    `;
    document.head.appendChild(style);
    
    console.log('Mizzy Star 前端应用已启动');
}); 