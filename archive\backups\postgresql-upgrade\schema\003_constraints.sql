-- PostgreSQL约束和触发器定义
-- 确保数据完整性和自动化维护

-- =============================================================================
-- 检查约束 - 数据验证
-- =============================================================================

-- 案例表约束
ALTER TABLE cases ADD CONSTRAINT chk_cases_case_name_not_empty 
    CHECK (LENGTH(TRIM(case_name)) > 0);

ALTER TABLE cases ADD CONSTRAINT chk_cases_deleted_at_logic 
    CHECK ((status = 'deleted' AND deleted_at IS NOT NULL) OR 
           (status != 'deleted' AND deleted_at IS NULL));

ALTER TABLE cases ADD CONSTRAINT chk_cases_cover_source_file_id_positive 
    CHECK (cover_source_file_id IS NULL OR cover_source_file_id > 0);

-- 文件表约束
ALTER TABLE files ADD CONSTRAINT chk_files_file_path_not_empty 
    CHECK (LENGTH(TRIM(file_path)) > 0);

ALTER TABLE files ADD CONSTRAINT chk_files_dimensions_positive 
    CHECK ((width IS NULL OR width > 0) AND (height IS NULL OR height > 0));

ALTER TABLE files ADD CONSTRAINT chk_files_quality_score_range 
    CHECK (quality_score IS NULL OR (quality_score >= 0 AND quality_score <= 100));

ALTER TABLE files ADD CONSTRAINT chk_files_num_faces_non_negative 
    CHECK (num_faces IS NULL OR num_faces >= 0);

ALTER TABLE files ADD CONSTRAINT chk_files_frame_number_positive 
    CHECK (frame_number IS NULL OR frame_number > 0);

-- 自定义标签约束
ALTER TABLE custom_tags ADD CONSTRAINT chk_custom_tags_name_not_empty 
    CHECK (LENGTH(TRIM(tag_name)) > 0);

ALTER TABLE custom_tags ADD CONSTRAINT chk_custom_tags_color_format 
    CHECK (tag_color ~ '^#[0-9A-Fa-f]{6}$');

ALTER TABLE custom_tags ADD CONSTRAINT chk_custom_tags_display_order_non_negative 
    CHECK (display_order >= 0);

-- 标签缓存约束
ALTER TABLE tag_cache ADD CONSTRAINT chk_tag_cache_category_not_empty
    CHECK (LENGTH(TRIM(tag_category)) > 0);

ALTER TABLE tag_cache ADD CONSTRAINT chk_tag_cache_name_not_empty
    CHECK (LENGTH(TRIM(tag_name)) > 0);

ALTER TABLE tag_cache ADD CONSTRAINT chk_tag_cache_file_count_non_negative
    CHECK (file_count >= 0);

-- 标签缓存唯一约束 - 防止重复的标签缓存记录
ALTER TABLE tag_cache ADD CONSTRAINT uq_tag_cache_category_name_value
    UNIQUE (tag_category, tag_name, tag_value);

-- =============================================================================
-- 触发器 - 自动更新时间戳
-- =============================================================================

-- 系统配置表更新时间戳触发器
CREATE TRIGGER trigger_system_config_updated_at
    BEFORE UPDATE ON system_config
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 案例处理规则表更新时间戳触发器
CREATE TRIGGER trigger_case_processing_rules_updated_at
    BEFORE UPDATE ON case_processing_rules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 自定义标签表更新时间戳触发器
CREATE TRIGGER trigger_custom_tags_updated_at
    BEFORE UPDATE ON custom_tags
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 标签缓存表更新时间戳触发器
CREATE TRIGGER trigger_tag_cache_updated_at
    BEFORE UPDATE ON tag_cache
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- 标签缓存维护触发器
-- =============================================================================

-- 创建标签缓存维护函数
CREATE OR REPLACE FUNCTION maintain_tag_cache()
RETURNS TRIGGER AS $$
DECLARE
    tag_record RECORD;
    category_key TEXT;
    tag_key TEXT;
    tag_val TEXT;
BEGIN
    -- 当文件的标签发生变化时，更新标签缓存
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- 处理新的或更新的标签数据
        IF NEW.tags IS NOT NULL THEN
            -- 处理metadata标签
            FOR tag_record IN 
                SELECT key, value 
                FROM jsonb_each_text(NEW.tags->'tags'->'metadata')
            LOOP
                INSERT INTO tag_cache (tag_category, tag_name, tag_value, file_ids, file_count)
                VALUES ('metadata', tag_record.key, tag_record.value, ARRAY[NEW.id], 1)
                ON CONFLICT (tag_category, tag_name, tag_value) 
                DO UPDATE SET 
                    file_ids = array_append(
                        array_remove(tag_cache.file_ids, NEW.id), 
                        NEW.id
                    ),
                    file_count = array_length(
                        array_append(
                            array_remove(tag_cache.file_ids, NEW.id), 
                            NEW.id
                        ), 1
                    ),
                    updated_at = NOW();
            END LOOP;
            
            -- 处理user标签数组
            IF NEW.tags->'tags'->'user' IS NOT NULL THEN
                FOR tag_val IN 
                    SELECT jsonb_array_elements_text(NEW.tags->'tags'->'user')
                LOOP
                    INSERT INTO tag_cache (tag_category, tag_name, tag_value, file_ids, file_count)
                    VALUES ('user', 'user_tag', tag_val, ARRAY[NEW.id], 1)
                    ON CONFLICT (tag_category, tag_name, tag_value) 
                    DO UPDATE SET 
                        file_ids = array_append(
                            array_remove(tag_cache.file_ids, NEW.id), 
                            NEW.id
                        ),
                        file_count = array_length(
                            array_append(
                                array_remove(tag_cache.file_ids, NEW.id), 
                                NEW.id
                            ), 1
                        ),
                        updated_at = NOW();
                END LOOP;
            END IF;
            
            -- 处理ai标签数组
            IF NEW.tags->'tags'->'ai' IS NOT NULL THEN
                FOR tag_val IN 
                    SELECT jsonb_array_elements_text(NEW.tags->'tags'->'ai')
                LOOP
                    INSERT INTO tag_cache (tag_category, tag_name, tag_value, file_ids, file_count)
                    VALUES ('ai', 'ai_tag', tag_val, ARRAY[NEW.id], 1)
                    ON CONFLICT (tag_category, tag_name, tag_value) 
                    DO UPDATE SET 
                        file_ids = array_append(
                            array_remove(tag_cache.file_ids, NEW.id), 
                            NEW.id
                        ),
                        file_count = array_length(
                            array_append(
                                array_remove(tag_cache.file_ids, NEW.id), 
                                NEW.id
                            ), 1
                        ),
                        updated_at = NOW();
                END LOOP;
            END IF;
        END IF;
    END IF;
    
    -- 当文件被删除时，从标签缓存中移除
    IF TG_OP = 'DELETE' THEN
        UPDATE tag_cache 
        SET file_ids = array_remove(file_ids, OLD.id),
            file_count = array_length(array_remove(file_ids, OLD.id), 1),
            updated_at = NOW()
        WHERE OLD.id = ANY(file_ids);
        
        -- 删除空的标签缓存记录
        DELETE FROM tag_cache WHERE file_count = 0 OR file_ids = '{}';
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 创建标签缓存维护触发器
CREATE TRIGGER trigger_files_tag_cache_maintenance
    AFTER INSERT OR UPDATE OR DELETE ON files
    FOR EACH ROW
    EXECUTE FUNCTION maintain_tag_cache();

-- =============================================================================
-- 自定义标签关联维护触发器
-- =============================================================================

-- 创建自定义标签缓存维护函数
CREATE OR REPLACE FUNCTION maintain_custom_tag_cache()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- 添加到标签缓存
        INSERT INTO tag_cache (tag_category, tag_name, tag_value, file_ids, file_count)
        SELECT 'custom', ct.tag_name, ct.tag_name, ARRAY[NEW.file_id], 1
        FROM custom_tags ct WHERE ct.id = NEW.custom_tag_id
        ON CONFLICT (tag_category, tag_name, tag_value) 
        DO UPDATE SET 
            file_ids = array_append(
                array_remove(tag_cache.file_ids, NEW.file_id), 
                NEW.file_id
            ),
            file_count = array_length(
                array_append(
                    array_remove(tag_cache.file_ids, NEW.file_id), 
                    NEW.file_id
                ), 1
            ),
            updated_at = NOW();
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        -- 从标签缓存中移除
        UPDATE tag_cache 
        SET file_ids = array_remove(file_ids, OLD.file_id),
            file_count = array_length(array_remove(file_ids, OLD.file_id), 1),
            updated_at = NOW()
        WHERE tag_category = 'custom' AND OLD.file_id = ANY(file_ids);
        
        -- 删除空的标签缓存记录
        DELETE FROM tag_cache 
        WHERE tag_category = 'custom' AND (file_count = 0 OR file_ids = '{}');
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 创建自定义标签关联维护触发器
CREATE TRIGGER trigger_file_custom_tags_cache_maintenance
    AFTER INSERT OR DELETE ON file_custom_tags
    FOR EACH ROW
    EXECUTE FUNCTION maintain_custom_tag_cache();

-- =============================================================================
-- 数据完整性维护
-- =============================================================================

-- 创建清理孤立记录的函数
CREATE OR REPLACE FUNCTION cleanup_orphaned_records()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- 清理孤立的文件自定义标签关联
    DELETE FROM file_custom_tags 
    WHERE file_id NOT IN (SELECT id FROM files);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 清理空的标签缓存记录
    DELETE FROM tag_cache WHERE file_count = 0 OR file_ids = '{}';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 约束和触发器创建完成日志
DO $$
BEGIN
    RAISE NOTICE 'PostgreSQL约束和触发器创建完成!';
    RAISE NOTICE '- 数据验证约束: 确保数据格式和范围正确';
    RAISE NOTICE '- 自动时间戳: 自动更新updated_at字段';
    RAISE NOTICE '- 标签缓存维护: 自动维护标签缓存表';
    RAISE NOTICE '- 数据完整性: 防止孤立记录和数据不一致';
END
$$;
