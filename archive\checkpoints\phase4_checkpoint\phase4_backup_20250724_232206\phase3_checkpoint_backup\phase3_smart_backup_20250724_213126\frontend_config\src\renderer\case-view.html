<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案例查看 - Mizzy Star</title>
    <link href="../styles/output.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/filepond/dist/filepond.css">
    <link rel="stylesheet" href="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css">
    <style>
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            padding: 1rem;
        }
        
        .image-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .image-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .image-preview {
            width: 100%;
            height: 200px;
            object-fit: cover;
            cursor: pointer;
        }
        
        .image-info {
            padding: 0.75rem;
        }
        
        .image-name {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .image-meta {
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.2;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
        }

        .modal-content {
            position: relative;
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: row;
        }

        .modal-image-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            min-width: 0;
        }

        .modal-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        .modal-sidebar {
            width: 400px;
            background-color: rgba(30, 30, 30, 0.95);
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            max-height: 100vh;
        }

        .modal-sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            flex-shrink: 0;
        }

        .modal-sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            /* 增加滚动条长度到原来的3倍 */
            max-height: calc(100vh - 120px);
        }

        .modal-sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            flex-shrink: 0;
        }

        /* 自定义滚动条样式 */
        .modal-sidebar-content::-webkit-scrollbar {
            width: 8px;
        }

        .modal-sidebar-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .modal-sidebar-content::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        .modal-sidebar-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1001;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close:hover,
        .close:focus {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            text-decoration: none;
            transform: scale(1.1);
        }

        /* 信息面板样式 */
        .info-section {
            margin-bottom: 24px;
            padding: 16px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .info-section h4 {
            color: #60a5fa;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #d1d5db;
            font-size: 13px;
            font-weight: 500;
        }

        .info-value {
            color: #f9fafb;
            font-size: 13px;
            text-align: right;
            max-width: 200px;
            word-break: break-all;
        }

        /* 按钮样式 */
        .action-button {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            margin: 4px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
        }

        .action-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
        }

        .btn-success {
            background-color: #10b981;
            color: white;
        }

        .btn-success:hover {
            background-color: #059669;
        }

        .btn-warning {
            background-color: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background-color: #d97706;
        }

        .btn-danger {
            background-color: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background-color: #dc2626;
        }

        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #4b5563;
        }
        

        
        .stats-bar {
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .breadcrumb a {
            color: #3b82f6;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 通知容器 -->
    <div id="notifications" class="fixed top-4 right-4 z-50 space-y-2"></div>
    
    <!-- 头部导航 -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="breadcrumb">
                    <a href="index.html">案例管理</a>
                    <span>/</span>
                    <span id="case-name">加载中...</span>
                </div>
                <div class="flex space-x-4">
                    <!-- 封面管理功能已整合到批量选择模式中 -->

                    <!-- 标签按钮 -->
                    <button id="tag-management-btn" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a2 2 0 01-2-2V5a2 2 0 012-2z"></path>
                        </svg>
                        🏷️ 标签
                    </button>

                    <button id="batch-select-btn" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                        选择图片
                    </button>

                    <!-- 回收站按钮 -->
                    <button id="trash-btn" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        🗑️ 回收站
                    </button>

                    <button id="refresh-btn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                        刷新
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息栏 -->
    <div class="stats-bar">
        <div class="flex space-x-6">
            <div>
                <span class="text-gray-600">总文件数:</span>
                <span id="total-files" class="font-semibold">0</span>
            </div>
            <div>
                <span class="text-gray-600">图片文件:</span>
                <span id="image-files" class="font-semibold">0</span>
            </div>
            <div>
                <span class="text-gray-600">已分析图片:</span>
                <span id="analyzed-images" class="font-semibold">0</span>
            </div>
            <div>
                <span class="text-gray-600">相似性群组:</span>
                <span id="similarity-groups" class="font-semibold">0</span>
            </div>
            <div>
                <span class="text-gray-600">案例状态:</span>
                <span id="case-status" class="font-semibold text-green-600">活跃</span>
            </div>
            <div>
                <span class="text-gray-600">封面状态:</span>
                <span id="cover-status" class="font-semibold">
                    <span id="cover-type-badge" class="px-2 py-1 rounded-full text-xs">占位</span>
                </span>
            </div>
        </div>
        <div class="flex flex-wrap items-center gap-4">
            <!-- 文件导入按钮 -->
            <button id="import-files-btn" 
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                </svg>
                导入文件
            </button>
            
            <!-- 批量导入按钮 -->
            <button id="batch-import-btn" 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
                批量导入
            </button>
            
            <!-- 分隔线 -->
            <div class="h-6 w-px bg-gray-300"></div>
            
            <!-- 排序和搜索 -->
            <select id="sort-select" class="border border-gray-300 rounded px-3 py-1">
                <option value="name">按文件名排序</option>
                <option value="date">按创建时间排序</option>
                <option value="size">按文件大小排序</option>
            </select>
            <input type="text" id="search-input" placeholder="搜索文件..." 
                   class="border border-gray-300 rounded px-3 py-1 w-64">
        </div>
    </div>

    <!-- 批量选择操作栏 -->
    <div id="batch-actions" class="bg-blue-50 border-b border-blue-200 px-4 py-3 hidden">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <span class="text-sm text-blue-700">
                    已选择 <span id="selected-count">0</span> 张图片
                </span>
                <button id="select-all-btn" class="text-sm text-blue-600 hover:text-blue-800">
                    全选
                </button>
                <button id="deselect-all-btn" class="text-sm text-blue-600 hover:text-blue-800">
                    取消全选
                </button>
            </div>
            <div class="flex items-center space-x-2">
                <button id="set-cover-btn"
                        class="bg-purple-500 hover:bg-purple-700 text-white text-sm font-medium py-2 px-4 rounded"
                        title="设置选中的图片为封面">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    设为封面
                </button>
                <button id="batch-delete-btn"
                        class="bg-red-500 hover:bg-red-700 text-white text-sm font-medium py-2 px-4 rounded"
                        title="删除选中的图片">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    删除选中
                </button>
                <button id="batch-quality-analysis-btn"
                        class="bg-blue-500 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    分析选中图片
                </button>
                <button id="exit-batch-mode-btn"
                        class="bg-gray-500 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded">
                    退出批量模式
                </button>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="container mx-auto px-4 py-6">


        <!-- 错误提示 -->
        <div id="error" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <span id="error-message">加载失败</span>
        </div>

        <!-- 空状态 -->
        <div id="empty-state" class="hidden text-center py-12">
            <div class="text-gray-400 text-6xl mb-4">📁</div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无图片文件</h3>
            <p class="text-gray-500 mb-4">这个案例中还没有添加任何图片文件。</p>
            <button id="upload-first-btn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                上传第一张图片
            </button>
        </div>

        <!-- 图片网格 -->
        <div id="image-grid" class="image-grid hidden">
            <!-- 图片卡片会在这里动态生成 -->
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div id="image-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>

            <!-- 图片显示区域 -->
            <div class="modal-image-container">
                <img id="modal-image" class="modal-image" src="" alt="">
            </div>

            <!-- 侧边栏信息面板 -->
            <div class="modal-sidebar">
                <!-- 头部 -->
                <div class="modal-sidebar-header">
                    <h3 id="modal-title" class="text-lg font-semibold text-white mb-2">图片详情</h3>
                    <p id="modal-subtitle" class="text-sm text-gray-300">文件信息和操作</p>
                </div>

                <!-- 内容区域（可滚动） -->
                <div class="modal-sidebar-content">
                    <div id="modal-info">
                        <!-- 图片详细信息会在这里显示 -->
                    </div>
                </div>

                <!-- 底部操作区域 -->
                <div class="modal-sidebar-footer">
                    <div id="modal-actions" class="flex flex-wrap gap-2">
                        <!-- 操作按钮会在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div id="batch-import-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">批量导入图片</h3>
                    <button id="close-batch-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <form id="batch-import-form" class="p-6">
                <div class="mb-4">
                    <label for="directory-path" class="block text-sm font-medium text-gray-700">目录路径:</label>
                    <input type="text" id="directory-path" name="directory-path" 
                           class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="C:\Images\MyPhotos">
                </div>
                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="recursive" name="recursive" checked 
                               class="mr-2">
                        <span class="text-sm text-gray-700">包含子目录</span>
                    </label>
                </div>
                <div class="mb-4">
                    <label for="batch-size" class="block text-sm font-medium text-gray-700">批次大小:</label>
                    <input type="number" id="batch-size" name="batch-size" value="100" min="10" max="1000"
                           class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancel-import" 
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        取消
                    </button>
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        开始导入
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 文件导入模态框 -->
    <div id="import-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col">
            <div class="px-6 py-4 border-b border-gray-200 flex-shrink-0">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">导入文件</h3>
                    <button id="close-import-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-6 flex-1 overflow-y-auto">
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-4">选择要导入的图片文件，支持拖拽上传。</p>
                    
                    <!-- FilePond 文件上传区域 -->
                    <div id="filepond-container">
                        <input type="file" id="filepond" name="files" multiple accept="image/*">
                    </div>
                </div>
                
                <!-- 上传进度 -->
                <div id="upload-progress" class="hidden mb-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">上传进度</span>
                        <span id="progress-text" class="text-sm text-gray-500">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progress-bar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
                
            </div>
            <!-- 操作按钮区域 - 固定在底部 -->
            <div class="px-6 py-4 border-t border-gray-200 flex-shrink-0">
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancel-file-import" 
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        取消
                    </button>
                    <button type="button" id="start-file-import" 
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        开始上传
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 回收站模态框 -->
    <div id="trash-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- 回收站标题栏 -->
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900">回收站</h3>
                        <span id="trash-count" class="ml-2 px-2 py-1 bg-orange-100 text-orange-800 text-sm rounded-full">0</span>
                    </div>
                    <button id="close-trash-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- 回收站操作栏 -->
                <div class="flex justify-between items-center mb-4 p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">已删除的文件将在此显示</span>
                    </div>
                    <div class="flex space-x-2">
                        <button id="restore-all-btn" class="bg-green-500 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded disabled:opacity-50" disabled>
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                            </svg>
                            恢复全部
                        </button>
                        <button id="empty-trash-btn" class="bg-red-500 hover:bg-red-700 text-white text-sm font-medium py-2 px-4 rounded disabled:opacity-50" disabled>
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            清空回收站
                        </button>
                    </div>
                </div>

                <!-- 回收站文件列表 -->
                <div id="trash-files-container" class="max-h-96 overflow-y-auto">
                    <div id="trash-loading" class="text-center py-8 hidden">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                        <p class="mt-2 text-gray-600">加载中...</p>
                    </div>
                    <div id="trash-empty" class="text-center py-12 text-gray-500 hidden">
                        <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        <p class="text-lg">回收站为空</p>
                        <p class="text-sm">删除的文件将显示在这里</p>
                    </div>
                    <div id="trash-files-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 p-4 hidden">
                        <!-- 回收站文件项将在这里动态生成 -->
                    </div>
                </div>

                <!-- 回收站底部按钮 -->
                <div class="flex justify-end mt-4 pt-4 border-t">
                    <button id="close-trash-modal-btn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 必需的依赖库 -->
    <script src="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.js"></script>
    <script src="https://unpkg.com/filepond-plugin-image-resize/dist/filepond-plugin-image-resize.js"></script>
    <script src="https://unpkg.com/filepond/dist/filepond.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <!-- 应用脚本 -->
    <script src="js/api.js"></script>
    <script src="js/case-view.js"></script>
</body>
</html> 