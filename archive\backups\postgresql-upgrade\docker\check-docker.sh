#!/bin/bash

# Docker环境检查和安装指导脚本
# 用于检查Docker是否可用并提供安装指导

echo "🔍 Docker环境检查"
echo "=================="

# 检查操作系统
OS=$(uname -s)
echo "操作系统: $OS"

# 检查Docker命令是否存在
if command -v docker &> /dev/null; then
    echo "✅ Docker命令已找到"
    
    # 检查Docker是否运行
    if docker info &> /dev/null; then
        echo "✅ Docker服务正在运行"
        
        # 显示Docker版本信息
        echo ""
        echo "📋 Docker版本信息:"
        docker --version
        docker-compose --version 2>/dev/null || echo "⚠️  docker-compose未找到"
        
        # 检查Docker资源
        echo ""
        echo "💾 Docker资源状态:"
        docker system df
        
        echo ""
        echo "🎉 Docker环境检查通过！可以继续启动PostgreSQL"
        echo "   运行: ./start.sh"
        
    else
        echo "❌ Docker服务未运行"
        echo ""
        echo "🔧 解决方案:"
        case $OS in
            "Linux")
                echo "   sudo systemctl start docker"
                echo "   sudo systemctl enable docker"
                ;;
            "Darwin")
                echo "   启动Docker Desktop应用程序"
                ;;
            *)
                echo "   启动Docker Desktop应用程序"
                ;;
        esac
    fi
    
else
    echo "❌ Docker命令未找到"
    echo ""
    echo "📥 Docker安装指导:"
    echo ""
    
    case $OS in
        "Linux")
            echo "🐧 Linux安装方法:"
            echo "   # Ubuntu/Debian:"
            echo "   curl -fsSL https://get.docker.com -o get-docker.sh"
            echo "   sudo sh get-docker.sh"
            echo "   sudo usermod -aG docker \$USER"
            echo ""
            echo "   # CentOS/RHEL:"
            echo "   sudo yum install -y docker"
            echo "   sudo systemctl start docker"
            echo "   sudo systemctl enable docker"
            ;;
        "Darwin")
            echo "🍎 macOS安装方法:"
            echo "   1. 访问: https://www.docker.com/products/docker-desktop/"
            echo "   2. 下载Docker Desktop for Mac"
            echo "   3. 安装并启动Docker Desktop"
            echo ""
            echo "   或使用Homebrew:"
            echo "   brew install --cask docker"
            ;;
        *)
            echo "🪟 Windows安装方法:"
            echo "   1. 访问: https://www.docker.com/products/docker-desktop/"
            echo "   2. 下载Docker Desktop for Windows"
            echo "   3. 安装并重启系统"
            echo "   4. 启动Docker Desktop"
            echo ""
            echo "   系统要求:"
            echo "   - Windows 10/11 Pro, Enterprise, or Education"
            echo "   - 启用Hyper-V和容器功能"
            echo "   - 或者使用WSL2后端"
            ;;
    esac
    
    echo ""
    echo "📚 安装完成后:"
    echo "   1. 重启终端或重新登录"
    echo "   2. 运行: docker --version"
    echo "   3. 运行: ./check-docker.sh"
    echo "   4. 运行: ./start.sh"
fi

echo ""
echo "🔗 有用的链接:"
echo "   Docker官网: https://www.docker.com/"
echo "   Docker文档: https://docs.docker.com/"
echo "   PostgreSQL Docker镜像: https://hub.docker.com/_/postgres"

echo ""
echo "❓ 如果遇到问题:"
echo "   1. 检查系统要求"
echo "   2. 确保有管理员权限"
echo "   3. 检查防火墙设置"
echo "   4. 查看Docker Desktop日志"
