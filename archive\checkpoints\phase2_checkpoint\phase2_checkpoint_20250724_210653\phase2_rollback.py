#!/usr/bin/env python3
# 第二阶段回滚脚本
# 检查点路径: C:\Users\<USER>\mizzy_star_v0.3\phase2_checkpoint\phase2_checkpoint_20250724_210653
# 生成时间: 2025-07-24T21:06:53.288950

import shutil
from pathlib import Path

def rollback_phase2():
    print("🔄 开始回滚到第二阶段检查点...")
    
    project_root = Path(__file__).parent.parent
    backend_root = project_root / "backend"
    checkpoint_path = Path("C:\Users\<USER>\mizzy_star_v0.3\phase2_checkpoint\phase2_checkpoint_20250724_210653")
    
    try:
        # 回滚数据库文件
        db_files = [
            "database.py",
            "database_async.py", 
            "database_config.py",
            "database_manager.py"
        ]
        
        src_dir = backend_root / "src"
        
        for db_file in db_files:
            backup_file = checkpoint_path / db_file
            target_file = src_dir / db_file
            
            if backup_file.exists():
                shutil.copy2(backup_file, target_file)
                print(f"✅ 回滚数据库文件: {db_file}")
            else:
                print(f"⚠️ 备份文件不存在: {db_file}")
        
        # 回滚main.py
        main_backup = checkpoint_path / "main.py"
        main_target = src_dir / "main.py"
        
        if main_backup.exists():
            shutil.copy2(main_backup, main_target)
            print("✅ 回滚main.py")
        
        print("🎉 第二阶段回滚完成！")
        
    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    rollback_phase2()
