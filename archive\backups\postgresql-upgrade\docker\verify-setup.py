#!/usr/bin/env python3
"""
PostgreSQL环境验证脚本
支持Docker和本地安装两种方式
"""

import os
import sys
import subprocess
import time
from typing import Dict, List, Tuple

def print_header(title: str):
    """打印标题"""
    print(f"\n{'='*50}")
    print(f"🔍 {title}")
    print(f"{'='*50}")

def print_success(message: str):
    """打印成功消息"""
    print(f"✅ {message}")

def print_error(message: str):
    """打印错误消息"""
    print(f"❌ {message}")

def print_warning(message: str):
    """打印警告消息"""
    print(f"⚠️  {message}")

def print_info(message: str):
    """打印信息消息"""
    print(f"ℹ️  {message}")

def check_docker_available() -> bool:
    """检查Docker是否可用"""
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success(f"Docker可用: {result.stdout.strip()}")
            return True
        else:
            print_error("Docker命令执行失败")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        print_error("Docker未安装或不在PATH中")
        return False

def check_docker_running() -> bool:
    """检查Docker服务是否运行"""
    try:
        result = subprocess.run(['docker', 'info'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success("Docker服务正在运行")
            return True
        else:
            print_error("Docker服务未运行")
            return False
    except (subprocess.TimeoutExpired, subprocess.SubprocessError):
        print_error("无法检查Docker服务状态")
        return False

def check_docker_compose() -> bool:
    """检查docker-compose是否可用"""
    try:
        result = subprocess.run(['docker-compose', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success(f"docker-compose可用: {result.stdout.strip()}")
            return True
        else:
            # 尝试docker compose (新版本)
            result = subprocess.run(['docker', 'compose', 'version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print_success(f"docker compose可用: {result.stdout.strip()}")
                return True
            else:
                print_error("docker-compose不可用")
                return False
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        print_error("docker-compose未找到")
        return False

def check_postgresql_local() -> bool:
    """检查本地PostgreSQL安装"""
    try:
        # 检查psql命令
        result = subprocess.run(['psql', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success(f"本地PostgreSQL可用: {result.stdout.strip()}")
            return True
        else:
            print_error("psql命令执行失败")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        print_error("本地PostgreSQL未安装或不在PATH中")
        return False

def test_postgresql_connection(use_docker: bool = True) -> bool:
    """测试PostgreSQL连接"""
    try:
        if use_docker:
            # Docker方式连接
            cmd = [
                'docker-compose', 'exec', '-T', 'postgres',
                'psql', '-U', 'mizzy_user', '-d', 'mizzy_main',
                '-c', 'SELECT version();'
            ]
        else:
            # 本地方式连接
            cmd = [
                'psql', '-h', 'localhost', '-p', '5432',
                '-U', 'mizzy_user', '-d', 'mizzy_main',
                '-c', 'SELECT version();'
            ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print_success("PostgreSQL连接测试成功")
            return True
        else:
            print_error(f"PostgreSQL连接失败: {result.stderr}")
            return False
    except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
        print_error(f"连接测试异常: {e}")
        return False

def check_extensions(use_docker: bool = True) -> List[str]:
    """检查已安装的扩展"""
    try:
        if use_docker:
            cmd = [
                'docker-compose', 'exec', '-T', 'postgres',
                'psql', '-U', 'mizzy_user', '-d', 'mizzy_main',
                '-t', '-c', 'SELECT name FROM pg_extension;'
            ]
        else:
            cmd = [
                'psql', '-h', 'localhost', '-p', '5432',
                '-U', 'mizzy_user', '-d', 'mizzy_main',
                '-t', '-c', 'SELECT name FROM pg_extension;'
            ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            extensions = [line.strip() for line in result.stdout.split('\n') if line.strip()]
            print_success(f"已安装扩展: {', '.join(extensions)}")
            return extensions
        else:
            print_error("无法获取扩展列表")
            return []
    except (subprocess.TimeoutExpired, subprocess.SubprocessError):
        print_error("检查扩展时发生异常")
        return []

def main():
    """主函数"""
    print_header("PostgreSQL环境验证")
    
    # 检查Docker环境
    print_header("Docker环境检查")
    docker_available = check_docker_available()
    docker_running = False
    docker_compose_available = False
    
    if docker_available:
        docker_running = check_docker_running()
        docker_compose_available = check_docker_compose()
    
    # 检查本地PostgreSQL
    print_header("本地PostgreSQL检查")
    postgresql_local = check_postgresql_local()
    
    # 确定使用哪种方式
    use_docker = docker_available and docker_running and docker_compose_available
    use_local = postgresql_local
    
    print_header("环境选择")
    if use_docker:
        print_success("将使用Docker环境进行测试")
        
        # 检查Docker容器状态
        try:
            result = subprocess.run(['docker-compose', 'ps'], 
                                  capture_output=True, text=True, timeout=10)
            if 'postgres' in result.stdout and 'Up' in result.stdout:
                print_success("PostgreSQL容器正在运行")
            else:
                print_warning("PostgreSQL容器未运行，尝试启动...")
                # 这里可以尝试启动容器
        except:
            pass
            
    elif use_local:
        print_success("将使用本地PostgreSQL进行测试")
    else:
        print_error("没有可用的PostgreSQL环境")
        print_info("请选择以下方案之一:")
        print_info("1. 安装Docker并启动PostgreSQL容器")
        print_info("2. 安装本地PostgreSQL")
        return False
    
    # 连接测试
    print_header("连接测试")
    if use_docker:
        connection_ok = test_postgresql_connection(use_docker=True)
    else:
        connection_ok = test_postgresql_connection(use_docker=False)
    
    if not connection_ok:
        print_error("连接测试失败，请检查配置")
        return False
    
    # 扩展检查
    print_header("扩展检查")
    extensions = check_extensions(use_docker=use_docker)
    
    required_extensions = ['pg_stat_statements', 'btree_gin']
    missing_extensions = [ext for ext in required_extensions if ext not in extensions]
    
    if missing_extensions:
        print_warning(f"缺少必需扩展: {', '.join(missing_extensions)}")
    else:
        print_success("所有必需扩展已安装")
    
    # 总结
    print_header("验证总结")
    if connection_ok and not missing_extensions:
        print_success("🎉 PostgreSQL环境验证通过！")
        print_info("可以继续进行Schema设计和应用开发")
        return True
    else:
        print_error("❌ 环境验证未完全通过")
        print_info("请解决上述问题后重新验证")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
