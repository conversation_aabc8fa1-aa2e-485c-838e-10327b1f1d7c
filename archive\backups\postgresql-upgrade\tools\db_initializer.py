#!/usr/bin/env python3
"""
PostgreSQL数据库初始化工具 for Mizzy Star
自动化Schema部署、配置和验证
"""

import os
import sys
import time
import argparse
import psycopg2
from psycopg2.extras import RealDictCursor
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.conn = None
        self.schema_dir = Path(__file__).parent.parent / 'schema'
        
        # Schema文件执行顺序
        self.schema_files = [
            '001_initial_schema.sql',
            '002_indexes.sql', 
            '003_constraints.sql',
            '004_functions.sql'
        ]
    
    def connect_db(self, database: str = None):
        """连接数据库"""
        config = self.db_config.copy()
        if database:
            config['database'] = database
            
        try:
            self.conn = psycopg2.connect(**config)
            self.conn.autocommit = True
            logger.info(f"连接到数据库: {config['database']}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def close_db(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
    
    def check_database_exists(self, database_name: str) -> bool:
        """检查数据库是否存在"""
        try:
            # 连接到postgres数据库检查
            self.connect_db('postgres')
            
            with self.conn.cursor() as cursor:
                cursor.execute(
                    "SELECT 1 FROM pg_database WHERE datname = %s",
                    (database_name,)
                )
                exists = cursor.fetchone() is not None
                
            self.close_db()
            return exists
            
        except Exception as e:
            logger.error(f"检查数据库存在性失败: {e}")
            return False
    
    def create_database(self, database_name: str):
        """创建数据库"""
        try:
            self.connect_db('postgres')
            
            with self.conn.cursor() as cursor:
                cursor.execute(f'CREATE DATABASE "{database_name}"')
                logger.info(f"数据库 {database_name} 创建成功")
                
            self.close_db()
            
        except Exception as e:
            logger.error(f"创建数据库失败: {e}")
            raise
    
    def check_extensions(self) -> dict:
        """检查必需的扩展"""
        required_extensions = ['pg_stat_statements', 'btree_gin']
        extension_status = {}
        
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("""
                    SELECT extname, extversion 
                    FROM pg_extension 
                    WHERE extname = ANY(%s)
                """, (required_extensions,))
                
                installed = {row['extname']: row['extversion'] for row in cursor.fetchall()}
                
                for ext in required_extensions:
                    extension_status[ext] = {
                        'installed': ext in installed,
                        'version': installed.get(ext, None)
                    }
                    
        except Exception as e:
            logger.error(f"检查扩展失败: {e}")
            
        return extension_status
    
    def install_extensions(self):
        """安装必需的扩展"""
        extensions = ['pg_stat_statements', 'btree_gin']
        
        try:
            with self.conn.cursor() as cursor:
                for ext in extensions:
                    cursor.execute(f'CREATE EXTENSION IF NOT EXISTS {ext}')
                    logger.info(f"扩展 {ext} 安装/验证完成")
                    
        except Exception as e:
            logger.error(f"安装扩展失败: {e}")
            raise
    
    def execute_sql_file(self, file_path: Path) -> bool:
        """执行SQL文件"""
        try:
            logger.info(f"执行SQL文件: {file_path.name}")

            with open(file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()

            # 直接执行整个文件内容，让PostgreSQL处理语句分割
            with self.conn.cursor() as cursor:
                try:
                    cursor.execute(sql_content)
                    logger.info(f"✅ {file_path.name} 执行完成")
                    return True
                except Exception as e:
                    logger.error(f"❌ 执行 {file_path.name} 失败: {e}")
                    return False

        except Exception as e:
            logger.error(f"❌ 读取 {file_path.name} 失败: {e}")
            return False
    
    def deploy_schema(self) -> bool:
        """部署Schema"""
        logger.info("开始部署PostgreSQL Schema...")
        
        success_count = 0
        for schema_file in self.schema_files:
            file_path = self.schema_dir / schema_file
            
            if not file_path.exists():
                logger.error(f"Schema文件不存在: {file_path}")
                continue
                
            if self.execute_sql_file(file_path):
                success_count += 1
            else:
                logger.error(f"Schema文件执行失败: {schema_file}")
                return False
        
        logger.info(f"Schema部署完成: {success_count}/{len(self.schema_files)} 个文件成功")
        return success_count == len(self.schema_files)
    
    def verify_schema(self) -> dict:
        """验证Schema部署"""
        logger.info("验证Schema部署...")
        
        verification_results = {
            'tables': {},
            'indexes': {},
            'functions': {},
            'triggers': {}
        }
        
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # 检查表
                expected_tables = [
                    'system_config', 'cases', 'case_processing_rules',
                    'files', 'tag_cache', 'custom_tags', 'file_custom_tags', 'deleted_files'
                ]
                
                cursor.execute("""
                    SELECT tablename 
                    FROM pg_tables 
                    WHERE schemaname = 'public' AND tablename = ANY(%s)
                """, (expected_tables,))
                
                existing_tables = [row['tablename'] for row in cursor.fetchall()]
                
                for table in expected_tables:
                    verification_results['tables'][table] = table in existing_tables
                
                # 检查关键索引
                cursor.execute("""
                    SELECT indexname 
                    FROM pg_indexes 
                    WHERE schemaname = 'public' AND indexname LIKE 'idx_%'
                """)
                
                indexes = [row['indexname'] for row in cursor.fetchall()]
                verification_results['indexes']['total_count'] = len(indexes)
                verification_results['indexes']['gin_indexes'] = len([idx for idx in indexes if 'gin' in idx])
                
                # 检查函数
                cursor.execute("""
                    SELECT proname 
                    FROM pg_proc 
                    WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
                    AND proname IN ('search_files_by_tags', 'rebuild_tag_cache', 'database_health_check')
                """)
                
                functions = [row['proname'] for row in cursor.fetchall()]
                expected_functions = ['search_files_by_tags', 'rebuild_tag_cache', 'database_health_check']
                
                for func in expected_functions:
                    verification_results['functions'][func] = func in functions
                
                # 检查触发器
                cursor.execute("""
                    SELECT tgname 
                    FROM pg_trigger 
                    WHERE tgname LIKE 'trigger_%'
                """)
                
                triggers = [row['tgname'] for row in cursor.fetchall()]
                verification_results['triggers']['total_count'] = len(triggers)
                
        except Exception as e:
            logger.error(f"Schema验证失败: {e}")
            
        return verification_results
    
    def run_health_check(self) -> dict:
        """运行数据库健康检查"""
        logger.info("运行数据库健康检查...")
        
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("SELECT * FROM database_health_check()")
                health_results = cursor.fetchall()
                
                health_status = {}
                for result in health_results:
                    health_status[result['check_name']] = {
                        'status': result['status'],
                        'details': result['details'],
                        'recommendation': result['recommendation']
                    }
                
                return health_status
                
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {}
    
    def initialize_database(self, force_recreate: bool = False) -> bool:
        """完整的数据库初始化流程"""
        database_name = self.db_config['database']
        
        try:
            # 1. 检查/创建数据库
            if not self.check_database_exists(database_name):
                logger.info(f"数据库 {database_name} 不存在，正在创建...")
                self.create_database(database_name)
            elif force_recreate:
                logger.warning(f"强制重建数据库 {database_name}")
                self.connect_db('postgres')
                with self.conn.cursor() as cursor:
                    cursor.execute(f'DROP DATABASE IF EXISTS "{database_name}"')
                    cursor.execute(f'CREATE DATABASE "{database_name}"')
                self.close_db()
            
            # 2. 连接到目标数据库
            self.connect_db(database_name)
            
            # 3. 安装扩展
            logger.info("安装必需的扩展...")
            self.install_extensions()
            
            # 4. 部署Schema
            if not self.deploy_schema():
                logger.error("Schema部署失败")
                return False
            
            # 5. 验证部署
            verification = self.verify_schema()
            
            # 6. 显示验证结果
            logger.info("=== Schema验证结果 ===")
            
            # 表验证
            table_success = all(verification['tables'].values())
            logger.info(f"表创建: {'✅ 成功' if table_success else '❌ 失败'}")
            for table, exists in verification['tables'].items():
                status = '✅' if exists else '❌'
                logger.info(f"  {status} {table}")
            
            # 索引验证
            logger.info(f"索引创建: ✅ {verification['indexes']['total_count']} 个索引")
            logger.info(f"  GIN索引: {verification['indexes']['gin_indexes']} 个")
            
            # 函数验证
            func_success = all(verification['functions'].values())
            logger.info(f"函数创建: {'✅ 成功' if func_success else '❌ 失败'}")
            
            # 触发器验证
            logger.info(f"触发器创建: ✅ {verification['triggers']['total_count']} 个触发器")
            
            # 7. 运行健康检查
            health_status = self.run_health_check()
            if health_status:
                logger.info("=== 数据库健康检查 ===")
                for check_name, result in health_status.items():
                    status_icon = '✅' if result['status'] == 'OK' else '⚠️'
                    logger.info(f"{status_icon} {check_name}: {result['details']}")
            
            logger.info("🎉 数据库初始化完成!")
            return table_success and func_success
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False
            
        finally:
            self.close_db()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PostgreSQL数据库初始化工具')
    parser.add_argument('--host', default='localhost', help='数据库主机')
    parser.add_argument('--port', default='5432', help='数据库端口')
    parser.add_argument('--database', default='mizzy_main', help='数据库名')
    parser.add_argument('--user', default='mizzy_user', help='数据库用户')
    parser.add_argument('--password', default='secure_password_123', help='数据库密码')
    parser.add_argument('--force-recreate', action='store_true', help='强制重建数据库')
    parser.add_argument('--verify-only', action='store_true', help='仅验证现有Schema')
    
    args = parser.parse_args()
    
    db_config = {
        'host': args.host,
        'port': args.port,
        'database': args.database,
        'user': args.user,
        'password': args.password
    }
    
    initializer = DatabaseInitializer(db_config)
    
    if args.verify_only:
        logger.info("仅验证模式...")
        try:
            initializer.connect_db()
            verification = initializer.verify_schema()
            health_status = initializer.run_health_check()
            
            print("\n=== 验证结果 ===")
            print("表状态:")
            for table, exists in verification['tables'].items():
                print(f"  {'✅' if exists else '❌'} {table}")
            
            print(f"\n索引: {verification['indexes']['total_count']} 个")
            print(f"函数: {len([f for f in verification['functions'].values() if f])} 个")
            print(f"触发器: {verification['triggers']['total_count']} 个")
            
            if health_status:
                print("\n健康检查:")
                for check_name, result in health_status.items():
                    print(f"  {'✅' if result['status'] == 'OK' else '⚠️'} {check_name}: {result['details']}")
                    
        finally:
            initializer.close_db()
    else:
        success = initializer.initialize_database(force_recreate=args.force_recreate)
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
