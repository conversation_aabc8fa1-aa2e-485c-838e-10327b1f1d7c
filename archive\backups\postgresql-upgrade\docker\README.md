# PostgreSQL Docker环境

## 概述
本目录包含PostgreSQL Docker环境的配置文件，用于Mizzy Star项目的数据库升级。

## 文件说明
- `docker-compose.yml` - Docker Compose配置文件
- `postgresql.conf` - PostgreSQL性能优化配置
- `init.sql` - 数据库初始化脚本
- `.env.example` - 环境变量模板
- `start.sh` - 自动启动脚本
- `stop.sh` - 安全停止脚本

## 快速启动

### 方法1: 使用自动脚本 (推荐)
```bash
# 启动PostgreSQL环境
./start.sh

# 启动PostgreSQL + pgAdmin
./start.sh --with-admin

# 重新构建并启动
./start.sh --rebuild
```

### 方法2: 手动启动
```bash
# 复制环境变量文件
cp .env.example .env
# 编辑 .env 文件，设置安全的密码

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f postgres

# 连接数据库
docker-compose exec postgres psql -U mizzy_user -d mizzy_main
```

## 停止服务
```bash
# 使用脚本停止 (推荐)
./stop.sh

# 强制停止并删除数据
./stop.sh --remove-data

# 手动停止
docker-compose down
```

## 常用命令
```bash
# 查看状态
docker-compose ps

# 重启服务
docker-compose restart postgres

# 查看日志
docker-compose logs -f postgres

# 备份数据库
docker-compose exec postgres pg_dump -U mizzy_user mizzy_main > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U mizzy_user -d mizzy_main < backup.sql
```

## 配置说明
- PostgreSQL 15
- 优化配置用于SSD存储
- 支持JSONB和全文搜索
- 预装pg_stat_statements扩展
