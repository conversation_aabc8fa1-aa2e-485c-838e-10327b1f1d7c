body {
  color: #86a5b1;
  background-color: #2f3241;
  font-family: Roboto, -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Segoe UI", "Oxygen", "Ubuntu", "Cantarell", "Open Sans", sans-serif;
  margin: 0;
  display: flex;
  flex-direction: column;
}

.container {
  margin: 15px 30px;
  background-color: #2f3241;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.svg-stroke {
  stroke: #9feaf9;
}

.svg-fill {
  fill: #9feaf9;
}

.vertical-middle {
  vertical-align: middle !important;
}

h2, h4, p {
  text-align: center;
}

h4 {
  font-weight: normal;
  margin: 0;
  line-height: 3;
}

.hero-icons {
  transform-origin: 50% 50%;
}

hero-icon.loop-3 {
  transform: translate(79px, 21px);
  opacity: 1;
}

.hero-icon {
  fill: #c2f5ff;
  opacity: 1;
  transform-origin: 50% 50%;
}

.hero-app {
  fill: #71abb7;
  transform-origin: 50% 50%;
}

a {
  color: #86a5b1;
  text-decoration: none;
  transition: all 0.2s;
}

a:hover {
  color: #c2f5ff;
  text-decoration: none;
}

pre, code, .code {
  font-family: "Menlo", "Lucida Console", monospace;
  color: #c2f5ff;
}

pre {
  background-color: #26282E;
  white-space: pre-wrap;
  line-height: 2.5;
  overflow: auto;
  margin: 0 auto;
  display: inline-block;
  padding: 6px 15px;
  text-align: center;
  border-radius: 3px;
}

pre.with-prompt:before {
  content: "$ ";
  opacity: 0.7;
}

code {
  padding: 1px 4px;
  font-size: 14px;
  text-align: center;
}

.versions {
  list-style: none;
  margin: 0 auto;
  padding: 0;
  float: none;
  clear: both;
  overflow: hidden;
}

.versions li {
  display: block;
  float: left;
  border-right: 1px solid rgba(194, 245, 255, 0.4);
  padding: 0 20px;
  font-size: 13px;
  opacity: 0.8;
}

.versions li:last-child {
  border: none;
}

nav {
  margin: 40px 0 0 0;
}

.linkcol {
  width: 19%;
  display: inline-block;
  text-align: center;
}

.hero-octicon {
  display: block;
  width: 80px;
  height: 80px;
  margin: 0;
  padding: 0;
  font-size: 42px !important;
  color: #9feaf9;
  text-align: center;
  background-color: rgba(194, 245, 255, 0.1);
  border-radius: 50%;
}

.hero-octicon svg {
  display: block;
  padding-top: 20px;
  height: 42px;
  width: 42px;
  margin: 0 auto;
}

.octicon-gist:before { padding-left: 10px; }
.octicon-gear:before { padding-left: 5px; }
.octicon-star:before { padding-left: 6px; }
.octicon-gift:before { padding-left: 2px; }