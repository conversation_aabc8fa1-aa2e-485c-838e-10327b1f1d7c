// WorkspaceWrapper - Feature flag controlled workspace component wrapper
// 功能开关控制的工作台组件包装器

import React from 'react';
import { useFeatureFlag } from '@/utils/hooks/useFeatureFlags';
import { Workspace } from './Workspace';
import { WorkbenchPanel } from '@/components/panels/WorkbenchPanel';
import { useUIStore } from '@/store';

// ============================================================================
// 接口定义
// ============================================================================

interface WorkspaceWrapperProps {
  onToggleWorkspace?: () => void;
  onSwapGalleryWorkspace?: () => void;
  galleryWorkspaceSwapped?: boolean;
  className?: string;
}

// ============================================================================
// WorkspaceWrapper 组件
// ============================================================================

export const WorkspaceWrapper: React.FC<WorkspaceWrapperProps> = (props) => {
  // 检查功能开关
  const useNewWorkspace = useFeatureFlag('useNewWorkspace');
  
  // 获取状态
  const {
    activeWorkbench,
    clipboardFiles,
    setActiveWorkbench,
    addToClipboard,
    removeFromClipboard,
    updateClipboardPosition,
  } = useUIStore();

  // 如果启用新工作台，使用新组件
  if (useNewWorkspace) {
    return <Workspace {...props} />;
  }

  // 否则使用原有的WorkbenchPanel
  const handleWorkbenchChange = (type: 'clipboard' | 'cluster' | null) => {
    setActiveWorkbench(type);
  };

  const handleClipboardFileMove = (fileId: number, position: { x: number; y: number }) => {
    updateClipboardPosition(fileId, position);
  };

  const handleClusterOperation = (
    operation: 'setMain' | 'addSimilar' | 'addVersion' | 'remove', 
    fileId: number
  ) => {
    // TODO: 实现图像簇操作逻辑
    console.log('Cluster operation:', operation, fileId);
  };

  // 转换剪贴板文件格式
  const formattedClipboardFiles = clipboardFiles.map(file => ({
    id: file.id,
    fileName: `文件_${file.id}`,
    filePath: `/files/${file.id}`,
    fileType: 'image/jpeg',
    fileSize: 1024 * 1024, // 1MB 默认
    thumbnailPath: undefined,
    position: file.position,
  }));

  return (
    <WorkbenchPanel
      activeWorkbench={activeWorkbench}
      clipboardFiles={formattedClipboardFiles}
      clusterData={undefined} // TODO: 连接真实的图像簇数据
      onWorkbenchChange={handleWorkbenchChange}
      onClipboardFileMove={handleClipboardFileMove}
      onClusterOperation={handleClusterOperation}
      {...props}
    />
  );
};

// ============================================================================
// 导出
// ============================================================================

export default WorkspaceWrapper;
