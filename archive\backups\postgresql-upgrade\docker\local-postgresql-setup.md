# 本地PostgreSQL安装指南

## 概述
如果无法使用Docker，可以直接在本地安装PostgreSQL。本指南提供了详细的安装和配置步骤。

## Windows安装

### 方法1: 官方安装程序 (推荐)
1. **下载PostgreSQL**
   - 访问: https://www.postgresql.org/download/windows/
   - 下载PostgreSQL 15.x版本
   - 选择适合您系统的版本 (x64)

2. **安装PostgreSQL**
   ```
   - 运行安装程序
   - 选择安装目录 (默认: C:\Program Files\PostgreSQL\15)
   - 设置数据目录 (默认: C:\Program Files\PostgreSQL\15\data)
   - 设置超级用户密码 (记住这个密码!)
   - 设置端口 (默认: 5432)
   - 选择区域设置 (默认: [Default locale])
   - 完成安装
   ```

3. **验证安装**
   ```cmd
   # 打开命令提示符
   cd "C:\Program Files\PostgreSQL\15\bin"
   psql --version
   ```

### 方法2: Chocolatey包管理器
```powershell
# 安装Chocolatey (如果未安装)
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装PostgreSQL
choco install postgresql15 --params '/Password:your_password_here'
```

## 配置PostgreSQL

### 1. 创建Mizzy数据库和用户
```sql
-- 连接到PostgreSQL (使用pgAdmin或命令行)
psql -U postgres

-- 创建数据库
CREATE DATABASE mizzy_main;

-- 创建用户
CREATE USER mizzy_user WITH PASSWORD 'your_secure_password';

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE mizzy_main TO mizzy_user;
ALTER USER mizzy_user CREATEDB;

-- 退出
\q
```

### 2. 应用优化配置
找到PostgreSQL配置文件 `postgresql.conf`：
- Windows: `C:\Program Files\PostgreSQL\15\data\postgresql.conf`

添加或修改以下配置：
```conf
# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# 性能优化
random_page_cost = 1.1
effective_io_concurrency = 200
checkpoint_completion_target = 0.9

# 日志配置
log_min_duration_statement = 100
log_checkpoints = on
log_connections = on

# 扩展
shared_preload_libraries = 'pg_stat_statements'
```

### 3. 安装扩展
```sql
-- 连接到mizzy_main数据库
psql -U mizzy_user -d mizzy_main

-- 安装扩展
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- 验证扩展
SELECT * FROM pg_extension;
```

### 4. 重启PostgreSQL服务
```cmd
# Windows服务管理
net stop postgresql-x64-15
net start postgresql-x64-15

# 或使用服务管理器 (services.msc)
```

## 连接测试

### 使用psql命令行
```bash
psql -h localhost -p 5432 -U mizzy_user -d mizzy_main
```

### 使用pgAdmin
1. 打开pgAdmin (通常在开始菜单中)
2. 创建新服务器连接：
   - 名称: Mizzy Local
   - 主机: localhost
   - 端口: 5432
   - 数据库: mizzy_main
   - 用户名: mizzy_user
   - 密码: [您设置的密码]

## 环境变量配置

创建 `.env` 文件：
```env
# 本地PostgreSQL配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=mizzy_main
POSTGRES_USER=mizzy_user
POSTGRES_PASSWORD=your_secure_password

# 数据库连接URL
DATABASE_URL=postgresql://mizzy_user:your_secure_password@localhost:5432/mizzy_main
```

## 验证脚本

创建验证脚本 `test-local-connection.py`：
```python
import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

try:
    conn = psycopg2.connect(
        host=os.getenv('POSTGRES_HOST'),
        port=os.getenv('POSTGRES_PORT'),
        database=os.getenv('POSTGRES_DB'),
        user=os.getenv('POSTGRES_USER'),
        password=os.getenv('POSTGRES_PASSWORD')
    )
    
    cursor = conn.cursor()
    cursor.execute("SELECT version();")
    version = cursor.fetchone()
    print(f"✅ 连接成功! PostgreSQL版本: {version[0]}")
    
    cursor.execute("SELECT name FROM pg_extension;")
    extensions = cursor.fetchall()
    print(f"✅ 已安装扩展: {[ext[0] for ext in extensions]}")
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"❌ 连接失败: {e}")
```

## 常见问题

### 1. 连接被拒绝
- 检查PostgreSQL服务是否运行
- 检查端口5432是否被占用
- 检查防火墙设置

### 2. 认证失败
- 确认用户名和密码正确
- 检查pg_hba.conf文件的认证配置

### 3. 权限问题
- 确保用户有足够的数据库权限
- 检查数据目录的文件权限

## 下一步
本地PostgreSQL安装完成后，您可以：
1. 运行验证脚本测试连接
2. 继续进行Schema设计
3. 开始应用层适配工作
