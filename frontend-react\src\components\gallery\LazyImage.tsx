// LazyImage - Optimized image loading component
// 优化的图片懒加载组件

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Image as ImageIcon } from 'lucide-react';

// ============================================================================
// 接口定义
// ============================================================================

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: React.ReactNode;
  onLoad?: () => void;
  onError?: () => void;
}

// ============================================================================
// 图片加载状态
// ============================================================================

type LoadingState = 'idle' | 'loading' | 'loaded' | 'error';

// ============================================================================
// LazyImage 组件
// ============================================================================

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = '',
  style,
  placeholder,
  onLoad,
  onError,
}) => {
  // ========================================
  // 状态管理
  // ========================================
  const [loadingState, setLoadingState] = useState<LoadingState>('idle');
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // ========================================
  // Intersection Observer 设置
  // ========================================
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.unobserve(container);
        }
      },
      {
        rootMargin: '50px', // 提前50px开始加载
        threshold: 0.1,
      }
    );

    observer.observe(container);

    return () => {
      observer.unobserve(container);
    };
  }, []);

  // ========================================
  // 图片加载处理
  // ========================================
  useEffect(() => {
    if (!isInView || loadingState !== 'idle') return;

    setLoadingState('loading');

    const img = new Image();
    
    img.onload = () => {
      setLoadingState('loaded');
      onLoad?.();
    };
    
    img.onerror = () => {
      setLoadingState('error');
      onError?.();
    };
    
    img.src = src;
  }, [isInView, src, loadingState, onLoad, onError]);

  // ========================================
  // 渲染函数
  // ========================================
  const renderPlaceholder = () => {
    if (placeholder) {
      return placeholder;
    }

    return (
      <div 
        className="w-full h-full flex items-center justify-center"
        style={{ background: 'var(--mizzy-input)' }}
      >
        {loadingState === 'loading' ? (
          <div className="flex flex-col items-center gap-2">
            <div className="w-6 h-6 border-2 border-[var(--mizzy-highlight)] border-t-transparent rounded-full animate-spin" />
            <span className="text-xs" style={{ color: 'var(--mizzy-icon)' }}>
              加载中...
            </span>
          </div>
        ) : loadingState === 'error' ? (
          <div className="flex flex-col items-center gap-2">
            <ImageIcon className="w-8 h-8" style={{ color: 'var(--mizzy-icon)' }} />
            <span className="text-xs" style={{ color: 'var(--mizzy-icon)' }}>
              加载失败
            </span>
          </div>
        ) : (
          <div className="flex flex-col items-center gap-2">
            <ImageIcon className="w-8 h-8" style={{ color: 'var(--mizzy-icon)' }} />
            <span className="text-xs" style={{ color: 'var(--mizzy-icon)' }}>
              准备加载
            </span>
          </div>
        )}
      </div>
    );
  };

  // ========================================
  // 主渲染
  // ========================================
  return (
    <div ref={containerRef} className={className} style={style}>
      {loadingState === 'loaded' ? (
        <img
          ref={imgRef}
          src={src}
          alt={alt}
          className="w-full h-full object-cover"
          style={{ opacity: 1, transition: 'opacity 0.3s ease-in-out' }}
        />
      ) : (
        renderPlaceholder()
      )}
    </div>
  );
};

// ============================================================================
// 高级懒加载Hook
// ============================================================================

export const useImagePreloader = (images: string[], batchSize: number = 5) => {
  const [preloadedImages, setPreloadedImages] = useState<Set<string>>(new Set());
  const [isPreloading, setIsPreloading] = useState(false);

  const preloadBatch = useCallback(async (imagesToPreload: string[]) => {
    const promises = imagesToPreload.map((src) => {
      return new Promise<string>((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(src);
        img.onerror = () => reject(src);
        img.src = src;
      });
    });

    try {
      const loaded = await Promise.allSettled(promises);
      const successful = loaded
        .filter((result) => result.status === 'fulfilled')
        .map((result) => (result as PromiseFulfilledResult<string>).value);

      setPreloadedImages((prev) => {
        const newSet = new Set(prev);
        successful.forEach((src) => newSet.add(src));
        return newSet;
      });
    } catch (error) {
      console.warn('Batch preload failed:', error);
    }
  }, []);

  const preloadImages = useCallback(async () => {
    if (isPreloading || images.length === 0) return;

    setIsPreloading(true);

    // 分批预加载图片
    for (let i = 0; i < images.length; i += batchSize) {
      const batch = images.slice(i, i + batchSize);
      const unloadedBatch = batch.filter((src) => !preloadedImages.has(src));
      
      if (unloadedBatch.length > 0) {
        await preloadBatch(unloadedBatch);
        // 批次间添加小延迟，避免阻塞UI
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    setIsPreloading(false);
  }, [images, batchSize, preloadedImages, preloadBatch, isPreloading]);

  return {
    preloadedImages,
    isPreloading,
    preloadImages,
  };
};

// ============================================================================
// 导出
// ============================================================================

export default LazyImage;
