// 共享工具函数库 - 智慧之眼系统
// 版本: 1.0
// 用途: 避免代码重复，提供通用功能

console.log('🔧 utils.js v1.0 - 共享工具库加载开始');

/**
 * 返回案例页面的通用函数
 * @param {number|string} currentPageCaseId - 当前页面的案例ID
 */
function goBack(currentPageCaseId) {
    try {
        console.log('🧭 goBack 被调用，案例ID:', currentPageCaseId);
        
        if (currentPageCaseId) {
            const targetUrl = `case-view.html?id=${currentPageCaseId}`;
            console.log('🔗 跳转到:', targetUrl);
            window.location.href = targetUrl;
        } else {
            console.log('🏠 没有案例ID，返回主页');
            window.location.href = 'index.html';
        }
    } catch (error) {
        console.error('❌ 返回时出错:', error);
        // 备用方案
        window.location.href = 'index.html';
    }
}

/**
 * 格式化日期显示
 * @param {string|Date} dateString - 日期字符串或日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(dateString) {
    if (!dateString) return '未知';
    
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '无效日期';
        
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        console.error('日期格式化错误:', error);
        return '格式错误';
    }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '未知';
    
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    if (i === 0) return bytes + ' ' + sizes[i];
    return (bytes / Math.pow(1024, i)).toFixed(1) + ' ' + sizes[i];
}

/**
 * 计算图片宽高比
 * @param {number} width - 宽度
 * @param {number} height - 高度
 * @returns {string} 宽高比字符串
 */
function calculateAspectRatio(width, height) {
    if (!width || !height) return '未知';
    
    const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);
    const divisor = gcd(width, height);
    
    return `${width / divisor}:${height / divisor}`;
}

/**
 * 格式化像素数量
 * @param {number} width - 宽度
 * @param {number} height - 高度
 * @returns {string} 格式化后的像素数量
 */
function formatPixelCount(width, height) {
    if (!width || !height) return '未知';
    
    const pixels = width * height;
    if (pixels >= 1000000) {
        return `${(pixels / 1000000).toFixed(1)}MP`;
    }
    return `${pixels.toLocaleString()} 像素`;
}

/**
 * 截断长路径
 * @param {string} path - 文件路径
 * @param {number} maxLength - 最大长度
 * @returns {string} 截断后的路径
 */
function truncatePath(path, maxLength = 50) {
    if (!path || path.length <= maxLength) return path || '';
    return '...' + path.slice(-(maxLength - 3));
}

/**
 * 安全的JSON解析
 * @param {string} jsonString - JSON字符串
 * @param {*} defaultValue - 默认值
 * @returns {*} 解析结果或默认值
 */
function safeJsonParse(jsonString, defaultValue = null) {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.warn('JSON解析失败:', error);
        return defaultValue;
    }
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 创建工具对象
const utils = {
    goBack,
    formatDate,
    formatFileSize,
    calculateAspectRatio,
    formatPixelCount,
    truncatePath,
    safeJsonParse,
    debounce,
    throttle
};

// 暴露到全局作用域
window.utils = utils;

// 为了向后兼容，也将 goBack 直接暴露到全局
window.goBack = goBack;

console.log('✅ utils.js v1.0 - 共享工具库加载完成');
console.log('🌐 可用工具函数:', Object.keys(utils));
console.log('🔧 window.goBack 类型:', typeof window.goBack);
console.log('🔧 window.utils.goBack 类型:', typeof window.utils.goBack);
