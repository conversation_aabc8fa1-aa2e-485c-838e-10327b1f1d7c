// VirtualizedGallery - Performance optimized gallery with virtual scrolling
// 虚拟滚动优化的画廊组件

import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { UIImageItem } from '@/adapters/uiDataAdapters';

// ============================================================================
// 接口定义
// ============================================================================

interface VirtualizedGalleryProps {
  images: UIImageItem[];
  selectedImages: string[];
  onImageClick: (imageId: string) => void;
  imageSize: number;
  showImageInfo: boolean;
  containerWidth: number;
  containerHeight: number;
}

interface GridItemProps {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    images: UIImageItem[];
    selectedImages: string[];
    onImageClick: (imageId: string) => void;
    imageSize: number;
    showImageInfo: boolean;
    columnCount: number;
  };
}

// ============================================================================
// 网格项组件
// ============================================================================

const GridItem: React.FC<GridItemProps> = ({ columnIndex, rowIndex, style, data }) => {
  const { images, selectedImages, onImageClick, imageSize, showImageInfo, columnCount } = data;
  const index = rowIndex * columnCount + columnIndex;
  const image = images[index];

  if (!image) {
    return <div style={style} />;
  }

  const isSelected = selectedImages.includes(image.id);

  return (
    <div style={style} className="p-1">
      <div
        className={`relative group cursor-pointer rounded-lg overflow-hidden transition-all ${
          isSelected 
            ? 'ring-2 ring-[var(--mizzy-highlight)]' 
            : 'hover:ring-1 hover:ring-[var(--mizzy-border-ui)]'
        }`}
        onClick={() => onImageClick(image.id)}
        style={{ height: imageSize - 8 }} // 减去padding
      >
        {/* 图片 */}
        <img
          src={image.src}
          alt={image.filename}
          className="w-full h-full object-cover"
          loading="lazy"
        />
        
        {/* 选中状态指示器 */}
        {isSelected && (
          <div className="absolute top-2 right-2 w-6 h-6 bg-[var(--mizzy-highlight)] rounded-full flex items-center justify-center">
            <span className="text-white text-xs">✓</span>
          </div>
        )}
        
        {/* 悬停信息 */}
        {showImageInfo && (
          <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="text-xs truncate">{image.filename}</div>
            <div className="text-xs text-gray-300">{image.type} • {image.size}</div>
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// 虚拟化画廊组件
// ============================================================================

export const VirtualizedGallery: React.FC<VirtualizedGalleryProps> = ({
  images,
  selectedImages,
  onImageClick,
  imageSize,
  showImageInfo,
  containerWidth,
  containerHeight,
}) => {
  // ========================================
  // 计算网格参数
  // ========================================
  const columnCount = useMemo(() => {
    return Math.max(1, Math.floor(containerWidth / imageSize));
  }, [containerWidth, imageSize]);

  const rowCount = useMemo(() => {
    return Math.ceil(images.length / columnCount);
  }, [images.length, columnCount]);

  // ========================================
  // 网格数据
  // ========================================
  const gridData = useMemo(() => ({
    images,
    selectedImages,
    onImageClick,
    imageSize,
    showImageInfo,
    columnCount,
  }), [images, selectedImages, onImageClick, imageSize, showImageInfo, columnCount]);

  // ========================================
  // 渲染
  // ========================================
  return (
    <Grid
      columnCount={columnCount}
      columnWidth={imageSize}
      height={containerHeight}
      rowCount={rowCount}
      rowHeight={imageSize}
      width={containerWidth}
      itemData={gridData}
    >
      {GridItem}
    </Grid>
  );
};

// ============================================================================
// 导出
// ============================================================================

export default VirtualizedGallery;
