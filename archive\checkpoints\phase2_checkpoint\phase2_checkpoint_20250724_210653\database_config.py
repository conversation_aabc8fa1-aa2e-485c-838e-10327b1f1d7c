# src/database_config.py
"""
数据库配置系统
支持PostgreSQL的配置管理
"""

import os
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional, Union

# 加载.env文件
try:
    from dotenv import load_dotenv
    # 从项目根目录加载.env文件
    project_root = Path(__file__).resolve().parents[2]
    env_path = project_root / ".env"
    if env_path.exists():
        load_dotenv(env_path)
        print(f"[OK] 已加载环境变量文件: {env_path}")
    else:
        print(f"[WARN] 环境变量文件不存在: {env_path}")
except ImportError:
    print("[WARN] python-dotenv未安装，将使用系统环境变量")
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
except ImportError:
    try:
        from pydantic import BaseSettings, Field
    except ImportError:
        # 如果pydantic不可用，使用简单的配置类
        BaseSettings = object
        Field = lambda **kwargs: None
import logging

logger = logging.getLogger(__name__)

class DatabaseType(str, Enum):
    """数据库类型枚举"""
    POSTGRESQL = "postgresql"

class DatabaseConfig:
    """数据库配置类"""

    def __init__(self):
        # 加载.env文件
        from dotenv import load_dotenv
        env_path = Path(__file__).resolve().parents[1] / ".env"
        if env_path.exists():
            load_dotenv(env_path)
            logger.info(f"加载环境变量文件: {env_path}")

        # 项目路径配置
        self.project_root = Path(__file__).resolve().parents[2]
        self.data_dir = self.project_root / "data"

        # 🚀 单一PostgreSQL数据库架构
        self.master_db_type = DatabaseType.POSTGRESQL
        self.case_db_type = DatabaseType.POSTGRESQL

        # PostgreSQL配置
        self.postgres_host = os.getenv("POSTGRES_HOST", "localhost")
        self.postgres_port = int(os.getenv("POSTGRES_PORT", "5432"))
        self.postgres_user = os.getenv("POSTGRES_USER", "postgres")
        self.postgres_password = os.getenv("POSTGRES_PASSWORD", "postgres")
        self.postgres_master_db = os.getenv("POSTGRES_DB", "mizzy_star_db")

        # 🔧 优化的连接池配置
        self.pool_size = int(os.getenv("DB_POOL_SIZE", "50"))  # 增加连接池大小
        self.max_overflow = int(os.getenv("DB_MAX_OVERFLOW", "20"))  # 增加溢出连接
        self.pool_timeout = int(os.getenv("DB_POOL_TIMEOUT", "10"))  # 减少超时时间
        self.pool_recycle = int(os.getenv("DB_POOL_RECYCLE", "1800"))  # 减少回收时间
        self.pool_pre_ping = True  # 启用连接预检查

        # 🚀 新增性能优化配置
        self.pool_reset_on_return = 'commit'  # 连接返回时重置
        self.connect_args = {
            'connect_timeout': 10,
            'application_name': 'mizzy_star',
            'options': '-c default_transaction_isolation=read_committed'
        }

        # 性能配置
        self.echo_sql = os.getenv("DB_ECHO_SQL", "false").lower() == "true"
        self.enable_query_cache = os.getenv("DB_ENABLE_QUERY_CACHE", "true").lower() == "true"

        # 确保目录存在
        self.data_dir.mkdir(exist_ok=True)
        (self.data_dir / "trash").mkdir(exist_ok=True)

    def get_master_database_url(self) -> str:
        """获取主数据库连接URL"""
        return (f"postgresql://{self.postgres_user}:{self.postgres_password}"
               f"@{self.postgres_host}:{self.postgres_port}/{self.postgres_master_db}")

    def get_async_master_database_url(self) -> str:
        """获取异步主数据库连接URL"""
        return (f"postgresql+asyncpg://{self.postgres_user}:{self.postgres_password}"
               f"@{self.postgres_host}:{self.postgres_port}/{self.postgres_master_db}")

    def get_case_database_url(self, case_id: Union[int, str]) -> str:
        """获取案例数据库连接URL - 在PostgreSQL模式下，返回主数据库URL"""
        return self.get_master_database_url()

    def get_async_case_database_url(self, case_id: Union[int, str]) -> str:
        """获取异步案例数据库连接URL - 在PostgreSQL模式下，返回主异步数据库URL"""
        return self.get_async_master_database_url()

    def get_case_directory_path(self, case_id: Union[int, str]) -> Path:
        """获取案例目录路径（用于文件存储）"""
        case_dir = self.data_dir / f"case_{case_id}"
        case_dir.mkdir(exist_ok=True)

        # 创建子目录
        uploads_dir = case_dir / "uploads"
        thumbnails_dir = uploads_dir / "thumbnails"
        thumbnails_dir.mkdir(parents=True, exist_ok=True)

        return case_dir

    def get_postgresql_connect_args(self) -> Dict[str, Any]:
        """获取PostgreSQL连接参数"""
        return {
            "connect_timeout": 30
        }

    def get_engine_kwargs(self, db_type: DatabaseType) -> Dict[str, Any]:
        """获取数据库引擎配置参数"""
        base_kwargs = {
            "echo": self.echo_sql,
            "pool_pre_ping": True,
            "pool_recycle": self.pool_recycle,
            "connect_args": self.get_postgresql_connect_args(),
            "pool_size": self.pool_size,
            "max_overflow": self.max_overflow,
            "pool_timeout": self.pool_timeout,
        }

        return base_kwargs

    def validate_postgresql_connection(self) -> bool:
        """验证PostgreSQL连接配置"""
        try:
            import psycopg2
            conn = psycopg2.connect(
                host=self.postgres_host,
                port=self.postgres_port,
                user=self.postgres_user,
                password=self.postgres_password,
                database=self.postgres_master_db,
                connect_timeout=10
            )
            conn.close()
            logger.info("PostgreSQL连接验证成功")
            return True
        except Exception as e:
            logger.error(f"PostgreSQL连接验证失败: {e}")
            return False

# 全局配置实例
db_config = DatabaseConfig()

# 兼容性函数 - 保持与现有代码的兼容性
def get_master_database_url() -> str:
    """获取主数据库URL（兼容性函数）"""
    return db_config.get_master_database_url()

# PostgreSQL模式下不再需要案例数据库路径函数

def create_case_directory(case_id: Union[int, str]) -> Path:
    """创建案例目录（兼容性函数）"""
    case_dir = db_config.data_dir / f"case_{case_id}"
    case_dir.mkdir(exist_ok=True)

    uploads_dir = case_dir / "uploads"
    thumbnails_dir = uploads_dir / "thumbnails"
    thumbnails_dir.mkdir(parents=True, exist_ok=True)

    return case_dir

# 导出配置常量
PROJECT_ROOT = db_config.project_root
DATA_DIR = db_config.data_dir
TRASH_DIR = db_config.data_dir / "trash"
