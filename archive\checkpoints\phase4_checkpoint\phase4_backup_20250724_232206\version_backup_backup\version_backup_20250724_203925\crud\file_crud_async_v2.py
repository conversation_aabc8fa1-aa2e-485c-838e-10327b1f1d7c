# src/crud/file_crud_async_v2.py
"""
异步文件CRUD操作 - PostgreSQL单一数据库架构
重构版本，支持批量操作和高性能处理
"""
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_, insert
from sqlalchemy.orm import selectinload

from .. import models, schemas
from ..database_config import db_config


async def create_file_async(
    db: AsyncSession, 
    case_id: int, 
    file: schemas.FileCreate
) -> Optional[models.File]:
    """
    异步创建单个文件记录
    
    Args:
        db: 异步数据库会话
        case_id: 案例ID
        file: 文件创建数据
        
    Returns:
        创建的文件对象或None
    """
    try:
        # 验证案例存在
        case_stmt = select(models.Case).where(
            and_(
                models.Case.id == case_id,
                models.Case.status == models.CaseStatus.ACTIVE
            )
        )
        case_result = await db.execute(case_stmt)
        if not case_result.scalar_one_or_none():
            raise ValueError(f"案例 {case_id} 不存在或不活跃")
        
        # 创建文件记录
        file_data = file.model_dump()
        file_data['case_id'] = case_id
        
        db_file = models.File(**file_data)
        db.add(db_file)
        await db.commit()
        await db.refresh(db_file)
        
        # 异步处理文件（生成缩略图、分析质量等）
        asyncio.create_task(process_file_async(db_file.id))
        
        return db_file
        
    except Exception as e:
        await db.rollback()
        raise Exception(f"创建文件失败: {e}")


async def batch_create_files_async(
    db: AsyncSession, 
    case_id: int, 
    files: List[schemas.FileCreate]
) -> List[models.File]:
    """
    异步批量创建文件记录 - 优化性能
    
    Args:
        db: 异步数据库会话
        case_id: 案例ID
        files: 文件创建数据列表
        
    Returns:
        创建的文件对象列表
    """
    try:
        # 验证案例存在
        case_stmt = select(models.Case).where(
            and_(
                models.Case.id == case_id,
                models.Case.status == models.CaseStatus.ACTIVE
            )
        )
        case_result = await db.execute(case_stmt)
        if not case_result.scalar_one_or_none():
            raise ValueError(f"案例 {case_id} 不存在或不活跃")
        
        # 准备批量插入数据
        files_data = []
        for file in files:
            file_data = file.model_dump()
            file_data['case_id'] = case_id
            file_data['created_at'] = datetime.utcnow()
            files_data.append(file_data)
        
        # 批量插入
        stmt = insert(models.File).returning(models.File)
        result = await db.execute(stmt, files_data)
        created_files = result.scalars().all()
        
        await db.commit()
        
        # 异步处理所有文件
        for file in created_files:
            asyncio.create_task(process_file_async(file.id))
        
        return list(created_files)
        
    except Exception as e:
        await db.rollback()
        raise Exception(f"批量创建文件失败: {e}")


async def get_files_async(
    db: AsyncSession, 
    case_id: int,
    skip: int = 0,
    limit: int = 100,
    file_type: Optional[str] = None,
    tags: Optional[List[str]] = None,
    quality_min: Optional[float] = None
) -> List[models.File]:
    """
    异步获取案例文件列表 - 支持过滤和搜索
    
    Args:
        db: 异步数据库会话
        case_id: 案例ID
        skip: 跳过记录数
        limit: 限制记录数
        file_type: 文件类型过滤
        tags: 标签过滤
        quality_min: 最低质量分数
        
    Returns:
        文件列表
    """
    try:
        # 构建查询
        stmt = select(models.File).where(models.File.case_id == case_id)
        
        # 添加文件类型过滤
        if file_type:
            stmt = stmt.where(models.File.file_type.ilike(f"{file_type}%"))
        
        # 添加质量过滤
        if quality_min is not None:
            stmt = stmt.where(models.File.quality_score >= quality_min)
        
        # 添加标签过滤 (JSONB查询)
        if tags:
            for tag in tags:
                stmt = stmt.where(models.File.tags.has_key(tag))
        
        # 排序和分页
        stmt = stmt.order_by(models.File.created_at.desc()).offset(skip).limit(limit)
        
        # 执行查询
        result = await db.execute(stmt)
        return list(result.scalars().all())
        
    except Exception as e:
        raise Exception(f"获取文件列表失败: {e}")


async def get_file_async(
    db: AsyncSession, 
    file_id: int, 
    case_id: int
) -> Optional[models.File]:
    """
    异步获取单个文件
    
    Args:
        db: 异步数据库会话
        file_id: 文件ID
        case_id: 案例ID
        
    Returns:
        文件对象或None
    """
    try:
        stmt = select(models.File).where(
            and_(
                models.File.id == file_id,
                models.File.case_id == case_id
            )
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
        
    except Exception as e:
        raise Exception(f"获取文件失败: {e}")


async def update_file_async(
    db: AsyncSession, 
    file_id: int, 
    case_id: int, 
    file_update: Dict[str, Any]
) -> Optional[models.File]:
    """
    异步更新文件信息
    
    Args:
        db: 异步数据库会话
        file_id: 文件ID
        case_id: 案例ID
        file_update: 更新数据
        
    Returns:
        更新后的文件对象或None
    """
    try:
        # 获取文件
        file = await get_file_async(db, file_id, case_id)
        if not file:
            return None
        
        # 更新字段
        for field, value in file_update.items():
            if hasattr(file, field):
                setattr(file, field, value)
        
        await db.commit()
        await db.refresh(file)
        
        return file
        
    except Exception as e:
        await db.rollback()
        raise Exception(f"更新文件失败: {e}")


async def delete_file_async(
    db: AsyncSession, 
    file_id: int, 
    case_id: int
) -> bool:
    """
    异步删除文件
    
    Args:
        db: 异步数据库会话
        file_id: 文件ID
        case_id: 案例ID
        
    Returns:
        删除是否成功
    """
    try:
        # 获取文件
        file = await get_file_async(db, file_id, case_id)
        if not file:
            return False
        
        # 删除文件记录
        await db.delete(file)
        await db.commit()
        
        # 异步删除物理文件
        asyncio.create_task(delete_physical_file_async(file.file_path))
        
        return True
        
    except Exception as e:
        await db.rollback()
        raise Exception(f"删除文件失败: {e}")


async def batch_delete_files_async(
    db: AsyncSession, 
    case_id: int, 
    file_ids: List[int]
) -> Dict[str, Any]:
    """
    异步批量删除文件
    
    Args:
        db: 异步数据库会话
        case_id: 案例ID
        file_ids: 文件ID列表
        
    Returns:
        删除结果统计
    """
    result = {
        'deleted': 0,
        'failed': 0,
        'errors': []
    }
    
    try:
        # 获取要删除的文件
        stmt = select(models.File).where(
            and_(
                models.File.case_id == case_id,
                models.File.id.in_(file_ids)
            )
        )
        files_result = await db.execute(stmt)
        files_to_delete = list(files_result.scalars().all())
        
        # 收集文件路径用于后续物理删除
        file_paths = [f.file_path for f in files_to_delete if f.file_path]
        
        # 批量删除数据库记录
        delete_stmt = delete(models.File).where(
            and_(
                models.File.case_id == case_id,
                models.File.id.in_(file_ids)
            )
        )
        delete_result = await db.execute(delete_stmt)
        await db.commit()
        
        result['deleted'] = delete_result.rowcount
        
        # 异步删除物理文件
        for file_path in file_paths:
            asyncio.create_task(delete_physical_file_async(file_path))
        
        return result
        
    except Exception as e:
        await db.rollback()
        result['failed'] = len(file_ids)
        result['errors'].append(str(e))
        return result


# 标签相关异步操作
async def add_tags_to_file_async(
    db: AsyncSession, 
    file_id: int, 
    case_id: int, 
    tags: List[str]
) -> Optional[models.File]:
    """
    异步为文件添加标签
    
    Args:
        db: 异步数据库会话
        file_id: 文件ID
        case_id: 案例ID
        tags: 标签列表
        
    Returns:
        更新后的文件对象或None
    """
    try:
        file = await get_file_async(db, file_id, case_id)
        if not file:
            return None
        
        # 合并标签
        current_tags = file.tags or {}
        for tag in tags:
            current_tags[tag] = {
                "added_at": datetime.utcnow().isoformat(),
                "confidence": 1.0
            }
        
        file.tags = current_tags
        await db.commit()
        await db.refresh(file)
        
        return file
        
    except Exception as e:
        await db.rollback()
        raise Exception(f"添加标签失败: {e}")


async def search_files_by_tags_async(
    db: AsyncSession, 
    case_id: int, 
    tags: List[str],
    match_all: bool = True
) -> List[models.File]:
    """
    异步基于标签搜索文件
    
    Args:
        db: 异步数据库会话
        case_id: 案例ID
        tags: 搜索标签
        match_all: 是否匹配所有标签
        
    Returns:
        匹配的文件列表
    """
    try:
        stmt = select(models.File).where(models.File.case_id == case_id)
        
        if match_all:
            # 匹配所有标签
            for tag in tags:
                stmt = stmt.where(models.File.tags.has_key(tag))
        else:
            # 匹配任一标签
            tag_conditions = [models.File.tags.has_key(tag) for tag in tags]
            stmt = stmt.where(or_(*tag_conditions))
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
        
    except Exception as e:
        raise Exception(f"标签搜索失败: {e}")


# 辅助函数
async def process_file_async(file_id: int):
    """异步处理文件（生成缩略图、分析质量等）"""
    try:
        # 这里可以添加文件处理逻辑
        # 例如：生成缩略图、分析图像质量、提取EXIF等
        await asyncio.sleep(0.1)  # 模拟处理时间
        print(f"文件 {file_id} 处理完成")
        
    except Exception as e:
        print(f"处理文件 {file_id} 失败: {e}")


async def delete_physical_file_async(file_path: str):
    """异步删除物理文件"""
    try:
        import os
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
            print(f"物理文件已删除: {file_path}")
            
    except Exception as e:
        print(f"删除物理文件失败: {e}")
