// 统一数据流架构 - 解决异步地狱和状态竞争问题
// 三步走方案：统一数据加载层 + 加固状态管理层 + 弹性化渲染层

console.log('🏗️ 统一数据流架构初始化...');

// ==================== 第一步：统一数据加载层 ====================

class UnifiedDataLoader {
    constructor(caseId) {
        this.caseId = caseId;
        this.loadingPromise = null;
        this.loadingState = {
            isLoading: false,
            loadCount: 0,
            lastLoadTime: null
        };
        
        // 获取API基础URL
        this.apiBaseUrl = 'http://localhost:8000';
        if (window.api && window.api.baseURL) {
            this.apiBaseUrl = window.api.baseURL;
        }
        console.log('📡 API基础URL:', this.apiBaseUrl);
    }

    // 统一数据加载入口 - 使用Promise.all协调所有API请求
    async loadAllData() {
        // 防止重复加载
        if (this.loadingState.isLoading) {
            console.log('⏳ 数据正在加载中，等待完成...');
            return await this.loadingPromise;
        }

        this.loadingState.isLoading = true;
        this.loadingState.loadCount++;
        this.loadingState.lastLoadTime = Date.now();

        console.log(`🚀 开始统一数据加载 (第${this.loadingState.loadCount}次)`);

        this.loadingPromise = this._executeDataLoading();
        
        try {
            const result = await this.loadingPromise;
            console.log('✅ 统一数据加载完成');
            return result;
        } finally {
            this.loadingState.isLoading = false;
            this.loadingPromise = null;
        }
    }

    // 执行实际的数据加载
    async _executeDataLoading() {
        try {
            console.log('📥 并行加载所有数据源...');

            // 使用Promise.all并行执行所有API请求
            const [basicInfo, tagTree, customTags, filesData] = await Promise.all([
                this._loadBasicInfo(),
                this._loadTagTree(),
                this._loadCustomTags(),
                this._loadFiles()
            ]);

            console.log('🔄 聚合数据...');

            // 数据聚合函数 - 将所有数据合并成统一的initialState
            const aggregatedData = this._aggregateData({
                basicInfo,
                tagTree,
                customTags,
                filesData
            });

            console.log('📊 数据聚合完成:', {
                basicInfo: !!aggregatedData.basicInfo,
                tagTree: !!aggregatedData.tagTree,
                files: aggregatedData.files.length,
                customTags: aggregatedData.customTags.length
            });

            return aggregatedData;

        } catch (error) {
            console.error('❌ 统一数据加载失败:', error);
            throw error;
        }
    }

    // 加载案例基本信息
    async _loadBasicInfo() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/v1/cases/${this.caseId}/basic`);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('❌ 加载基本信息失败:', error);
            throw error;
        }
    }

    // 加载标签树
    async _loadTagTree() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/v1/cases/${this.caseId}/tags/tree?include_empty=true`);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('❌ 加载标签树失败:', error);
            throw error;
        }
    }

    // 加载自定义标签
    async _loadCustomTags() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/v1/cases/${this.caseId}/custom-tags`);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            return await response.json();
        } catch (error) {
            console.warn('⚠️ 加载自定义标签失败，使用空数组:', error);
            return [];
        }
    }

    // 加载文件列表
    async _loadFiles() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/v1/cases/${this.caseId}/files?limit=100`);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const data = await response.json();
            return data.files || [];
        } catch (error) {
            console.error('❌ 加载文件列表失败:', error);
            throw error;
        }
    }

    // 数据聚合函数
    _aggregateData({ basicInfo, tagTree, customTags, filesData }) {
        // 检查标签树是否为空，如果为空则从文件构建
        const isTagTreeEmpty = !tagTree.tags || 
                              (Object.keys(tagTree.tags.metadata || {}).length === 0 &&
                               Object.keys(tagTree.tags.cv || {}).length === 0 &&
                               Object.keys(tagTree.tags.user || {}).length === 0 &&
                               Object.keys(tagTree.tags.ai || {}).length === 0);

        let finalTagTree = tagTree;
        if (isTagTreeEmpty && filesData.length > 0) {
            console.log('🏗️ 标签树为空，从文件构建...');
            finalTagTree = this._buildTagTreeFromFiles(filesData);
        }

        // 将自定义标签合并到标签树
        finalTagTree.custom = customTags;

        return {
            basicInfo,
            tagTree: finalTagTree,
            customTags,
            files: filesData,
            metadata: {
                loadTime: Date.now(),
                fileCount: filesData.length,
                tagCount: this._countTags(finalTagTree)
            }
        };
    }

    // 从文件构建标签树
    _buildTagTreeFromFiles(files) {
        const tagTree = {
            tags: { metadata: {}, cv: {}, user: {}, ai: {} },
            properties: {},
            custom: []
        };

        files.forEach(file => {
            if (file.tags && file.tags.tags) {
                const fileTags = file.tags.tags;

                // 处理元数据标签
                if (fileTags.metadata) {
                    Object.entries(fileTags.metadata).forEach(([key, value]) => {
                        if (value) {
                            if (!tagTree.tags.metadata[key]) {
                                tagTree.tags.metadata[key] = {};
                            }
                            if (!tagTree.tags.metadata[key][value]) {
                                tagTree.tags.metadata[key][value] = 0;
                            }
                            tagTree.tags.metadata[key][value]++;
                        }
                    });
                }

                // 处理用户标签
                if (fileTags.user && Array.isArray(fileTags.user)) {
                    fileTags.user.forEach(tag => {
                        if (tag) {
                            if (!tagTree.tags.user[tag]) {
                                tagTree.tags.user[tag] = 0;
                            }
                            tagTree.tags.user[tag]++;
                        }
                    });
                }

                // 处理AI标签
                if (fileTags.ai && Array.isArray(fileTags.ai)) {
                    fileTags.ai.forEach(tag => {
                        if (tag) {
                            if (!tagTree.tags.ai[tag]) {
                                tagTree.tags.ai[tag] = 0;
                            }
                            tagTree.tags.ai[tag]++;
                        }
                    });
                }
            }

            // 处理属性标签
            if (file.tags && file.tags.properties) {
                Object.entries(file.tags.properties).forEach(([key, value]) => {
                    if (value) {
                        if (!tagTree.properties[key]) {
                            tagTree.properties[key] = {};
                        }
                        if (!tagTree.properties[key][value]) {
                            tagTree.properties[key][value] = 0;
                        }
                        tagTree.properties[key][value]++;
                    }
                });
            }
        });

        return tagTree;
    }

    // 计算标签总数
    _countTags(tagTree) {
        let count = 0;
        if (tagTree.tags) {
            Object.values(tagTree.tags).forEach(category => {
                count += Object.keys(category).length;
            });
        }
        if (tagTree.properties) {
            count += Object.keys(tagTree.properties).length;
        }
        if (tagTree.custom) {
            count += tagTree.custom.length;
        }
        return count;
    }
}

// ==================== 第二步：加固状态管理层 ====================

class StateManager {
    constructor() {
        this.state = {
            isLoading: false,
            isUpdating: false,
            data: null,
            error: null,
            lastUpdate: null
        };
        this.updateQueue = [];
        this.subscribers = [];
        this.updateLock = false;
    }

    // 原子状态更新 - 使用互斥锁防止竞争
    async updateState(newData) {
        // 检查是否有其他更新在进行
        if (this.updateLock) {
            console.log('⏳ 状态更新已锁定，加入队列...');
            return new Promise((resolve) => {
                this.updateQueue.push({ data: newData, resolve });
            });
        }

        this.updateLock = true;
        this.state.isUpdating = true;

        try {
            console.log('🔒 执行原子状态更新...');

            // 事务式更新 - 全有或全无
            const newState = {
                ...this.state,
                isLoading: false,
                isUpdating: false,
                data: newData,
                error: null,
                lastUpdate: Date.now()
            };

            // 验证新状态的完整性
            if (this._validateState(newState)) {
                this.state = newState;
                console.log('✅ 状态更新成功');
                
                // 通知所有订阅者
                this._notifySubscribers();
            } else {
                throw new Error('状态验证失败');
            }

        } catch (error) {
            console.error('❌ 状态更新失败:', error);
            this.state.error = error;
            throw error;
        } finally {
            this.updateLock = false;
            this.state.isUpdating = false;

            // 处理队列中的更新
            if (this.updateQueue.length > 0) {
                const { data, resolve } = this.updateQueue.shift();
                this.updateState(data).then(resolve);
            }
        }
    }

    // 状态验证
    _validateState(state) {
        if (!state.data) return false;
        if (!state.data.basicInfo) return false;
        if (!state.data.tagTree) return false;
        if (!Array.isArray(state.data.files)) return false;
        return true;
    }

    // 订阅状态变化
    subscribe(callback) {
        this.subscribers.push(callback);
        return () => {
            this.subscribers = this.subscribers.filter(cb => cb !== callback);
        };
    }

    // 通知订阅者
    _notifySubscribers() {
        this.subscribers.forEach(callback => {
            try {
                callback(this.state);
            } catch (error) {
                console.error('❌ 订阅者回调失败:', error);
            }
        });
    }

    // 获取当前状态
    getState() {
        return { ...this.state };
    }

    // 设置加载状态
    setLoading(isLoading) {
        this.state.isLoading = isLoading;
        this._notifySubscribers();
    }

    // 设置错误状态
    setError(error) {
        this.state.error = error;
        this.state.isLoading = false;
        this._notifySubscribers();
    }
}

// ==================== 第三步：弹性化渲染层 ====================

// 添加防抖函数
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

class AdaptiveRenderer {
    constructor(stateManager) {
        this.stateManager = stateManager;
        this.renderQueue = [];
        this.isRendering = false;
        this.lastRenderTime = 0;
        this.renderDebounceTime = 100; // 100ms防抖
        
        // 防抖渲染函数
        this.debouncedRender = debounce(this._executeRender.bind(this), this.renderDebounceTime);
        
        // 订阅状态变化
        this.stateManager.subscribe(this._onStateChange.bind(this));
    }

    // 状态变化处理 - 使用防抖机制
    _onStateChange(state) {
        // 使用防抖函数处理渲染
        this.debouncedRender(state);
    }

    // 执行渲染
    async _executeRender(state) {
        if (this.isRendering) {
            console.log('⏳ 渲染正在进行中，跳过...');
            return;
        }

        this.isRendering = true;
        this.lastRenderTime = Date.now();

        try {
            console.log('🎨 开始弹性渲染...');

            if (state.isLoading) {
                this._renderLoadingState();
            } else if (state.error) {
                this._renderErrorState(state.error);
            } else if (state.data) {
                await this._renderDataState(state.data);
            } else {
                this._renderEmptyState();
            }

            console.log('✅ 弹性渲染完成');

        } catch (error) {
            console.error('❌ 渲染失败:', error);
            this._renderErrorState(error);
        } finally {
            this.isRendering = false;
        }
    }

    // 渲染加载状态
    _renderLoadingState() {
        console.log('⏳ 渲染加载状态...');
        
        // 渲染标签加载状态
        const categories = ['properties', 'metadata', 'cv', 'user', 'ai'];
        categories.forEach(category => {
            const container = document.getElementById(`${category}-tags`);
            if (container) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                        <div class="text-sm text-gray-500 mt-2">加载${this._getCategoryDisplayName(category)}中...</div>
                    </div>
                `;
            }
        });

        // 渲染画廊加载状态
        const galleryContainer = document.getElementById('gallery-grid');
        if (galleryContainer) {
            galleryContainer.innerHTML = `
                <div class="col-span-full text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <div class="text-gray-500 mt-4">加载文件中...</div>
                </div>
            `;
            galleryContainer.classList.remove('hidden');
        }
    }

    // 渲染错误状态
    _renderErrorState(error) {
        console.log('❌ 渲染错误状态:', error);
        
        const galleryContainer = document.getElementById('gallery-grid');
        if (galleryContainer) {
            galleryContainer.innerHTML = `
                <div class="col-span-full text-center py-8">
                    <div class="text-red-500 text-6xl mb-4">⚠️</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
                    <p class="text-gray-500 mb-4">${error.message || '未知错误'}</p>
                    <button onclick="window.unifiedArchitecture.reload()" 
                            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        重新加载
                    </button>
                </div>
            `;
            galleryContainer.classList.remove('hidden');
        }
    }

    // 渲染数据状态 - 增强防御性渲染
    async _renderDataState(data) {
        console.log('📊 渲染数据状态...');

        // 增强的数据验证
        if (!this._validateRenderData(data)) {
            console.warn('⚠️ 数据不完整，渲染占位符');
            this._renderIncompleteState(data);
            return;
        }

        try {
            // 渲染标签树
            this._renderTagTree(data.tagTree);

            // 渲染文件画廊
            this._renderGallery(data.files);

            // 更新案例信息
            this._updateCaseInfo(data.basicInfo, data.files.length);

            // 记录渲染结果
            const tagCount = this._countTags(data.tagTree);
            console.log(`📈 渲染监控: ${tagCount} 个标签, ${data.files.length} 个文件`);

            // 如果结果为空，触发诊断和自动修复
            if (tagCount === 0 || data.files.length === 0) {
                console.warn('🔍 渲染结果不完整，记录状态快照:', {
                    timestamp: Date.now(),
                    dataSnapshot: {
                        basicInfo: !!data.basicInfo,
                        tagTree: !!data.tagTree,
                        tagCount: tagCount,
                        files: data.files.length,
                        customTags: data.customTags?.length || 0
                    }
                });
                
                // 尝试自动修复
                setTimeout(() => {
                    console.log('🔄 尝试自动修复...');
                    if (window.unifiedArchitecture && window.unifiedArchitecture.autoRepair) {
                        window.unifiedArchitecture.autoRepair();
                    }
                }, 1000);
            }
        } catch (error) {
            console.error('❌ 渲染过程中出错:', error);
            this._renderErrorState(error);
        }
    }

    // 验证渲染数据 - 增强验证
    _validateRenderData(data) {
        // 基本验证
        if (!data) return false;
        
        // 验证基本信息
        if (!data.basicInfo || typeof data.basicInfo !== 'object') {
            console.warn('⚠️ 基本信息无效');
            return false;
        }
        
        // 验证标签树
        if (!data.tagTree || typeof data.tagTree !== 'object') {
            console.warn('⚠️ 标签树无效');
            return false;
        }
        
        // 验证文件列表
        if (!Array.isArray(data.files)) {
            console.warn('⚠️ 文件列表无效');
            return false;
        }
        
        return true;
    }

    // 渲染不完整状态
    _renderIncompleteState(data) {
        console.log('⚠️ 渲染不完整状态...');
        
        const galleryContainer = document.getElementById('gallery-grid');
        if (galleryContainer) {
            galleryContainer.innerHTML = `
                <div class="col-span-full text-center py-8">
                    <div class="text-yellow-500 text-6xl mb-4">⚠️</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">数据不完整</h3>
                    <p class="text-gray-500 mb-4">部分数据加载失败，正在重试...</p>
                    <div class="text-sm text-gray-400">
                        基本信息: ${data?.basicInfo ? '✅' : '❌'} | 
                        标签树: ${data?.tagTree ? '✅' : '❌'} | 
                        文件列表: ${Array.isArray(data?.files) ? '✅' : '❌'}
                    </div>
                </div>
            `;
        }
    }

    // 渲染空状态
    _renderEmptyState() {
        console.log('📭 渲染空状态...');
        
        const galleryContainer = document.getElementById('gallery-grid');
        if (galleryContainer) {
            galleryContainer.innerHTML = `
                <div class="col-span-full text-center py-8">
                    <div class="text-gray-400 text-6xl mb-4">📭</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">暂无数据</h3>
                    <p class="text-gray-500">请检查案例是否存在或重新加载</p>
                </div>
            `;
        }
    }

    // 渲染标签树
    _renderTagTree(tagTree) {
        const categories = ['properties', 'metadata', 'cv', 'user', 'ai'];
        let totalTagsRendered = 0;

        categories.forEach(category => {
            const container = document.getElementById(`${category}-tags`);
            if (!container) return;

            let categoryData = {};
            if (category === 'properties') {
                categoryData = tagTree.properties || {};
            } else {
                categoryData = tagTree.tags[category] || {};
            }

            const tagCount = Object.keys(categoryData).length;

            // 更新标签计数
            const header = document.querySelector(`[data-category="${category}"]`);
            const countElement = header?.querySelector('.tag-count');
            if (countElement) {
                countElement.textContent = tagCount;
            }

            if (tagCount === 0) {
                container.innerHTML = `
                    <div class="text-center py-2 text-gray-500 text-sm">
                        暂无${this._getCategoryDisplayName(category)}
                    </div>
                `;
                return;
            }

            // 渲染标签项
            const tagItems = [];
            Object.entries(categoryData).forEach(([tagName, tagData]) => {
                if (typeof tagData === 'object' && !Array.isArray(tagData)) {
                    Object.entries(tagData).forEach(([tagValue, count]) => {
                        tagItems.push({
                            name: `${tagName}: ${tagValue}`,
                            count: count || 0
                        });
                    });
                } else {
                    tagItems.push({
                        name: tagName,
                        count: tagData || 0
                    });
                }
            });

            container.innerHTML = tagItems.map(tag => `
                <div class="tag-item cursor-pointer hover:bg-blue-50 p-2 rounded" 
                     data-category="${category}" 
                     data-tag-name="${tag.name}">
                    <span class="tag-name text-sm font-medium">${tag.name}</span>
                    <span class="tag-count ml-2 px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs">${tag.count}</span>
                </div>
            `).join('');

            totalTagsRendered += tagItems.length;
        });

        console.log(`✅ 标签树渲染完成: ${totalTagsRendered} 个标签项`);
    }

    // 渲染文件画廊
    _renderGallery(files) {
        const galleryContainer = document.getElementById('gallery-grid');
        const emptyState = document.getElementById('gallery-empty');

        if (!galleryContainer) {
            console.error('❌ gallery-grid 容器不存在');
            return;
        }

        if (files.length === 0) {
            console.log('📭 没有文件，显示空状态');
            galleryContainer.innerHTML = `
                <div class="col-span-full text-center py-8">
                    <div class="text-gray-400 text-6xl mb-4">📁</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">暂无文件</h3>
                    <p class="text-gray-500">请添加文件到此案例</p>
                </div>
            `;
            galleryContainer.classList.remove('hidden');
            if (emptyState) emptyState.classList.add('hidden');
            return;
        }

        galleryContainer.classList.remove('hidden');
        if (emptyState) emptyState.classList.add('hidden');

        galleryContainer.innerHTML = files.map(file => {
            const isImage = file.file_type && file.file_type.startsWith('image/');
            const thumbnailUrl = isImage && file.thumbnail_small_path ? 
                `file://${file.thumbnail_small_path}` : null;

            return `
                <div class="gallery-item bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer" 
                     data-file-id="${file.id}">
                    
                    <div class="aspect-square bg-gray-100 rounded-t-lg flex items-center justify-center">
                        ${thumbnailUrl ? `
                            <img src="${thumbnailUrl}" 
                                 alt="${file.file_name}"
                                 class="w-full h-full object-cover rounded-t-lg"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="hidden w-full h-full items-center justify-center text-gray-400">
                                <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        ` : `
                            <div class="text-gray-400">
                                <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        `}
                    </div>
                    
                    <div class="p-3">
                        <div class="text-sm font-medium text-gray-900 truncate" title="${file.file_name}">
                            ${file.file_name}
                        </div>
                        <div class="text-xs text-gray-500 mt-1">
                            ID: ${file.id}
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        console.log(`✅ 文件画廊渲染完成: ${files.length} 个文件`);
    }

    // 更新案例信息
    _updateCaseInfo(basicInfo, fileCount) {
        const caseNameElement = document.getElementById('case-name');
        const caseDescElement = document.getElementById('case-description');
        const fileCountElement = document.getElementById('file-count');

        if (caseNameElement) {
            caseNameElement.textContent = basicInfo.case_name;
        }
        if (caseDescElement) {
            caseDescElement.textContent = basicInfo.description || '无描述';
        }
        if (fileCountElement) {
            fileCountElement.textContent = fileCount;
        }
    }

    // 获取类别显示名称
    _getCategoryDisplayName(category) {
        const names = {
            'properties': '属性标签',
            'metadata': '元数据标签',
            'cv': 'CV标签',
            'user': '用户标签',
            'ai': 'AI标签'
        };
        return names[category] || category;
    }

    // 计算标签总数
    _countTags(tagTree) {
        let count = 0;
        if (tagTree.tags) {
            Object.values(tagTree.tags).forEach(category => {
                count += Object.keys(category).length;
            });
        }
        if (tagTree.properties) {
            count += Object.keys(tagTree.properties).length;
        }
        if (tagTree.custom) {
            count += tagTree.custom.length;
        }
        return count;
    }
}

// ==================== 统一架构控制器 ====================

class UnifiedArchitecture {
    constructor(caseId) {
        this.caseId = caseId;
        this.dataLoader = new UnifiedDataLoader(caseId);
        this.stateManager = new StateManager();
        this.renderer = new AdaptiveRenderer(this.stateManager);
        this.isInitialized = false;
        this.autoRepairEnabled = true;
        
        // 添加对TagManagementApp的支持
        this._setupTagAppIntegration();
    }

    // 设置TagManagementApp集成
    _setupTagAppIntegration() {
        // 添加状态变化订阅
        this.stateManager.subscribe((state) => {
            if (typeof window.onUnifiedArchitectureUpdate === 'function') {
                try {
                    window.onUnifiedArchitectureUpdate(state);
                } catch (error) {
                    console.error('❌ TagApp回调失败:', error);
                }
            }
        });
        
        // 检查现有TagApp实例
        if (window.tagApp) {
            console.log('🔗 检测到现有TagApp实例，设置双向集成');
            
            // 添加刷新方法
            const originalReload = this.reload.bind(this);
            this.reload = async () => {
                const result = await originalReload();
                
                // 同步到TagApp
                if (window.tagApp.syncWithUnifiedArchitecture) {
                    window.tagApp.syncWithUnifiedArchitecture(this.stateManager.getState());
                }
                
                return result;
            };
        }
    }

    // 初始化架构
    async initialize() {
        if (this.isInitialized) {
            console.log('⚠️ 架构已初始化，跳过...');
            return;
        }

        console.log('🚀 初始化统一数据流架构...');

        try {
            // 设置加载状态
            this.stateManager.setLoading(true);

            // 加载所有数据
            const data = await this.dataLoader.loadAllData();

            // 更新状态
            await this.stateManager.updateState(data);

            this.isInitialized = true;
            console.log('✅ 统一数据流架构初始化完成');

            // 显示成功通知
            this._showNotification('统一数据流架构已激活！界面将保持稳定显示。', 'success');
            
            // 设置定期健康检查
            this._setupHealthCheck();

        } catch (error) {
            console.error('❌ 架构初始化失败:', error);
            this.stateManager.setError(error);
            this._showNotification('架构初始化失败: ' + error.message, 'error');
            throw error;
        }
    }
    
    // 设置定期健康检查
    _setupHealthCheck() {
        console.log('🔄 设置定期健康检查...');
        
        // 每30秒执行一次健康检查
        this.healthCheckInterval = setInterval(() => {
            this._performHealthCheck();
        }, 30000);
        
        // 首次健康检查在5秒后执行
        setTimeout(() => {
            this._performHealthCheck();
        }, 5000);
    }
    
    // 执行健康检查
    async _performHealthCheck() {
        console.log('🩺 执行健康检查...');
        
        try {
            const currentState = this.stateManager.getState();
            
            if (!currentState.data) {
                console.warn('⚠️ 健康检查：无状态数据');
                await this.reload();
                return;
            }
            
            const { tagTree, files } = currentState.data;
            const tagCount = this._countTags(tagTree);
            const fileCount = Array.isArray(files) ? files.length : 0;
            
            console.log('📊 健康状态:', {
                tagCount,
                fileCount,
                lastUpdate: new Date(currentState.lastUpdate).toLocaleTimeString()
            });
            
            // 检查是否需要修复
            if (tagCount === 0 || fileCount === 0) {
                console.warn('⚠️ 健康检查：检测到不完整数据，尝试修复');
                await this.autoRepair();
            }
            
        } catch (error) {
            console.error('❌ 健康检查失败:', error);
        }
    }
    
    // 计算标签总数
    _countTags(tagTree) {
        if (!tagTree) return 0;
        
        let count = 0;
        
        // 计算系统标签
        if (tagTree.tags) {
            Object.values(tagTree.tags).forEach(category => {
                count += Object.keys(category || {}).length;
            });
        }
        
        // 计算属性标签
        if (tagTree.properties) {
            count += Object.keys(tagTree.properties).length;
        }
        
        // 计算自定义标签
        if (tagTree.custom && Array.isArray(tagTree.custom)) {
            count += tagTree.custom.length;
        }
        
        return count;
    }

    // 重新加载
    async reload() {
        console.log('🔄 重新加载数据...');
        this.isInitialized = false;
        await this.initialize();
    }

    // 获取当前状态
    getState() {
        return this.stateManager.getState();
    }

    // 显示通知
    _showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed; top: 20px; right: 20px; padding: 15px 20px;
            background: ${type === 'error' ? '#f44336' : type === 'success' ? '#4caf50' : '#2196f3'};
            color: white; border-radius: 4px; z-index: 10000;
            font-family: Arial, sans-serif; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            max-width: 350px;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    // 添加自动修复功能
    async autoRepair() {
        if (!this.autoRepairEnabled) return;
        
        console.log('🔧 开始自动修复流程...');
        
        try {
            const currentState = this.stateManager.getState();
            if (!currentState.data) {
                console.log('⚠️ 无状态数据，无法修复');
                return;
            }
            
            const { basicInfo, tagTree, files } = currentState.data;
            
            // 检测标签树是否为空
            const isTagTreeEmpty = !tagTree || 
                                  !tagTree.tags || 
                                  (Object.keys(tagTree.tags.metadata || {}).length === 0 &&
                                   Object.keys(tagTree.tags.cv || {}).length === 0 &&
                                   Object.keys(tagTree.tags.user || {}).length === 0 &&
                                   Object.keys(tagTree.tags.ai || {}).length === 0);
            
            // 检测文件列表是否为空
            const isFilesEmpty = !Array.isArray(files) || files.length === 0;
            
            if (isTagTreeEmpty && !isFilesEmpty) {
                console.log('🔍 检测到标签树为空但文件列表不为空，尝试重建标签树');
                
                // 从文件重建标签树
                const rebuiltTagTree = this.dataLoader._buildTagTreeFromFiles(files);
                
                // 更新状态
                const updatedData = {
                    ...currentState.data,
                    tagTree: rebuiltTagTree
                };
                
                await this.stateManager.updateState(updatedData);
                console.log('✅ 标签树重建完成');
                
                return true;
            } else if (isFilesEmpty && basicInfo) {
                console.log('🔍 检测到文件列表为空但基本信息存在，尝试重新加载文件');
                
                // 重新加载文件
                try {
                    const filesData = await this.dataLoader._loadFiles();
                    
                    // 更新状态
                    const updatedData = {
                        ...currentState.data,
                        files: filesData
                    };
                    
                    await this.stateManager.updateState(updatedData);
                    console.log('✅ 文件列表重新加载完成，数量:', filesData.length);
                    
                    return true;
                } catch (error) {
                    console.error('❌ 重新加载文件失败:', error);
                }
            }
            
            console.log('✓ 无需修复或无法修复');
            return false;
        } catch (error) {
            console.error('❌ 自动修复失败:', error);
            return false;
        }
    }
}

// ==================== 全局初始化 ====================

// 获取当前案例ID
function getCurrentCaseId() {
    // 从URL参数获取
    const urlParams = new URLSearchParams(window.location.search);
    let caseId = urlParams.get('caseId');
    
    // 从tagManager获取
    if (!caseId && typeof window.tagManager !== 'undefined') {
        caseId = window.tagManager.currentCaseId;
    }
    
    // 默认使用案例28
    if (!caseId) {
        caseId = 28;
    }
    
    return parseInt(caseId);
}

// 初始化统一架构
async function initializeUnifiedArchitecture() {
    const caseId = getCurrentCaseId();
    console.log(`🎯 为案例 ${caseId} 初始化统一数据流架构...`);
    
    window.unifiedArchitecture = new UnifiedArchitecture(caseId);
    
    try {
        await window.unifiedArchitecture.initialize();
        
        console.log('🎉 统一数据流架构已成功激活！');
        console.log('💡 架构特性:');
        console.log('   • 统一数据加载层 - 消除异步竞争');
        console.log('   • 加固状态管理层 - 原子更新操作');
        console.log('   • 弹性化渲染层 - 防御性渲染');
        console.log('   • 手动重载命令: unifiedArchitecture.reload()');
        
        return true;
    } catch (error) {
        console.error('❌ 统一架构初始化失败:', error);
        return false;
    }
}

// 提供全局控制函数
window.initUnifiedArchitecture = initializeUnifiedArchitecture;
window.reloadUnifiedArchitecture = function() {
    if (window.unifiedArchitecture) {
        return window.unifiedArchitecture.reload();
    } else {
        return initializeUnifiedArchitecture();
    }
};

// 自动初始化
console.log('🏗️ 自动启动统一数据流架构...');
initializeUnifiedArchitecture().then(success => {
    if (success) {
        console.log('🎊 统一数据流架构已成功部署！异步地狱问题已解决。');
    }
});

console.log('📋 统一数据流架构脚本已加载完成！');
