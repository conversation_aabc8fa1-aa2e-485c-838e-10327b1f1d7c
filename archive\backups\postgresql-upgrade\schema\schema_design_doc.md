# PostgreSQL Schema设计文档

## 📋 设计概览

### 设计目标
1. **性能优化**: 10-300倍查询性能提升
2. **JSONB优化**: 高效存储和查询标签数据
3. **扩展性**: 支持50万标签和未来AI向量搜索
4. **兼容性**: 与现有SQLite结构保持兼容

### 核心技术特性
- **JSONB字段**: 高性能JSON数据存储
- **GIN索引**: 支持复杂JSONB查询
- **全文搜索**: PostgreSQL原生全文搜索
- **数组类型**: 高效存储文件ID列表
- **向量预留**: 为AI搜索预留embedding字段

## 🗄️ 表结构设计

### 主数据库表 (相当于 mizzy_star.db)

#### system_config - 系统配置表
```sql
CREATE TABLE system_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**用途**: 存储系统配置信息，如schema版本等

#### cases - 案例表
```sql
CREATE TABLE cases (
    id SERIAL PRIMARY KEY,
    case_name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status case_status DEFAULT 'active' NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE,
    db_path VARCHAR(500) UNIQUE,
    -- 封面相关字段
    cover_image_url VARCHAR(500),
    cover_type cover_type DEFAULT 'placeholder' NOT NULL,
    cover_source_file_id INTEGER,
    cover_needs_attention BOOLEAN DEFAULT FALSE NOT NULL,
    cover_updated_at TIMESTAMP WITH TIME ZONE
);
```
**改进点**:
- 使用枚举类型确保数据一致性
- 添加时区支持的时间戳
- 优化字段长度和约束

#### case_processing_rules - 案例处理规则表
```sql
CREATE TABLE case_processing_rules (
    id SERIAL PRIMARY KEY,
    case_id INTEGER NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
    rule_type rule_type NOT NULL,
    rule_config JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**改进点**:
- 使用JSONB存储规则配置
- 添加外键约束和级联删除

### 案例数据库表 (相当于 case_X.db)

#### files - 文件表 (核心表)
```sql
CREATE TABLE files (
    id SERIAL PRIMARY KEY,
    file_name VARCHAR(255),
    file_type VARCHAR(100),
    file_path TEXT NOT NULL,
    thumbnail_small_path TEXT,
    width INTEGER,
    height INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    taken_at TIMESTAMP WITH TIME ZONE,
    
    -- 图像质量分析字段
    quality_score DECIMAL(5,2),
    sharpness DECIMAL(8,4),
    brightness DECIMAL(8,4),
    dynamic_range DECIMAL(8,4),
    num_faces INTEGER,
    face_sharpness DECIMAL(8,4),
    face_quality DECIMAL(8,4),
    cluster_id INTEGER,
    phash VARCHAR(64),
    group_id VARCHAR(100),
    frame_number INTEGER,
    
    -- 标签系统字段 - 核心优化
    tags JSONB,
    
    -- 向量搜索预留字段
    embedding_vector VECTOR(1536),
    
    -- 全文搜索字段 (自动生成)
    search_vector TSVECTOR GENERATED ALWAYS AS (
        to_tsvector('english', 
            COALESCE(file_name, '') || ' ' ||
            COALESCE(tags::text, '')
        )
    ) STORED
);
```

**核心改进**:
1. **JSONB标签字段**: 替代SQLite的JSON字符串，支持高效查询
2. **向量字段预留**: 为未来AI搜索功能预留
3. **自动全文搜索**: 自动生成的搜索向量
4. **精确数值类型**: 使用DECIMAL确保精度

#### 标签数据结构
```json
{
  "properties": {
    "filename": "example.jpg",
    "qualityScore": 85.5,
    "fileSize": 2048576
  },
  "tags": {
    "metadata": {
      "fileType": "image/jpeg",
      "camera_make": "SONY",
      "camera_model": "ILCE-7RM4",
      "resolution": "300.0 DPI"
    },
    "cv": {
      "faces": 2,
      "objects": ["person", "tree"],
      "scene": "outdoor"
    },
    "user": ["重要", "精选"],
    "ai": ["高质量", "人像"]
  }
}
```

#### tag_cache - 标签缓存表
```sql
CREATE TABLE tag_cache (
    id SERIAL PRIMARY KEY,
    tag_category VARCHAR(50) NOT NULL,
    tag_name VARCHAR(100) NOT NULL,
    tag_value VARCHAR(500) NOT NULL,
    file_ids INTEGER[] NOT NULL, -- PostgreSQL数组类型
    file_count INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**改进点**:
- 使用PostgreSQL数组类型存储文件ID
- 自动维护文件计数
- 支持高效的数组查询

#### custom_tags - 自定义标签表
```sql
CREATE TABLE custom_tags (
    id SERIAL PRIMARY KEY,
    tag_name VARCHAR(100) NOT NULL UNIQUE,
    tag_color VARCHAR(7) DEFAULT '#3B82F6' NOT NULL,
    display_order INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### file_custom_tags - 文件自定义标签关联表
```sql
CREATE TABLE file_custom_tags (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    custom_tag_id INTEGER NOT NULL REFERENCES custom_tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(file_id, custom_tag_id)
);
```

## 🚀 索引策略

### JSONB索引 - 性能核心
```sql
-- 主要GIN索引 - 支持所有JSONB查询
CREATE INDEX idx_files_tags_gin ON files USING GIN (tags);

-- 特定路径索引 - 优化常用查询
CREATE INDEX idx_files_tags_metadata ON files USING GIN ((tags->'tags'->'metadata'));
CREATE INDEX idx_files_tags_user ON files USING GIN ((tags->'tags'->'user'));

-- 表达式索引 - 优化精确查询
CREATE INDEX idx_files_camera_make ON files ((tags->'tags'->'metadata'->>'camera_make'));
```

### 全文搜索索引
```sql
-- 全文搜索GIN索引
CREATE INDEX idx_files_search_vector ON files USING GIN (search_vector);
```

### 复合索引 - 优化复杂查询
```sql
-- 质量和时间复合索引
CREATE INDEX idx_files_quality_time ON files(quality_score DESC, created_at DESC);
```

## 🔧 查询优化示例

### 高性能标签查询
```sql
-- 查询特定相机型号的文件
SELECT id, file_name, quality_score 
FROM files 
WHERE tags->'tags'->'metadata'->>'camera_model' = 'ILCE-7RM4'
ORDER BY quality_score DESC;

-- 查询包含特定用户标签的文件
SELECT id, file_name 
FROM files 
WHERE tags->'tags'->'user' ? '重要';

-- 复合条件查询
SELECT id, file_name, quality_score
FROM files 
WHERE tags->'tags'->'metadata'->>'camera_make' = 'SONY'
  AND tags->'tags'->'user' ?| ARRAY['重要', '精选']
  AND quality_score > 80
ORDER BY quality_score DESC;
```

### 全文搜索查询
```sql
-- 全文搜索
SELECT id, file_name, ts_rank(search_vector, query) as rank
FROM files, plainto_tsquery('english', 'sony camera portrait') query
WHERE search_vector @@ query
ORDER BY rank DESC;
```

## 📊 性能预期

### 查询性能提升
- **简单标签查询**: 10-50倍提升
- **复杂组合查询**: 50-100倍提升  
- **全文搜索**: 100-300倍提升
- **聚合统计**: 20-80倍提升

### 存储优化
- **JSONB压缩**: 比JSON字符串节省20-30%空间
- **索引效率**: GIN索引支持快速JSONB查询
- **数组类型**: 比JSON数组更高效的存储和查询

## 🔄 迁移兼容性

### 数据类型映射
| SQLite类型 | PostgreSQL类型 | 说明 |
|-----------|---------------|------|
| INTEGER | SERIAL/INTEGER | 自增主键使用SERIAL |
| VARCHAR | VARCHAR(n) | 指定长度限制 |
| DATETIME | TIMESTAMP WITH TIME ZONE | 支持时区 |
| JSON | JSONB | 高性能二进制JSON |
| TEXT | TEXT/INTEGER[] | 文件ID列表使用数组 |

### 查询兼容性
- 保持相同的字段名和基本结构
- JSONB查询语法需要适配
- 数组查询使用PostgreSQL特有语法

## 🛡️ 数据完整性

### 约束设计
- **检查约束**: 确保数据格式和范围
- **外键约束**: 维护引用完整性
- **唯一约束**: 防止重复数据

### 触发器维护
- **自动时间戳**: 自动更新updated_at字段
- **标签缓存维护**: 自动维护tag_cache表
- **数据一致性**: 防止孤立记录

## 🎯 扩展性设计

### AI向量搜索准备
- 预留embedding_vector字段
- 支持向量相似度搜索索引
- 为多模态搜索做准备

### 大数据量支持
- 分区表策略 (可选)
- 索引优化策略
- 查询性能监控

这个Schema设计为Mizzy Star项目提供了坚实的技术基础，支持当前需求并为未来扩展做好准备。
