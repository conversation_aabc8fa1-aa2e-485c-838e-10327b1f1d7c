# 🐘 PostgreSQL完整安装指南

## 📋 快速开始

### 方法1: 自动安装脚本 (推荐)

1. **以管理员身份运行批处理文件**
   ```bash
   # 右键点击文件，选择"以管理员身份运行"
   install_postgresql.bat
   ```

2. **安装Python依赖**
   ```bash
   cd backend
   python install_postgresql_deps.py
   ```

3. **初始化数据库**
   ```bash
   python init_database.py --reset
   ```

4. **运行测试**
   ```bash
   python test_postgresql_migration.py
   ```

### 方法2: 手动安装

如果自动安装失败，请按照以下步骤手动安装：

## 🔧 手动安装步骤

### 步骤1: 下载PostgreSQL

1. 访问官方下载页面: https://www.postgresql.org/download/windows/
2. 点击 "Download the installer"
3. 选择最新版本 (推荐PostgreSQL 15.x或16.x)
4. 下载64位安装程序

### 步骤2: 安装PostgreSQL

1. **运行安装程序** (以管理员身份)
2. **安装路径**: 保持默认 `C:\Program Files\PostgreSQL\16`
3. **组件选择**: 全部勾选
   - PostgreSQL Server
   - pgAdmin 4
   - Stack Builder
   - Command Line Tools
4. **数据目录**: 保持默认
5. **超级用户密码**: 设置为 `mizzy_star_2025` (或自定义)
6. **端口**: 保持默认 `5432`
7. **区域设置**: 保持默认
8. **完成安装**

### 步骤3: 配置环境变量

1. 打开系统环境变量设置
2. 在系统变量PATH中添加:
   ```
   C:\Program Files\PostgreSQL\16\bin
   ```
3. 重启命令提示符

### 步骤4: 创建数据库

打开命令提示符，运行：

```bash
# 连接到PostgreSQL
psql -U postgres -h localhost

# 在PostgreSQL命令行中执行:
CREATE DATABASE mizzy_star_db;
CREATE USER mizzy_user WITH PASSWORD 'mizzy_star_2025';
GRANT ALL PRIVILEGES ON DATABASE mizzy_star_db TO mizzy_user;

# 安装pgvector扩展
\c mizzy_star_db
CREATE EXTENSION IF NOT EXISTS vector;

# 退出
\q
```

### 步骤5: 安装Python依赖

```bash
cd backend
pip install psycopg2-binary pgvector sqlalchemy[postgresql]
```

### 步骤6: 配置环境变量

在项目根目录创建 `.env` 文件：

```bash
# PostgreSQL配置
USE_POSTGRESQL=true
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=mizzy_star_2025
POSTGRES_DB=mizzy_star_db
```

### 步骤7: 初始化和测试

```bash
cd backend

# 初始化数据库
python init_database.py --reset

# 运行测试
python test_postgresql_migration.py

# 启动服务
python -m uvicorn src.main:app --reload
```

## 🛠️ 故障排除

### 问题1: psql命令不存在

**解决方案**:
1. 确认PostgreSQL已正确安装
2. 检查PATH环境变量是否包含PostgreSQL bin目录
3. 重启命令提示符

### 问题2: 连接被拒绝

**错误**: `connection refused`

**解决方案**:
1. 检查PostgreSQL服务是否运行:
   ```bash
   # 在服务管理器中查找 postgresql-x64-16
   services.msc
   ```
2. 启动服务:
   ```bash
   net start postgresql-x64-16
   ```

### 问题3: 密码认证失败

**错误**: `password authentication failed`

**解决方案**:
1. 确认密码正确
2. 检查用户是否存在
3. 重置postgres用户密码:
   ```bash
   # 以系统用户身份运行
   psql -U postgres
   ALTER USER postgres PASSWORD 'new_password';
   ```

### 问题4: 端口被占用

**错误**: `port 5432 already in use`

**解决方案**:
1. 检查端口占用:
   ```bash
   netstat -an | findstr :5432
   ```
2. 停止占用端口的程序或更改PostgreSQL端口

### 问题5: pgvector扩展安装失败

**解决方案**:
1. 确认PostgreSQL版本支持pgvector
2. 手动下载pgvector扩展
3. 或使用Docker PostgreSQL镜像

## 📊 验证安装

运行以下命令验证安装是否成功：

```bash
# 检查PostgreSQL版本
psql --version

# 检查服务状态
sc query postgresql-x64-16

# 测试连接
psql -U postgres -h localhost -c "SELECT version();"

# 检查数据库
psql -U postgres -h localhost -c "\l"

# 检查扩展
psql -U postgres -d mizzy_star_db -c "\dx"
```

## 🎯 性能优化建议

### PostgreSQL配置优化

编辑 `postgresql.conf` 文件 (通常在数据目录中):

```ini
# 内存设置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

# 连接设置
max_connections = 100

# 日志设置
log_statement = 'all'
log_duration = on
```

### 索引优化

```sql
-- 为常用查询创建索引
CREATE INDEX idx_files_case_id ON files(case_id);
CREATE INDEX idx_files_created_at ON files(created_at DESC);
CREATE INDEX idx_cases_status ON cases(status);

-- 向量索引 (需要pgvector)
CREATE INDEX idx_files_vector_image ON files USING ivfflat(vector_image vector_cosine_ops);
```

## 🔄 从SQLite迁移数据

如果您有现有的SQLite数据需要迁移：

```bash
# 1. 备份现有数据
cp mizzy_star_unified.db mizzy_star_backup.db

# 2. 导出SQLite数据
sqlite3 mizzy_star_unified.db .dump > data_export.sql

# 3. 转换为PostgreSQL格式 (可能需要手动调整)
# 4. 导入到PostgreSQL
psql -U postgres -d mizzy_star_db -f data_export.sql
```

## 📞 技术支持

如果遇到问题：

1. 检查PostgreSQL官方文档
2. 查看Windows事件日志
3. 检查防火墙设置
4. 确认系统权限
5. 查看PostgreSQL日志文件

## 🎉 完成

安装完成后，您的mizzy_star项目将具备：

- ✅ 高性能PostgreSQL数据库
- ✅ 向量搜索支持 (pgvector)
- ✅ 完整的ACID事务支持
- ✅ 并发访问能力
- ✅ 企业级数据完整性

**下一步**: 开始使用新的PostgreSQL架构进行开发！
