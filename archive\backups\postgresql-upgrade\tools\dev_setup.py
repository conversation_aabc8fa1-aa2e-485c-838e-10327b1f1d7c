#!/usr/bin/env python3
"""
开发环境配置脚本 for Mizzy Star PostgreSQL升级
自动化开发环境设置，包括依赖安装、数据库配置、测试数据生成
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DevEnvironmentSetup:
    """开发环境设置器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.tools_dir = self.project_root / 'tools'
        self.docker_dir = self.project_root / 'docker'
        
    def check_python_version(self) -> bool:
        """检查Python版本"""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            logger.error("需要Python 3.8或更高版本")
            return False
        
        logger.info(f"Python版本: {version.major}.{version.minor}.{version.micro} ✅")
        return True
    
    def check_docker_status(self) -> bool:
        """检查Docker状态"""
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info(f"Docker版本: {result.stdout.strip()} ✅")
                
                # 检查Docker是否运行
                result = subprocess.run(['docker', 'info'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    logger.info("Docker服务正在运行 ✅")
                    return True
                else:
                    logger.warning("Docker服务未运行 ⚠️")
                    return False
            else:
                logger.error("Docker命令执行失败")
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.error("Docker未安装或不在PATH中 ❌")
            return False
    
    def install_python_dependencies(self) -> bool:
        """安装Python依赖"""
        logger.info("安装Python依赖...")
        
        dependencies = [
            'psycopg2-binary>=2.9.0',
            'python-dotenv>=0.19.0',
            'click>=8.0.0',
            'rich>=12.0.0',
            'tabulate>=0.9.0'
        ]
        
        try:
            for dep in dependencies:
                logger.info(f"安装 {dep}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', dep
                ], capture_output=True, text=True)
                
                if result.returncode != 0:
                    logger.error(f"安装 {dep} 失败: {result.stderr}")
                    return False
            
            logger.info("Python依赖安装完成 ✅")
            return True
            
        except Exception as e:
            logger.error(f"安装依赖时发生错误: {e}")
            return False
    
    def setup_environment_file(self) -> bool:
        """设置环境变量文件"""
        logger.info("设置环境变量文件...")
        
        env_example = self.docker_dir / '.env.example'
        env_file = self.docker_dir / '.env'
        
        if not env_example.exists():
            logger.error(f"环境变量模板文件不存在: {env_example}")
            return False
        
        if not env_file.exists():
            # 复制模板文件
            with open(env_example, 'r', encoding='utf-8') as f:
                content = f.read()
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"环境变量文件已创建: {env_file} ✅")
        else:
            logger.info("环境变量文件已存在 ✅")
        
        return True
    
    def start_postgresql_service(self) -> bool:
        """启动PostgreSQL服务"""
        logger.info("启动PostgreSQL服务...")
        
        try:
            # 切换到docker目录
            os.chdir(self.docker_dir)
            
            # 启动PostgreSQL容器
            result = subprocess.run([
                'docker-compose', 'up', '-d', 'postgres'
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"启动PostgreSQL失败: {result.stderr}")
                return False
            
            logger.info("PostgreSQL服务启动成功 ✅")
            
            # 等待服务就绪
            import time
            logger.info("等待PostgreSQL服务就绪...")
            time.sleep(10)
            
            # 测试连接
            result = subprocess.run([
                'docker-compose', 'exec', '-T', 'postgres',
                'psql', '-U', 'mizzy_user', '-d', 'mizzy_main',
                '-c', 'SELECT version();'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("PostgreSQL连接测试成功 ✅")
                return True
            else:
                logger.warning("PostgreSQL连接测试失败，但服务可能正在启动中")
                return True
                
        except Exception as e:
            logger.error(f"启动PostgreSQL服务时发生错误: {e}")
            return False
    
    def initialize_database_schema(self) -> bool:
        """初始化数据库Schema"""
        logger.info("初始化数据库Schema...")
        
        try:
            # 运行数据库初始化工具
            db_initializer = self.tools_dir / 'db_initializer.py'
            
            result = subprocess.run([
                sys.executable, str(db_initializer)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("数据库Schema初始化成功 ✅")
                return True
            else:
                logger.error(f"数据库Schema初始化失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"初始化数据库Schema时发生错误: {e}")
            return False
    
    def generate_test_data(self, num_files: int = 1000) -> bool:
        """生成测试数据"""
        logger.info(f"生成 {num_files} 条测试数据...")
        
        try:
            # 运行测试数据生成器
            data_generator = self.tools_dir / 'test_data_generator.py'
            
            result = subprocess.run([
                sys.executable, str(data_generator),
                '--files', str(num_files),
                '--batch-size', '500'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("测试数据生成成功 ✅")
                return True
            else:
                logger.error(f"测试数据生成失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"生成测试数据时发生错误: {e}")
            return False
    
    def run_performance_test(self) -> bool:
        """运行性能测试"""
        logger.info("运行性能测试...")
        
        try:
            # 运行性能测试工具
            performance_tester = self.tools_dir / 'performance_tester.py'
            
            result = subprocess.run([
                sys.executable, str(performance_tester),
                '--output', str(self.project_root / 'performance_report.txt')
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("性能测试完成 ✅")
                return True
            else:
                logger.error(f"性能测试失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"运行性能测试时发生错误: {e}")
            return False
    
    def create_development_scripts(self):
        """创建开发便利脚本"""
        logger.info("创建开发便利脚本...")
        
        # 创建快速启动脚本
        quick_start_script = self.project_root / 'quick_start.py'
        quick_start_content = '''#!/usr/bin/env python3
"""
快速启动脚本 - Mizzy Star PostgreSQL开发环境
"""

import subprocess
import sys
from pathlib import Path

def main():
    project_root = Path(__file__).parent
    docker_dir = project_root / 'docker'
    
    print("🚀 启动Mizzy Star PostgreSQL开发环境...")
    
    # 启动PostgreSQL
    subprocess.run(['docker-compose', 'up', '-d'], cwd=docker_dir)
    
    print("✅ PostgreSQL服务已启动")
    print("📊 访问pgAdmin: http://localhost:8080 (如果启用)")
    print("🔗 数据库连接: localhost:5432/mizzy_main")
    print("👤 用户名: mizzy_user")

if __name__ == "__main__":
    main()
'''
        
        with open(quick_start_script, 'w', encoding='utf-8') as f:
            f.write(quick_start_content)
        
        # 创建快速测试脚本
        quick_test_script = self.project_root / 'quick_test.py'
        quick_test_content = '''#!/usr/bin/env python3
"""
快速测试脚本 - 运行基本的功能和性能测试
"""

import subprocess
import sys
from pathlib import Path

def main():
    project_root = Path(__file__).parent
    tools_dir = project_root / 'tools'
    
    print("🧪 运行快速测试...")
    
    # 运行数据库验证
    print("1. 验证数据库Schema...")
    subprocess.run([sys.executable, str(tools_dir / 'db_initializer.py'), '--verify-only'])
    
    # 运行基本性能测试
    print("2. 运行性能测试...")
    subprocess.run([sys.executable, str(tools_dir / 'performance_tester.py'), '--test-suite', 'basic'])
    
    print("✅ 快速测试完成")

if __name__ == "__main__":
    main()
'''
        
        with open(quick_test_script, 'w', encoding='utf-8') as f:
            f.write(quick_test_content)
        
        logger.info("开发便利脚本创建完成 ✅")
    
    def setup_complete_environment(self, include_test_data: bool = True, num_test_files: int = 1000) -> bool:
        """完整的开发环境设置"""
        logger.info("开始设置完整的开发环境...")
        
        steps = [
            ("检查Python版本", self.check_python_version),
            ("检查Docker状态", self.check_docker_status),
            ("安装Python依赖", self.install_python_dependencies),
            ("设置环境变量文件", self.setup_environment_file),
            ("启动PostgreSQL服务", self.start_postgresql_service),
            ("初始化数据库Schema", self.initialize_database_schema),
        ]
        
        if include_test_data:
            steps.append(("生成测试数据", lambda: self.generate_test_data(num_test_files)))
            steps.append(("运行性能测试", self.run_performance_test))
        
        steps.append(("创建开发脚本", lambda: (self.create_development_scripts(), True)[1]))
        
        failed_steps = []
        
        for step_name, step_func in steps:
            logger.info(f"\n{'='*50}")
            logger.info(f"执行步骤: {step_name}")
            logger.info(f"{'='*50}")
            
            try:
                if not step_func():
                    failed_steps.append(step_name)
                    logger.error(f"步骤失败: {step_name}")
                else:
                    logger.info(f"步骤成功: {step_name}")
            except Exception as e:
                failed_steps.append(step_name)
                logger.error(f"步骤异常: {step_name} - {e}")
        
        # 总结
        logger.info(f"\n{'='*50}")
        logger.info("开发环境设置完成")
        logger.info(f"{'='*50}")
        
        if failed_steps:
            logger.warning(f"失败的步骤: {', '.join(failed_steps)}")
            logger.info("请手动检查和修复失败的步骤")
            return False
        else:
            logger.info("🎉 所有步骤都成功完成!")
            logger.info("\n📋 下一步操作:")
            logger.info("1. 运行 python quick_start.py 启动服务")
            logger.info("2. 运行 python quick_test.py 进行测试")
            logger.info("3. 查看 performance_report.txt 了解性能情况")
            return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='开发环境配置脚本')
    parser.add_argument('--skip-test-data', action='store_true', help='跳过测试数据生成')
    parser.add_argument('--test-files', type=int, default=1000, help='测试文件数量 (默认: 1000)')
    parser.add_argument('--deps-only', action='store_true', help='仅安装依赖')
    
    args = parser.parse_args()
    
    setup = DevEnvironmentSetup()
    
    if args.deps_only:
        logger.info("仅安装依赖模式...")
        success = (setup.check_python_version() and 
                  setup.install_python_dependencies())
    else:
        success = setup.setup_complete_environment(
            include_test_data=not args.skip_test_data,
            num_test_files=args.test_files
        )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
