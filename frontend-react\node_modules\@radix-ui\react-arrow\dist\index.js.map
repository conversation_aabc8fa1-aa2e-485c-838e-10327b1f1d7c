{"version": 3, "sources": ["../src/index.ts", "../src/arrow.tsx"], "sourcesContent": ["export {\n  Arrow,\n  //\n  Root,\n} from './arrow';\nexport type { ArrowProps } from './arrow';\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Arrow';\n\ntype ArrowElement = React.ComponentRef<typeof Primitive.svg>;\ntype PrimitiveSvgProps = React.ComponentPropsWithoutRef<typeof Primitive.svg>;\ninterface ArrowProps extends PrimitiveSvgProps {}\n\nconst Arrow = React.forwardRef<ArrowElement, ArrowProps>((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return (\n    <Primitive.svg\n      {...arrowProps}\n      ref={forwardedRef}\n      width={width}\n      height={height}\n      viewBox=\"0 0 30 10\"\n      preserveAspectRatio=\"none\"\n    >\n      {/* We use their children if they're slotting to replace the whole svg */}\n      {props.asChild ? children : <polygon points=\"0,0 30,0 15,10\" />}\n    </Primitive.svg>\n  );\n});\n\nArrow.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Arrow;\n\nexport {\n  Arrow,\n  //\n  Root,\n};\nexport type { ArrowProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,6BAA0B;AAwBQ;AAlBlC,IAAM,OAAO;AAMb,IAAM,QAAc,iBAAqC,CAAC,OAAO,iBAAiB;AAChF,QAAM,EAAE,UAAU,QAAQ,IAAI,SAAS,GAAG,GAAG,WAAW,IAAI;AAC5D,SACE;AAAA,IAAC,iCAAU;AAAA,IAAV;AAAA,MACE,GAAG;AAAA,MACJ,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAQ;AAAA,MACR,qBAAoB;AAAA,MAGnB,gBAAM,UAAU,WAAW,4CAAC,aAAQ,QAAO,kBAAiB;AAAA;AAAA,EAC/D;AAEJ,CAAC;AAED,MAAM,cAAc;AAIpB,IAAM,OAAO;", "names": []}