/**
 * Filename Extraction Feature
 * Allows users to extract fields from filenames based on patterns and create custom tags
 */

class FilenameExtractionTool {
    constructor(caseId) {
        this.caseId = caseId;
        this.selectedFiles = [];
        this.extractionRules = [];
        this.modalElement = null;
    }

    /**
     * Initialize the tool and create the UI
     */
    initialize() {
        this.createModal();
        this.attachEventListeners();
        console.log('✅ Filename extraction tool initialized');
    }

    /**
     * Create the modal dialog for the filename extraction tool
     */
    createModal() {
        // Create modal element if it doesn't exist
        if (!document.getElementById('filename-extraction-modal')) {
            const modalHtml = `
                <div id="filename-extraction-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
                    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h2 class="text-xl font-bold text-gray-900">从文件名提取标签</h2>
                                <button id="close-extraction-modal" class="text-gray-500 hover:text-gray-700">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>

                            <div class="mb-4">
                                <p class="text-gray-600 mb-2">从选定文件的文件名中提取字段并创建自定义标签。</p>
                                <div class="bg-blue-50 p-3 rounded-lg">
                                    <p class="text-sm text-blue-700">已选择 <span id="extraction-file-count">0</span> 个文件</p>
                                </div>
                            </div>

                            <div class="mb-6">
                                <h3 class="font-semibold text-gray-900 mb-2">文件名示例</h3>
                                <div id="filename-examples" class="bg-gray-50 p-3 rounded-lg text-sm font-mono max-h-24 overflow-y-auto">
                                    <!-- 示例文件名将在这里显示 -->
                                </div>
                            </div>

                            <div class="mb-6">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-semibold text-gray-900">提取规则</h3>
                                    <button id="add-extraction-rule" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                        添加规则
                                    </button>
                                </div>
                                <div id="extraction-rules-container" class="space-y-3">
                                    <!-- 提取规则将在这里动态生成 -->
                                </div>
                            </div>

                            <div class="mb-6">
                                <h3 class="font-semibold text-gray-900 mb-2">选项</h3>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="overwrite-existing-tags" class="mr-2">
                                        覆盖已存在的同名标签
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="skip-errors" class="mr-2" checked>
                                        遇到错误时跳过该文件
                                    </label>
                                </div>
                            </div>

                            <div class="flex justify-end space-x-2">
                                <button id="cancel-extraction" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
                                    取消
                                </button>
                                <button id="apply-extraction" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                                    应用提取规则
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            this.modalElement = document.getElementById('filename-extraction-modal');
        }
    }

    /**
     * Attach event listeners to UI elements
     */
    attachEventListeners() {
        // Close modal buttons
        document.getElementById('close-extraction-modal').addEventListener('click', () => this.hideModal());
        document.getElementById('cancel-extraction').addEventListener('click', () => this.hideModal());
        
        // Add rule button
        document.getElementById('add-extraction-rule').addEventListener('click', () => this.addExtractionRule());
        
        // Apply extraction button
        document.getElementById('apply-extraction').addEventListener('click', () => this.applyExtractionRules());
    }

    /**
     * Show the extraction modal with selected files
     * @param {Array} selectedFiles - Array of selected file objects
     */
    showModal(selectedFiles) {
        if (!selectedFiles || selectedFiles.length === 0) {
            showNotification('请先选择文件', 'warning');
            return;
        }
        
        this.selectedFiles = selectedFiles;
        
        // Update file count
        document.getElementById('extraction-file-count').textContent = selectedFiles.length;
        
        // Show filename examples
        this.updateFilenameExamples();
        
        // Reset rules
        this.extractionRules = [];
        document.getElementById('extraction-rules-container').innerHTML = '';
        
        // Add initial rule
        this.addExtractionRule();
        
        // Show modal
        this.modalElement.classList.remove('hidden');
    }

    /**
     * Hide the extraction modal
     */
    hideModal() {
        this.modalElement.classList.add('hidden');
    }

    /**
     * Update the filename examples in the UI
     */
    updateFilenameExamples() {
        const container = document.getElementById('filename-examples');
        const maxExamples = 5;
        
        // Get up to 5 example filenames
        const examples = this.selectedFiles
            .slice(0, maxExamples)
            .map(file => file.file_name);
            
        // Add ellipsis if there are more files
        if (this.selectedFiles.length > maxExamples) {
            examples.push('...');
        }
        
        // Update container
        container.innerHTML = examples.map(name => `<div>${name}</div>`).join('');
    }

    /**
     * Add a new extraction rule to the UI
     */
    addExtractionRule() {
        const container = document.getElementById('extraction-rules-container');
        const ruleId = Date.now(); // Unique ID for the rule
        
        const ruleHtml = `
            <div class="extraction-rule bg-gray-50 p-3 rounded-lg" data-rule-id="${ruleId}">
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-medium text-gray-900">规则 #${this.extractionRules.length + 1}</h4>
                    <button class="delete-rule text-red-600 hover:text-red-800">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">分隔符</label>
                        <select class="rule-delimiter w-full border border-gray-300 rounded-md p-2">
                            <option value="_">下划线 (_)</option>
                            <option value="-">连字符 (-)</option>
                            <option value=".">点 (.)</option>
                            <option value=" ">空格 ( )</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">位置</label>
                        <div class="flex space-x-2">
                            <input type="number" class="rule-position w-full border border-gray-300 rounded-md p-2" min="0" value="0">
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">标签名称</label>
                    <input type="text" class="rule-tag-name w-full border border-gray-300 rounded-md p-2" placeholder="输入标签名称">
                </div>
                
                <div class="mt-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">标签颜色</label>
                    <input type="color" class="rule-tag-color w-full border border-gray-300 rounded-md p-1 h-10" value="#3B82F6">
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', ruleHtml);
        
        // Add event listener to delete button
        const ruleElement = container.lastElementChild;
        ruleElement.querySelector('.delete-rule').addEventListener('click', () => {
            this.deleteExtractionRule(ruleId);
        });
        
        // Add rule to array
        this.extractionRules.push({
            id: ruleId,
            delimiter: '_',
            position: 0,
            tagName: '',
            tagColor: '#3B82F6'
        });
    }

    /**
     * Delete an extraction rule from the UI
     * @param {number} ruleId - ID of the rule to delete
     */
    deleteExtractionRule(ruleId) {
        // Remove from DOM
        const ruleElement = document.querySelector(`.extraction-rule[data-rule-id="${ruleId}"]`);
        if (ruleElement) {
            ruleElement.remove();
        }
        
        // Remove from array
        this.extractionRules = this.extractionRules.filter(rule => rule.id !== ruleId);
        
        // Update rule numbers
        document.querySelectorAll('.extraction-rule').forEach((el, index) => {
            el.querySelector('h4').textContent = `规则 #${index + 1}`;
        });
    }

    /**
     * Collect rule data from the UI
     */
    collectRuleData() {
        const rules = [];
        
        document.querySelectorAll('.extraction-rule').forEach((el, index) => {
            const ruleId = parseInt(el.dataset.ruleId);
            const delimiter = el.querySelector('.rule-delimiter').value;
            const position = parseInt(el.querySelector('.rule-position').value);
            const tagName = el.querySelector('.rule-tag-name').value.trim();
            const tagColor = el.querySelector('.rule-tag-color').value;
            
            // Validate
            if (!tagName) {
                throw new Error(`规则 #${index + 1} 缺少标签名称`);
            }
            
            rules.push({
                id: ruleId,
                delimiter,
                position,
                tagName,
                tagColor
            });
        });
        
        return rules;
    }

    /**
     * Apply extraction rules to selected files
     */
    async applyExtractionRules() {
        try {
            // Collect options
            const overwriteExisting = document.getElementById('overwrite-existing-tags').checked;
            const skipErrors = document.getElementById('skip-errors').checked;
            
            // Collect rules
            this.extractionRules = this.collectRuleData();
            
            if (this.extractionRules.length === 0) {
                showNotification('请添加至少一条提取规则', 'warning');
                return;
            }
            
            // Show loading state
            const applyButton = document.getElementById('apply-extraction');
            const originalText = applyButton.textContent;
            applyButton.textContent = '处理中...';
            applyButton.disabled = true;
            
            // Process each file
            const results = {
                success: 0,
                failed: 0,
                tags: {}
            };
            
            for (const file of this.selectedFiles) {
                try {
                    // Extract fields from filename
                    const extractedTags = this.extractTagsFromFilename(file);
                    
                    // Create custom tags
                    for (const [tagName, tagValue] of Object.entries(extractedTags)) {
                        // Skip if tag already exists and overwrite is disabled
                        if (!overwriteExisting && file.tags && file.tags.custom_tags && 
                            file.tags.custom_tags.some(tag => tag.name === tagName)) {
                            console.log(`跳过已存在的标签: ${tagName}`);
                            continue;
                        }
                        
                        // Create or update tag
                        await this.createOrUpdateCustomTag(file.id, tagName, tagValue);
                        
                        // Track created tags
                        if (!results.tags[tagName]) {
                            results.tags[tagName] = 0;
                        }
                        results.tags[tagName]++;
                    }
                    
                    results.success++;
                } catch (error) {
                    console.error(`处理文件 ${file.file_name} 失败:`, error);
                    results.failed++;
                    
                    if (!skipErrors) {
                        throw error;
                    }
                }
            }
            
            // Show results
            const tagCount = Object.keys(results.tags).length;
            showNotification(
                `成功处理 ${results.success} 个文件，创建 ${tagCount} 个自定义标签`,
                results.failed > 0 ? 'warning' : 'success'
            );
            
            // Hide modal
            this.hideModal();
            
            // Refresh tag data
            if (window.tagApp) {
                window.tagApp.loadData();
            }
            
        } catch (error) {
            console.error('应用提取规则失败:', error);
            showNotification(`应用提取规则失败: ${error.message}`, 'error');
        } finally {
            // Reset button state
            const applyButton = document.getElementById('apply-extraction');
            applyButton.textContent = '应用提取规则';
            applyButton.disabled = false;
        }
    }

    /**
     * Extract tags from a filename based on the defined rules
     * @param {Object} file - File object
     * @returns {Object} - Extracted tags
     */
    extractTagsFromFilename(file) {
        const extractedTags = {};
        const filename = file.file_name;
        
        // Process each rule
        for (const rule of this.extractionRules) {
            // Split filename by delimiter
            const parts = filename.split(rule.delimiter);
            
            // Check if position is valid
            if (rule.position >= 0 && rule.position < parts.length) {
                // Extract value
                const value = parts[rule.position].trim();
                
                // Store extracted value
                extractedTags[rule.tagName] = {
                    value,
                    color: rule.tagColor
                };
            }
        }
        
        return extractedTags;
    }

    /**
     * Create or update a custom tag for a file
     * @param {number} fileId - File ID
     * @param {string} tagName - Tag name
     * @param {Object} tagData - Tag data including value and color
     */
    async createOrUpdateCustomTag(fileId, tagName, tagData) {
        try {
            // Create custom tag if it doesn't exist
            let customTag = await this.findCustomTagByName(tagName);
            
            if (!customTag) {
                customTag = await api.createCustomTag(this.caseId, {
                    name: tagName,
                    color: tagData.color
                });
            }
            
            // Add tag to file
            await api.addCustomTagToFile(this.caseId, fileId, customTag.id);
            
            return customTag;
        } catch (error) {
            console.error(`创建或更新标签 ${tagName} 失败:`, error);
            throw error;
        }
    }

    /**
     * Find a custom tag by name
     * @param {string} tagName - Tag name
     * @returns {Object|null} - Custom tag object or null if not found
     */
    async findCustomTagByName(tagName) {
        try {
            const customTags = await api.getCustomTags(this.caseId);
            return customTags.find(tag => tag.name === tagName) || null;
        } catch (error) {
            console.error(`查找标签 ${tagName} 失败:`, error);
            return null;
        }
    }
}

// Initialize the tool when the page loads
document.addEventListener('DOMContentLoaded', () => {
    // Wait for tagApp to be initialized
    const initInterval = setInterval(() => {
        if (window.tagApp) {
            clearInterval(initInterval);
            
            // Get case ID from tagApp
            const caseId = window.tagApp.currentCaseId;
            
            if (caseId) {
                // Create and initialize the tool
                window.filenameExtractionTool = new FilenameExtractionTool(caseId);
                window.filenameExtractionTool.initialize();
                
                // Add button to batch actions
                const batchActions = document.querySelector('#batch-actions .flex');
                if (batchActions) {
                    const extractButton = document.createElement('button');
                    extractButton.id = 'batch-extract-tags-btn';
                    extractButton.className = 'px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-700';
                    extractButton.textContent = '从文件名提取标签';
                    extractButton.addEventListener('click', () => {
                        const selectedFiles = [];
                        window.tagApp.selectedFiles.forEach(fileId => {
                            const file = window.tagApp.currentFiles.find(f => f.id === fileId);
                            if (file) {
                                selectedFiles.push(file);
                            }
                        });
                        
                        window.filenameExtractionTool.showModal(selectedFiles);
                    });
                    
                    batchActions.appendChild(extractButton);
                }
            }
        }
    }, 500);
}); 