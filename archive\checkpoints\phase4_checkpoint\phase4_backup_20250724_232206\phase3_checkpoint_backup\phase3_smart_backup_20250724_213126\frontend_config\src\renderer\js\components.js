// 组件管理器
class ComponentManager {
    constructor() {
        this.filePond = null;
        this.currentCase = null;
    }

    // 创建案例卡片
    createCaseCard(caseData, isTrash = false) {
        const card = document.createElement('div');
        card.className = 'card hover:shadow-md transition-shadow cursor-pointer';
        card.dataset.caseId = caseData.id;

        const statusColor = caseData.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';

        // 获取封面信息
        const coverStatus = api.getCoverStatus(caseData);
        const coverUrl = coverStatus ? coverStatus.url : null;
        const coverType = coverStatus ? coverStatus.type : 'placeholder';
        const needsAttention = coverStatus ? coverStatus.needsAttention : false;

        card.innerHTML = `
            <!-- 封面图片区域 -->
            <div class="relative mb-3">
                <div class="w-full h-32 bg-gray-100 rounded-lg overflow-hidden">
                    ${coverUrl ? `
                        <img src="${coverUrl}"
                             alt="案例封面"
                             class="w-full h-full object-cover"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDIwMCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik02MCA0MEgxNDBWODhINjBWNDBaIiBmaWxsPSIjRTVFN0VCIiBzdHJva2U9IiNEMUQ1REIiIHN0cm9rZS13aWR0aD0iMiIgcng9IjQiLz4KPGNpcmNsZSBjeD0iODUiIGN5PSI1NSIgcj0iOCIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNNjAgNzJMODUgNTVMMTEwIDY4TDEzMCA2MEwxNDAgNzJWODhINjBWNzJaIiBmaWxsPSIjRDFENURCIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTIiPuaaguaXoOWyuumdojwvdGV4dD4KPC9zdmc+'">
                    ` : `
                        <div class="w-full h-full flex items-center justify-center bg-gray-100">
                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    `}
                </div>

                <!-- 封面类型标识 -->
                <div class="absolute top-2 left-2 flex items-center space-x-1">
                    ${coverType === 'manual' ? `
                        <span class="px-2 py-1 bg-blue-500 text-white text-xs rounded-full">手动</span>
                    ` : coverType === 'automatic' ? `
                        <span class="px-2 py-1 bg-green-500 text-white text-xs rounded-full">自动</span>
                    ` : `
                        <span class="px-2 py-1 bg-gray-500 text-white text-xs rounded-full">占位</span>
                    `}

                    ${needsAttention ? `
                        <span class="px-2 py-1 bg-orange-500 text-white text-xs rounded-full" title="需要关注">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </span>
                    ` : ''}
                </div>
            </div>

            <div class="flex items-start justify-between mb-3">
                <h3 class="text-lg font-semibold text-secondary-900 truncate">${caseData.case_name}</h3>
                <span class="px-2 py-1 rounded-full text-xs font-medium ${statusColor}">
                    ${caseData.status === 'active' ? '活跃' : '已归档'}
                </span>
            </div>

            <p class="text-secondary-600 text-sm mb-4 line-clamp-2">${caseData.description || '暂无描述'}</p>

            <div class="flex items-center justify-between text-sm text-secondary-500 mb-4">
                <span>创建时间: ${formatDate(caseData.created_at)}</span>
                <span>ID: ${caseData.id}</span>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center text-sm text-secondary-600">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span>${(caseData.files && caseData.files.length) || 0} 个文件</span>
                </div>

                <div class="flex items-center space-x-2">
                    ${isTrash ? `
                        <button class="restore-btn text-green-600 hover:text-green-800 p-1" title="恢复">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                        <button class="permanent-delete-btn text-red-600 hover:text-red-800 p-1" title="永久删除">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    ` : `
                        <button class="view-btn text-purple-600 hover:text-purple-800 p-1" title="查看案例">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                        <button class="edit-btn text-blue-600 hover:text-blue-800 p-1" title="编辑">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                        <button class="delete-btn text-red-600 hover:text-red-800 p-1" title="删除">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    `}
                </div>
            </div>
        `;

        // 添加事件监听器
        this.attachCaseCardEvents(card, caseData, isTrash);

        return card;
    }

    // 附加案例卡片事件
    attachCaseCardEvents(card, caseData, isTrash) {
        // 点击卡片查看详情
        card.addEventListener('click', (e) => {
            if (!e.target.closest('button')) {
                this.showCaseDetails(caseData);
            }
        });

        if (isTrash) {
            // 恢复按钮
            const restoreBtn = card.querySelector('.restore-btn');
            restoreBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.restoreCase(caseData.id);
            });

            // 永久删除按钮
            const permanentDeleteBtn = card.querySelector('.permanent-delete-btn');
            permanentDeleteBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.permanentlyDeleteCase(caseData.id);
            });
        } else {
            // 查看按钮
            const viewBtn = card.querySelector('.view-btn');
            viewBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showCaseDetails(caseData);
            });

            // 编辑按钮
            const editBtn = card.querySelector('.edit-btn');
            editBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showEditCaseModal(caseData);
            });

            // 删除按钮
            const deleteBtn = card.querySelector('.delete-btn');
            deleteBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteCase(caseData.id);
            });
        }
    }

    // 显示案例详情模态框
    async showCaseDetails(caseData) {
        const modal = document.getElementById('modal-overlay');
        const modalContent = document.getElementById('modal-content');

        modalContent.innerHTML = `
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-secondary-900">${caseData.case_name}</h2>
                <div class="flex items-center space-x-2">
                    <button id="open-case-view" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                        打开案例查看页面
                    </button>
                    <button id="close-modal" class="text-secondary-400 hover:text-secondary-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-2">案例信息</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-medium text-secondary-700">案例ID:</span>
                            <span class="text-secondary-600">${caseData.id}</span>
                        </div>
                        <div>
                            <span class="font-medium text-secondary-700">状态:</span>
                            <span class="text-secondary-600">${caseData.status === 'active' ? '活跃' : '已归档'}</span>
                        </div>
                        <div>
                            <span class="font-medium text-secondary-700">创建时间:</span>
                            <span class="text-secondary-600">${formatDate(caseData.created_at)}</span>
                        </div>
                        <div>
                            <span class="font-medium text-secondary-700">更新时间:</span>
                            <span class="text-secondary-600">${formatDate(caseData.updated_at)}</span>
                        </div>
                    </div>

                    <div class="mt-4">
                        <span class="font-medium text-secondary-700">描述:</span>
                        <p class="text-secondary-600 mt-1">${caseData.description || '暂无描述'}</p>
                    </div>
                </div>

                <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">文件统计</h3>
                        <span class="text-sm text-secondary-500">总计 ${(caseData.files || []).length} 个文件</span>
                    </div>

                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">${(caseData.files || []).length}</div>
                            <div class="text-sm text-blue-600">总文件数</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">${(caseData.files || []).filter(f => f.file_type && f.file_type.startsWith('image/')).length}</div>
                            <div class="text-sm text-green-600">图片文件</div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600">${(caseData.files || []).filter(f => f.file_type && !f.file_type.startsWith('image/')).length}</div>
                            <div class="text-sm text-purple-600">其他文件</div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <p class="text-secondary-500 mb-4">点击上方的"打开案例查看页面"按钮查看所有文件详情</p>
                </div>
            </div>
        `;

        modal.classList.remove('hidden');

        // 添加事件监听器
        document.getElementById('close-modal').addEventListener('click', () => {
            this.hideModal();
        });

        // 打开案例查看页面
        document.getElementById('open-case-view').addEventListener('click', () => {
            this.openCaseViewPage(caseData.id);
        });
    }

    // 显示新建/编辑案例模态框
    showEditCaseModal(caseData = null) {
        const modal = document.getElementById('modal-overlay');
        const content = document.getElementById('modal-content');

        const isEdit = caseData !== null;
        const title = isEdit ? '编辑案例' : '新建案例';

        content.innerHTML = `
            <div class="p-6 border-b border-secondary-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold text-secondary-900">${title}</h2>
                    <button id="close-modal" class="text-secondary-400 hover:text-secondary-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <form id="case-form" class="p-6">
                <div class="mb-4">
                    <label for="case-title" class="block text-sm font-medium text-secondary-700 mb-2">案例标题</label>
                    <input type="text" id="case-title" class="input" value="${isEdit ? caseData.case_name : ''}" required>
                </div>

                <div class="mb-4">
                    <label for="case-description" class="block text-sm font-medium text-secondary-700 mb-2">案例描述</label>
                    <textarea id="case-description" class="input" rows="4" placeholder="请输入案例描述...">${isEdit ? (caseData.description || '') : ''}</textarea>
                </div>

                <div class="mb-6">
                    <label for="case-status" class="block text-sm font-medium text-secondary-700 mb-2">状态</label>
                    <select id="case-status" class="input">
                        <option value="active" ${isEdit && caseData.status === 'active' ? 'selected' : ''}>活跃</option>
                        <option value="archived" ${isEdit && caseData.status === 'archived' ? 'selected' : ''}>已归档</option>
                    </select>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancel-btn" class="btn-secondary">取消</button>
                    <button type="submit" class="btn-primary">
                        ${isEdit ? '保存' : '创建'}
                    </button>
                </div>
            </form>
        `;

        modal.classList.remove('hidden');

        // 添加事件监听器
        document.getElementById('close-modal').addEventListener('click', () => {
            this.hideModal();
        });

        document.getElementById('cancel-btn').addEventListener('click', () => {
            this.hideModal();
        });

        document.getElementById('case-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleCaseSubmit(isEdit ? caseData.id : null);
        });

        // 聚焦标题输入框
        document.getElementById('case-title').focus();
    }

    // 切换文件上传区域
    toggleFileUpload() {
        const uploadArea = document.getElementById('file-upload-area');
        const isHidden = uploadArea.classList.contains('hidden');

        if (isHidden) {
            uploadArea.classList.remove('hidden');
            this.initializeFilePond();
        } else {
            uploadArea.classList.add('hidden');
            this.destroyFilePond();
        }
    }

    // 初始化 FilePond
    initializeFilePond() {
        if (this.filePond) {
            this.destroyFilePond();
        }

        // 注册插件
        FilePond.registerPlugin(
            FilePondPluginFileValidateType,
            FilePondPluginImagePreview,
            FilePondPluginImageResize
        );

        // 创建 FilePond 实例
        this.filePond = FilePond.create(document.getElementById('file-input'), {
            allowMultiple: true,
            maxFiles: 10,
            acceptedFileTypes: ['image/*', 'application/pdf', 'text/*', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            labelIdle: '拖拽文件到这里或者 <span class="filepond--label-action">浏览</span>',
            labelFileProcessing: '上传中...',
            labelFileProcessingComplete: '上传完成',
            labelFileProcessingError: '上传失败',
            server: {
                process: (fieldName, file, metadata, load, error, progress, abort) => {
                    this.uploadFile(file, progress, load, error, abort);
                }
            }
        });
    }

    // 销毁 FilePond
    destroyFilePond() {
        if (this.filePond) {
            this.filePond.destroy();
            this.filePond = null;
        }
    }

    // 文件导入（默认不复制原始文件）
    async uploadFile(file, progress, load, error, abort) {
        try {
            let response;
            let filePath;

            // 🔧 修复：在Electron环境中，优先使用路径导入（不复制文件）
            if (file.path) {
                filePath = file.path;
            } else if (file.webkitRelativePath) {
                filePath = file.webkitRelativePath;
            } else {
                // 如果无法获取文件路径，这可能是真正的Web上传场景
                console.warn(`无法获取文件路径，使用上传模式: ${file.name}`);
                response = await api.uploadAndCopyFile(this.currentCase.id, file);
                showNotification('文件上传成功', 'success');
                load(response.id);
                await this.loadCaseFiles(this.currentCase.id);
                return;
            }

            // 使用路径导入，不复制文件
            console.log(`文件导入（不复制）: ${filePath}`);
            response = await api.importByPath(this.currentCase.id, filePath);
            showNotification('文件导入成功', 'success');

            load(response.id);

            // 重新加载文件列表
            await this.loadCaseFiles(this.currentCase.id);
        } catch (err) {
            error('导入失败');
            console.error('文件导入失败:', err);
        }
    }

    // 加载案例文件
    async loadCaseFiles(caseId) {
        try {
            const files = await api.getCaseFiles(caseId);
            this.renderFilesList(files);
        } catch (error) {
            console.error('加载文件列表失败:', error);
            document.getElementById('files-list').innerHTML = `
                <div class="text-center py-4 text-red-500">
                    <p>加载文件列表失败</p>
                </div>
            `;
        }
    }

    // 渲染文件列表
    renderFilesList(files) {
        const container = document.getElementById('files-list');

        if (files.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-secondary-500">
                    <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p>暂无文件</p>
                </div>
            `;
            return;
        }

        container.innerHTML = files.map(file => `
            <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div class="flex items-center">
                    <span class="text-2xl mr-3">${getFileIcon(file.file_name)}</span>
                    <div>
                        <div class="font-medium text-secondary-900">${file.file_name}</div>
                        <div class="text-sm text-secondary-500">
                            ${file.file_type} • ${formatDate(file.created_at)}
                        </div>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <button class="download-file-btn text-blue-600 hover:text-blue-800 p-1" data-file-id="${file.id}" title="下载">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-4-4V4"></path>
                        </svg>
                    </button>
                    <button class="delete-file-btn text-red-600 hover:text-red-800 p-1" data-file-id="${file.id}" title="删除">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `).join('');

        // 添加文件操作事件监听器
        container.querySelectorAll('.download-file-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.downloadFile(btn.dataset.fileId);
            });
        });

        container.querySelectorAll('.delete-file-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.deleteFile(btn.dataset.fileId);
            });
        });
    }

    // 隐藏模态框
    hideModal() {
        const modal = document.getElementById('modal-overlay');
        modal.classList.add('hidden');
        this.destroyFilePond();
        this.currentCase = null;
    }

    // 处理案例表单提交
    async handleCaseSubmit(caseId) {
        const title = document.getElementById('case-title').value.trim();
        const description = document.getElementById('case-description').value.trim();
        const status = document.getElementById('case-status').value;

        if (!title) {
            showNotification('请输入案例标题', 'error');
            return;
        }

        try {
            const caseData = { case_name: title, description };

            if (caseId) {
                await api.updateCase(caseId, caseData);
                showNotification('案例更新成功', 'success');
            } else {
                await api.createCase(caseData);
                showNotification('案例创建成功', 'success');
            }

            this.hideModal();

            // 触发案例列表刷新（使用await确保完成）
            if (window.app) {
                try {
                    await window.app.loadCases();
                    console.log('✅ 案例列表刷新完成');
                } catch (refreshError) {
                    console.error('❌ 刷新案例列表失败:', refreshError);
                    // 即使刷新失败，也不影响用户体验
                }
            }
        } catch (error) {
            console.error('保存案例失败:', error);
            showNotification('保存案例失败，请检查网络连接', 'error');
        }
    }

    // 删除案例
    async deleteCase(caseId) {
        const confirmed = await this.showConfirmDialog('确定要删除这个案例吗？', '删除后的案例将移入回收站，可以恢复。');

        if (confirmed) {
            try {
                await api.deleteCase(caseId);
                showNotification('案例已移入回收站', 'success');

                // 强制刷新案例列表和回收站
                if (window.app) {
                    await window.app.loadCases();
                    await window.app.loadTrashCases();
                }
            } catch (error) {
                console.error('删除案例失败:', error);
                // 提供更详细的错误信息
                const errorMessage = error.message || error.toString();
                if (errorMessage.includes('404')) {
                    showNotification('案例不存在或已被删除，正在刷新列表', 'warning');
                    // 如果是404错误，强制刷新列表
                    if (window.app) {
                        await window.app.loadCases();
                        await window.app.loadTrashCases();
                    }
                } else {
                    showNotification(`删除案例失败: ${errorMessage}`, 'error');
                }
            }
        }
    }

    // 恢复案例
    async restoreCase(caseId) {
        try {
            const result = await api.restoreCase(caseId);
            if (result.success) {
                showNotification(result.message || '案例已恢复', 'success');
            } else {
                showNotification(result.message || '恢复案例失败', 'error');
            }

            // 强制刷新案例列表和回收站
            if (window.app) {
                await window.app.loadCases(); // 恢复的案例会出现在活跃列表中
                await window.app.loadTrashCases(); // 回收站中会移除该案例
            }
        } catch (error) {
            console.error('恢复案例失败:', error);
            showNotification('恢复案例失败，请检查网络连接', 'error');
        }
    }

    // 永久删除案例
    async permanentlyDeleteCase(caseId) {
        const confirmed = await this.showConfirmDialog('确定要永久删除这个案例吗？', '此操作不可撤销！');

        if (confirmed) {
            try {
                const result = await api.permanentlyDeleteCase(caseId);
                if (result.success) {
                    showNotification(result.message || '案例已永久删除', 'success');
                } else {
                    showNotification(result.message || '永久删除案例失败', 'error');
                }

                // 强制刷新回收站列表
                if (window.app) {
                    await window.app.loadTrashCases();
                }
            } catch (error) {
                console.error('永久删除案例失败:', error);
                showNotification('永久删除案例失败，请检查网络连接', 'error');
            }
        }
    }

    // 下载文件
    async downloadFile(fileId) {
        try {
            const blob = await api.downloadFile(this.currentCase.id, fileId);

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `file_${fileId}`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showNotification('文件下载成功', 'success');
        } catch (error) {
            console.error('下载文件失败:', error);
        }
    }

    // 删除文件
    async deleteFile(fileId) {
        const confirmed = await this.showConfirmDialog('确定要删除这个文件吗？', '此操作不可撤销！');

        if (confirmed) {
            try {
                await api.deleteFile(this.currentCase.id, fileId);
                showNotification('文件已删除', 'success');

                // 重新加载文件列表
                await this.loadCaseFiles(this.currentCase.id);
            } catch (error) {
                console.error('删除文件失败:', error);
            }
        }
    }

    // 显示确认对话框
    async showConfirmDialog(title, message) {
        return new Promise((resolve) => {
            const modal = document.getElementById('modal-overlay');
            const content = document.getElementById('modal-content');

            content.innerHTML = `
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-secondary-900">${title}</h3>
                            <p class="text-sm text-secondary-600 mt-1">${message}</p>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button id="cancel-confirm" class="btn-secondary">取消</button>
                        <button id="confirm-action" class="btn-danger">确定</button>
                    </div>
                </div>
            `;

            modal.classList.remove('hidden');

            document.getElementById('cancel-confirm').addEventListener('click', () => {
                modal.classList.add('hidden');
                resolve(false);
            });

            document.getElementById('confirm-action').addEventListener('click', () => {
                modal.classList.add('hidden');
                resolve(true);
            });
        });
    }

    // 打开案例查看页面
    openCaseViewPage(caseId) {
        // 在新窗口中打开案例查看页面
        const { shell } = require('electron');
        const path = require('path');

        // 构建案例查看页面的URL，使用 caseId 参数保持一致性
        const caseViewUrl = `file://${path.join(__dirname, 'case-view.html')}?caseId=${caseId}`;

        // 使用系统默认浏览器打开，或者可以在同一窗口中导航
        window.location.href = caseViewUrl;
    }

    // ==================== 标签显示组件 ====================

    // 创建标签显示组件
    createTagDisplay(file) {
        if (!file.tags) {
            return '<div class="no-tags">暂无标签信息</div>';
        }

        let html = '<div class="tag-display">';

        // 显示属性标签
        if (file.tags.properties) {
            html += this.createTagSection('属性信息', file.tags.properties, 'properties');
        }

        // 显示标签信息
        if (file.tags.tags) {
            if (file.tags.tags.metadata) {
                html += this.createTagSection('元数据标签', file.tags.tags.metadata, 'metadata');
            }

            if (file.tags.tags.cv) {
                html += this.createTagSection('计算机视觉', file.tags.tags.cv, 'cv');
            }

            if (file.tags.tags.user && file.tags.tags.user.length > 0) {
                html += this.createArrayTagSection('用户标签', file.tags.tags.user, 'user');
            }

            if (file.tags.tags.ai && file.tags.tags.ai.length > 0) {
                html += this.createArrayTagSection('AI标签', file.tags.tags.ai, 'ai');
            }
        }

        html += '</div>';
        return html;
    }

    // 创建标签区域
    createTagSection(title, data, type) {
        let html = `
            <div class="tag-section ${type}">
                <h4 class="tag-section-title">
                    ${this.getTagSectionIcon(type)} ${title}
                </h4>
                <div class="tag-items">
        `;

        Object.entries(data).forEach(([key, value]) => {
            const displayValue = this.formatTagValue(value);
            html += `
                <div class="tag-item">
                    <span class="tag-key">${this.formatTagKey(key)}:</span>
                    <span class="tag-value">${displayValue}</span>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    // 创建数组标签区域
    createArrayTagSection(title, tags, type) {
        let html = `
            <div class="tag-section ${type}">
                <h4 class="tag-section-title">
                    ${this.getTagSectionIcon(type)} ${title}
                </h4>
                <div class="tag-badges">
        `;

        tags.forEach(tag => {
            html += `<span class="tag-badge ${type}">${tag}</span>`;
        });

        html += '</div></div>';
        return html;
    }

    // 获取标签区域图标
    getTagSectionIcon(type) {
        const icons = {
            'properties': '📊',
            'metadata': '📋',
            'cv': '👁️',
            'user': '👤',
            'ai': '🤖'
        };
        return icons[type] || '🏷️';
    }

    // 格式化标签键名
    formatTagKey(key) {
        const keyMap = {
            'filename': '文件名',
            'qualityScore': '质量分数',
            'fileSize': '文件大小',
            'project': '项目',
            'photographer': '摄影师',
            'date': '日期',
            'processedDate': '处理日期',
            'fileType': '文件类型',
            'dimensions': '尺寸',
            'faces': '人脸数量',
            'objects': '识别对象',
            'scene': '场景类型'
        };
        return keyMap[key] || key;
    }

    // 格式化标签值
    formatTagValue(value) {
        if (Array.isArray(value)) {
            return value.join(', ');
        }

        if (typeof value === 'number') {
            // 如果是文件大小，格式化为可读格式
            if (value > 1024 * 1024) {
                return `${(value / (1024 * 1024)).toFixed(2)} MB`;
            } else if (value > 1024) {
                return `${(value / 1024).toFixed(2)} KB`;
            } else {
                return `${value} B`;
            }
        }

        return String(value);
    }

    // 创建标签概览组件（用于文件列表）
    createTagOverview(file) {
        if (!file.tags) {
            return '<span class="no-tags-indicator">无标签</span>';
        }

        const tags = [];

        // 收集主要标签
        if (file.tags.tags) {
            if (file.tags.tags.metadata) {
                if (file.tags.tags.metadata.project) {
                    tags.push(`项目: ${file.tags.tags.metadata.project}`);
                }
                if (file.tags.tags.metadata.photographer) {
                    tags.push(`摄影师: ${file.tags.tags.metadata.photographer}`);
                }
            }

            if (file.tags.tags.ai && file.tags.tags.ai.length > 0) {
                tags.push(`AI: ${file.tags.tags.ai[0]}`);
            }
        }

        if (tags.length === 0) {
            return '<span class="no-tags-indicator">无标签</span>';
        }

        const displayTags = tags.slice(0, 3); // 最多显示3个标签
        const moreCount = tags.length - displayTags.length;

        let html = '<div class="tag-overview">';
        displayTags.forEach(tag => {
            html += `<span class="tag-overview-item">${tag}</span>`;
        });

        if (moreCount > 0) {
            html += `<span class="tag-more">+${moreCount}</span>`;
        }

        html += '</div>';
        return html;
    }

    // 创建标签筛选器组件
    createTagFilter(availableTags) {
        let html = '<div class="tag-filter-component">';

        Object.entries(availableTags).forEach(([category, tags]) => {
            html += `
                <div class="filter-category">
                    <h5>${this.formatTagKey(category)}</h5>
                    <div class="filter-options">
            `;

            tags.forEach(tag => {
                html += `
                    <label class="filter-option">
                        <input type="checkbox" value="${tag}" data-category="${category}">
                        <span>${tag}</span>
                    </label>
                `;
            });

            html += '</div></div>';
        });

        html += '</div>';
        return html;
    }
}

// 创建全局组件管理器实例
window.components = new ComponentManager();

// ==================== 全局通知系统 ====================

/**
 * 显示通知消息
 * @param {string} message - 通知消息
 * @param {string} type - 通知类型: 'success', 'error', 'warning', 'info'
 * @param {number} duration - 显示时长(毫秒)，默认3000ms
 */
function showNotification(message, type = 'info', duration = 3000) {
    // 创建通知容器（如果不存在）
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        `;
        document.body.appendChild(container);
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.style.cssText = `
        background: ${getNotificationColor(type)};
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        margin-bottom: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        pointer-events: auto;
        font-size: 14px;
        max-width: 300px;
        word-wrap: break-word;
    `;

    // 添加图标
    const icon = getNotificationIcon(type);
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <span style="font-size: 16px;">${icon}</span>
            <span>${message}</span>
        </div>
    `;

    container.appendChild(notification);

    // 动画显示
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 10);

    // 自动隐藏
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (container.contains(notification)) {
                container.removeChild(notification);
            }
        }, 300);
    }, duration);

    // 点击关闭
    notification.addEventListener('click', () => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (container.contains(notification)) {
                container.removeChild(notification);
            }
        }, 300);
    });
}

/**
 * 获取通知颜色
 */
function getNotificationColor(type) {
    const colors = {
        'success': '#10b981',
        'error': '#ef4444',
        'warning': '#f59e0b',
        'info': '#3b82f6'
    };
    return colors[type] || colors.info;
}

/**
 * 获取通知图标
 */
function getNotificationIcon(type) {
    const icons = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    };
    return icons[type] || icons.info;
}

// 将showNotification函数设为全局可用
window.showNotification = showNotification;