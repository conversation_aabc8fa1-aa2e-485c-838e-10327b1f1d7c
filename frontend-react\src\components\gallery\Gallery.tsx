// Gallery - Integrated with existing API and state management
// 集成现有API和状态管理的画廊组件

import React, { useState, useMemo } from 'react';
import { Search, Grid3X3, List, Layers, Settings, Upload, ZoomIn, ZoomOut, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu';

// Hooks and utilities
import { useFiles } from '@/hooks/useApi';
import { useUIStore } from '@/store';
import { adaptFileItemsToUIImageItems } from '@/adapters/uiDataAdapters';
import type { UIImageItem } from '@/adapters/uiDataAdapters';

// ============================================================================
// 接口定义
// ============================================================================

interface GalleryProps {
  className?: string;
  isVisible?: boolean;
}

// ============================================================================
// Gallery 组件
// ============================================================================

export const Gallery: React.FC<GalleryProps> = ({
  className,
  isVisible = true,
}) => {
  // ========================================
  // 状态管理
  // ========================================
  const {
    selectedCaseId,
    selectedFileIds,
    toggleFileSelection,
    searchQuery,
    setSearchQuery,
    galleryViewMode,
    galleryZoomLevelNew,
    showImageInfo,
    setGalleryViewMode,
    setGalleryZoomNew,
    toggleShowImageInfo,
  } = useUIStore();

  // 本地状态
  const [isDraggingImage, setIsDraggingImage] = useState(false);

  // ========================================
  // API数据获取
  // ========================================
  const { data: filesData, isLoading: filesLoading } = useFiles(selectedCaseId || undefined);

  // ========================================
  // 计算属性
  // ========================================

  // 转换文件数据为UI格式
  const images: UIImageItem[] = useMemo(() => {
    if (!filesData?.files) return [];
    return adaptFileItemsToUIImageItems(filesData.files);
  }, [filesData]);

  // 选中的图片ID（转换为字符串格式）
  const selectedImages = useMemo(() => {
    return selectedFileIds.map(id => id.toString());
  }, [selectedFileIds]);

  // 过滤后的图片
  const filteredImages = useMemo(() => {
    if (!searchQuery) return images;

    return images.filter(img =>
      img.filename.toLowerCase().includes(searchQuery.toLowerCase()) ||
      img.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [images, searchQuery]);

  // ========================================
  // 事件处理函数
  // ========================================

  const handleImageClick = (imageId: string) => {
    const numericId = parseInt(imageId, 10);
    if (!isNaN(numericId)) {
      toggleFileSelection(numericId);
    }
  };

  const handleFileUpload = () => {
    // TODO: 实现文件上传逻辑
    console.log('上传文件到案例:', selectedCaseId);
  };

  const getImageSize = () => {
    const baseSize = 200;
    const zoomFactor = galleryZoomLevelNew[0] / 5;
    return Math.max(100, Math.min(400, baseSize * zoomFactor));
  };

  const getGridCols = () => {
    const containerWidth = 800; // 假设容器宽度
    const imageSize = getImageSize();
    return Math.max(1, Math.min(10, Math.floor(containerWidth / imageSize)));
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  // ========================================
  // 渲染函数
  // ========================================

  const renderImageGrid = () => {
    const imageSize = getImageSize();

    return (
      <div
        className="grid gap-2 p-4"
        style={{
          gridTemplateColumns: `repeat(auto-fill, minmax(${imageSize}px, 1fr))`,
        }}
      >
        {filteredImages.map((image) => {
          const isSelected = selectedImages.includes(image.id);

          return (
            <div
              key={image.id}
              className={`relative group cursor-pointer rounded-lg overflow-hidden transition-all ${
                isSelected
                  ? 'ring-2 ring-[var(--mizzy-highlight)]'
                  : 'hover:ring-1 hover:ring-[var(--mizzy-border-ui)]'
              }`}
              onClick={() => handleImageClick(image.id)}
              style={{ height: imageSize }}
            >
              {/* 图片 */}
              <img
                src={image.src}
                alt={image.filename}
                className="w-full h-full object-cover"
                loading="lazy"
              />

              {/* 选中状态指示器 */}
              {isSelected && (
                <div className="absolute top-2 right-2 w-6 h-6 bg-[var(--mizzy-highlight)] rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
              )}

              {/* 悬停信息 */}
              {showImageInfo && (
                <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="text-xs truncate">{image.filename}</div>
                  <div className="text-xs text-gray-300">{image.type} • {image.size}</div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  const renderImageList = () => {
    return (
      <div className="p-4 space-y-2">
        {filteredImages.map((image) => {
          const isSelected = selectedImages.includes(image.id);

          return (
            <div
              key={image.id}
              className={`flex items-center gap-4 p-3 rounded-lg cursor-pointer transition-all ${
                isSelected
                  ? 'bg-[var(--mizzy-highlight)] text-white'
                  : 'hover:bg-[var(--mizzy-button)]'
              }`}
              onClick={() => handleImageClick(image.id)}
            >
              {/* 缩略图 */}
              <img
                src={image.src}
                alt={image.filename}
                className="w-16 h-16 object-cover rounded"
                loading="lazy"
              />

              {/* 文件信息 */}
              <div className="flex-1 min-w-0">
                <div className="font-medium truncate" style={{ color: isSelected ? 'white' : 'var(--mizzy-content)' }}>
                  {image.filename}
                </div>
                <div className="text-sm opacity-70">
                  {image.type} • {image.size}
                  {image.width && image.height && ` • ${image.width}×${image.height}`}
                </div>
                {image.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-1">
                    {image.tags.slice(0, 3).map((tag, index) => (
                      <span
                        key={index}
                        className="text-xs px-2 py-1 rounded"
                        style={{
                          background: isSelected ? 'rgba(255,255,255,0.2)' : 'var(--mizzy-input)',
                          color: isSelected ? 'white' : 'var(--mizzy-content)'
                        }}
                      >
                        {tag}
                      </span>
                    ))}
                    {image.tags.length > 3 && (
                      <span className="text-xs opacity-70">+{image.tags.length - 3}</span>
                    )}
                  </div>
                )}
              </div>

              {/* 选中指示器 */}
              {isSelected && (
                <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                  <span className="text-[var(--mizzy-highlight)] text-xs">✓</span>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // ========================================
  // 主渲染
  // ========================================

  if (!isVisible) return null;

  return (
    <div
      className={`h-full flex flex-col ${className || ''}`}
      style={{ background: 'var(--mizzy-gallery)' }}
    >
      {/* 顶部工具栏 */}
      <div className="p-4 border-b" style={{ borderColor: 'var(--mizzy-border-ui)' }}>
        <div className="flex items-center justify-between mb-4">
          {/* 左侧：上传按钮 */}
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={handleFileUpload}
              className="hover:bg-opacity-10"
              style={{ color: 'var(--mizzy-content)' }}
            >
              <Upload className="h-4 w-4 mr-2" />
              上传文件
            </Button>
          </div>

          {/* 右侧：视图控制 */}
          <div className="flex items-center gap-2">
            {/* 视图模式切换 */}
            <div className="flex items-center gap-1 p-1 rounded" style={{ background: 'var(--mizzy-input)' }}>
              <Button
                variant={galleryViewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setGalleryViewMode('grid')}
                className="p-1"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={galleryViewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setGalleryViewMode('list')}
                className="p-1"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            {/* 缩放控制 */}
            {galleryViewMode === 'grid' && (
              <div className="flex items-center gap-2">
                <ZoomOut className="h-4 w-4" style={{ color: 'var(--mizzy-icon)' }} />
                <input
                  type="range"
                  min="1"
                  max="10"
                  value={galleryZoomLevelNew[0]}
                  onChange={(e) => setGalleryZoomNew([parseInt(e.target.value)])}
                  className="w-24"
                />
                <ZoomIn className="h-4 w-4" style={{ color: 'var(--mizzy-icon)' }} />
              </div>
            )}

            {/* 设置菜单 */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="p-1">
                  <Settings className="h-4 w-4" style={{ color: 'var(--mizzy-icon)' }} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuCheckboxItem
                  checked={showImageInfo}
                  onCheckedChange={toggleShowImageInfo}
                >
                  显示图片信息
                </DropdownMenuCheckboxItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setGalleryViewMode('adaptive')}>
                  自适应视图
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setGalleryViewMode('masonry')}>
                  瀑布流视图
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* 搜索栏 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" style={{ color: 'var(--mizzy-icon)' }} />
          <Input
            placeholder="搜索文件..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-10"
            style={{
              background: 'var(--mizzy-input)',
              borderColor: 'var(--mizzy-border-ui)',
              color: 'var(--mizzy-content)'
            }}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1"
            >
              <X className="h-4 w-4" style={{ color: 'var(--mizzy-icon)' }} />
            </Button>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto">
        {filesLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-[var(--mizzy-highlight)] border-t-transparent rounded-full animate-spin mx-auto mb-2" />
              <div style={{ color: 'var(--mizzy-content)' }}>加载中...</div>
            </div>
          </div>
        ) : filteredImages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center" style={{ color: 'var(--mizzy-content)' }}>
              {searchQuery ? '未找到匹配的文件' : '暂无文件'}
            </div>
          </div>
        ) : galleryViewMode === 'list' ? (
          renderImageList()
        ) : (
          renderImageGrid()
        )}
      </div>

      {/* 底部状态栏 */}
      <div className="p-2 border-t text-sm" style={{
        borderColor: 'var(--mizzy-border-ui)',
        color: 'var(--mizzy-icon)'
      }}>
        <div className="flex justify-between items-center">
          <span>
            {filteredImages.length} 个文件
            {selectedImages.length > 0 && ` • ${selectedImages.length} 个已选中`}
          </span>
          <span>
            {galleryViewMode === 'grid' ? '网格视图' : galleryViewMode === 'list' ? '列表视图' : galleryViewMode}
          </span>
        </div>
      </div>
    </div>
  );
};
