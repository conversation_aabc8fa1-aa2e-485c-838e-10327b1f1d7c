# 测试脚本和数据

## 概述
本目录包含PostgreSQL升级项目的测试脚本、测试数据和测试报告。

## 目录结构
- `unit/` - 单元测试
- `integration/` - 集成测试
- `performance/` - 性能测试
- `data/` - 测试数据
- `reports/` - 测试报告

## 测试类型

### 单元测试
- 数据库模型测试
- 查询函数测试
- 数据验证测试

### 集成测试
- API端到端测试
- 数据库连接测试
- 功能完整性测试

### 性能测试
- 查询性能基准测试
- 并发压力测试
- 大数据量测试
- 内存和CPU使用率测试

## 测试数据
- 模拟照片数据
- 标签数据集
- EXIF元数据样本
- 性能测试数据集

## 运行测试
```bash
# 运行所有测试
python -m pytest tests/

# 运行单元测试
python -m pytest tests/unit/

# 运行性能测试
python -m pytest tests/performance/

# 生成测试报告
python -m pytest tests/ --html=reports/test_report.html
```

## 测试报告
- 功能测试结果
- 性能基准报告
- 压力测试结果
- 测试覆盖率报告
