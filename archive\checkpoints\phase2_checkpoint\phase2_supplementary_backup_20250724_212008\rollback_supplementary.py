#!/usr/bin/env python3
# 第二阶段补充清理回滚脚本
# 备份时间: 2025-07-24T21:20:08.741009

import shutil
from pathlib import Path

def rollback_supplementary():
    print("🔄 开始回滚第二阶段补充清理...")
    
    project_root = Path(__file__).parent.parent.parent
    backend_root = project_root / "backend"
    backup_file = Path(__file__).parent / "database_async.py"
    target_file = backend_root / "src" / "database_async.py"
    
    try:
        if backup_file.exists():
            shutil.copy2(backup_file, target_file)
            print("✅ 恢复database_async.py")
            print("🎉 第二阶段补充清理回滚完成！")
            return True
        else:
            print("❌ 备份文件不存在")
            return False
    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        return False

if __name__ == "__main__":
    rollback_supplementary()
