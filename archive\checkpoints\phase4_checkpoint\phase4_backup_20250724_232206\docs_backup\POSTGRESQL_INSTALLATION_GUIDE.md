# 🐘 PostgreSQL安装指南 - Windows

## 📋 安装概述

本指南将帮助您在Windows系统上安装PostgreSQL，并为mizzy_star项目配置数据库。

## 🚀 步骤1: 下载PostgreSQL

### 1.1 访问官方下载页面
- 官方下载页面已在浏览器中打开: https://www.postgresql.org/download/windows/
- 点击 **"Download the installer"** 按钮

### 1.2 选择版本
- **推荐版本**: PostgreSQL 15.x 或 16.x (最新稳定版)
- **架构**: 选择 x86-64 (64位)
- **操作系统**: Windows x86-64

### 1.3 下载安装程序
- 文件名类似: `postgresql-16.1-1-windows-x64.exe`
- 文件大小约: 350-400MB

## 🔧 步骤2: 安装PostgreSQL

### 2.1 运行安装程序
1. 双击下载的安装程序
2. 如果出现UAC提示，点击"是"

### 2.2 安装向导配置

#### 欢迎页面
- 点击 **"Next"**

#### 安装目录
- 默认路径: `C:\Program Files\PostgreSQL\16`
- **建议**: 保持默认路径
- 点击 **"Next"**

#### 选择组件
**必选组件** (保持勾选):
- ✅ PostgreSQL Server
- ✅ pgAdmin 4 (图形管理工具)
- ✅ Stack Builder (扩展管理)
- ✅ Command Line Tools

点击 **"Next"**

#### 数据目录
- 默认路径: `C:\Program Files\PostgreSQL\16\data`
- **建议**: 保持默认路径
- 点击 **"Next"**

#### 设置超级用户密码
- 用户名: `postgres` (默认)
- **密码**: 请设置一个强密码，例如: `mizzy_star_2025`
- **⚠️ 重要**: 请记住这个密码，后续配置需要使用
- 点击 **"Next"**

#### 端口配置
- 默认端口: `5432`
- **建议**: 保持默认端口
- 点击 **"Next"**

#### 区域设置
- 默认区域: `[Default locale]`
- **建议**: 保持默认设置
- 点击 **"Next"**

#### 安装确认
- 检查安装摘要
- 点击 **"Next"** 开始安装

### 2.3 等待安装完成
- 安装过程需要5-10分钟
- 安装完成后，点击 **"Finish"**

## ✅ 步骤3: 验证安装

### 3.1 检查服务状态
1. 按 `Win + R`，输入 `services.msc`
2. 查找 `postgresql-x64-16` 服务
3. 确认状态为 **"正在运行"**

### 3.2 测试连接
打开命令提示符或PowerShell，运行：

```bash
# 测试PostgreSQL是否安装成功
psql --version

# 连接到PostgreSQL (会提示输入密码)
psql -U postgres -h localhost
```

如果成功连接，您会看到PostgreSQL提示符：
```
postgres=#
```

输入 `\q` 退出。

## 🔧 步骤4: 为mizzy_star创建数据库

### 4.1 连接到PostgreSQL
```bash
psql -U postgres -h localhost
```

### 4.2 创建数据库和用户
```sql
-- 创建mizzy_star数据库
CREATE DATABASE mizzy_star_db;

-- 创建专用用户 (可选，推荐)
CREATE USER mizzy_user WITH PASSWORD 'mizzy_star_2025';

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE mizzy_star_db TO mizzy_user;

-- 退出
\q
```

### 4.3 安装pgvector扩展 (AI功能支持)
```bash
# 连接到mizzy_star数据库
psql -U postgres -d mizzy_star_db

# 安装vector扩展
CREATE EXTENSION IF NOT EXISTS vector;

# 验证安装
\dx

# 退出
\q
```

## 🎯 步骤5: 配置mizzy_star项目

### 5.1 设置环境变量
在项目根目录创建 `.env` 文件：

```bash
# PostgreSQL配置
USE_POSTGRESQL=true
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=mizzy_star_2025
POSTGRES_DB=mizzy_star_db

# 或者使用专用用户
# POSTGRES_USER=mizzy_user
# POSTGRES_PASSWORD=mizzy_star_2025
```

### 5.2 安装Python依赖
```bash
cd backend
pip install psycopg2-binary
pip install pgvector
```

### 5.3 初始化数据库
```bash
cd backend
python init_database.py --reset
```

### 5.4 运行测试
```bash
python test_postgresql_migration.py
```

## 🛠️ 常见问题解决

### 问题1: 端口被占用
**错误**: `could not bind IPv4 address "127.0.0.1": Address already in use`

**解决方案**:
1. 检查端口占用: `netstat -an | findstr :5432`
2. 更改PostgreSQL端口或停止占用端口的程序

### 问题2: 密码认证失败
**错误**: `password authentication failed`

**解决方案**:
1. 确认密码正确
2. 检查 `pg_hba.conf` 文件配置
3. 重启PostgreSQL服务

### 问题3: psql命令不存在
**错误**: `'psql' is not recognized as an internal or external command`

**解决方案**:
1. 将PostgreSQL bin目录添加到PATH环境变量
2. 路径通常为: `C:\Program Files\PostgreSQL\16\bin`

### 问题4: pgvector扩展安装失败
**错误**: `extension "vector" is not available`

**解决方案**:
1. 下载pgvector扩展
2. 或使用Docker PostgreSQL镜像

## 📊 安装验证清单

安装完成后，请验证以下项目：

- [ ] PostgreSQL服务正在运行
- [ ] 可以使用psql连接数据库
- [ ] mizzy_star_db数据库已创建
- [ ] pgvector扩展已安装
- [ ] Python可以连接PostgreSQL
- [ ] mizzy_star项目测试通过

## 🎉 完成

PostgreSQL安装完成后，您的mizzy_star项目将能够：

1. **高性能数据库**: 使用PostgreSQL替代SQLite
2. **向量搜索**: 支持AI图像相似性搜索
3. **并发处理**: 支持多用户同时访问
4. **数据完整性**: 完整的ACID事务支持
5. **扩展性**: 支持大规模数据存储

## 📞 技术支持

如果在安装过程中遇到问题，请：

1. 检查PostgreSQL官方文档
2. 查看Windows事件日志
3. 检查防火墙设置
4. 确认系统权限

**下一步**: 安装完成后，运行 `python init_database.py --reset` 初始化数据库。
