version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: mizzy_postgres
    environment:
      POSTGRES_DB: mizzy_main
      POSTGRES_USER: mizzy_user
      POSTGRES_PASSWORD: MizzyStarProd2024!
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgresql.conf:/etc/postgresql/postgresql.conf
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c log_statement=all
      -c log_min_duration_statement=100
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mizzy_user -d mizzy_main"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - mizzy_network

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: mizzy_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - mizzy_network
    profiles:
      - admin

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  mizzy_network:
    driver: bridge
