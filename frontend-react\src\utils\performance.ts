// Performance Utilities - Memory and performance optimization utilities
// 性能和内存优化工具

import { useCallback, useEffect, useRef, useMemo } from 'react';

// ============================================================================
// 内存管理工具
// ============================================================================

/**
 * 图片缓存管理器
 */
class ImageCacheManager {
  private cache = new Map<string, HTMLImageElement>();
  private maxCacheSize: number;
  private accessOrder: string[] = [];

  constructor(maxCacheSize: number = 100) {
    this.maxCacheSize = maxCacheSize;
  }

  /**
   * 获取缓存的图片
   */
  get(src: string): HTMLImageElement | undefined {
    const img = this.cache.get(src);
    if (img) {
      // 更新访问顺序
      this.updateAccessOrder(src);
    }
    return img;
  }

  /**
   * 缓存图片
   */
  set(src: string, img: HTMLImageElement): void {
    // 如果缓存已满，移除最久未访问的图片
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLeastRecentlyUsed();
    }

    this.cache.set(src, img);
    this.updateAccessOrder(src);
  }

  /**
   * 更新访问顺序
   */
  private updateAccessOrder(src: string): void {
    const index = this.accessOrder.indexOf(src);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
    this.accessOrder.push(src);
  }

  /**
   * 移除最久未访问的图片
   */
  private evictLeastRecentlyUsed(): void {
    if (this.accessOrder.length > 0) {
      const lruSrc = this.accessOrder.shift()!;
      this.cache.delete(lruSrc);
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.accessOrder = [];
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      usage: (this.cache.size / this.maxCacheSize) * 100,
    };
  }
}

// 全局图片缓存实例
export const imageCache = new ImageCacheManager(200);

// ============================================================================
// 性能监控工具
// ============================================================================

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();

  /**
   * 记录性能指标
   */
  record(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // 只保留最近100个值
    if (values.length > 100) {
      values.shift();
    }
  }

  /**
   * 获取性能统计
   */
  getStats(name: string) {
    const values = this.metrics.get(name) || [];
    if (values.length === 0) {
      return { avg: 0, min: 0, max: 0, count: 0 };
    }

    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return { avg, min, max, count: values.length };
  }

  /**
   * 获取所有指标
   */
  getAllStats() {
    const stats: Record<string, any> = {};
    for (const [name] of this.metrics) {
      stats[name] = this.getStats(name);
    }
    return stats;
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// ============================================================================
// React Hooks
// ============================================================================

/**
 * 防抖Hook
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * 节流Hook
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
}

/**
 * 内存使用监控Hook
 */
export function useMemoryMonitor() {
  const [memoryInfo, setMemoryInfo] = React.useState<any>(null);

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMemoryInfo({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
          usage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
        });
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 5000); // 每5秒更新一次

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
}

/**
 * 渲染性能监控Hook
 */
export function useRenderPerformance(componentName: string) {
  const renderStart = useRef<number>(0);

  useEffect(() => {
    renderStart.current = performance.now();
  });

  useEffect(() => {
    const renderTime = performance.now() - renderStart.current;
    performanceMonitor.record(`${componentName}_render_time`, renderTime);
  });
}

/**
 * 虚拟化列表优化Hook
 */
export function useVirtualization(
  itemCount: number,
  itemHeight: number,
  containerHeight: number,
  scrollTop: number
) {
  return useMemo(() => {
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(startIndex + visibleCount + 1, itemCount - 1);
    const offsetY = startIndex * itemHeight;

    return {
      startIndex: Math.max(0, startIndex),
      endIndex,
      visibleCount,
      offsetY,
    };
  }, [itemCount, itemHeight, containerHeight, scrollTop]);
}

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * 格式化内存使用
 */
export function formatMemoryUsage(bytes: number): string {
  return formatFileSize(bytes);
}

/**
 * 检查是否为移动设备
 */
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
}

/**
 * 获取设备性能等级
 */
export function getDevicePerformanceLevel(): 'low' | 'medium' | 'high' {
  const hardwareConcurrency = navigator.hardwareConcurrency || 1;
  const memory = (navigator as any).deviceMemory || 1;

  if (hardwareConcurrency >= 8 && memory >= 8) {
    return 'high';
  } else if (hardwareConcurrency >= 4 && memory >= 4) {
    return 'medium';
  } else {
    return 'low';
  }
}

// ============================================================================
// 导出
// ============================================================================

export default {
  imageCache,
  performanceMonitor,
  useDebounce,
  useThrottle,
  useMemoryMonitor,
  useRenderPerformance,
  useVirtualization,
  formatFileSize,
  formatMemoryUsage,
  isMobileDevice,
  getDevicePerformanceLevel,
};
