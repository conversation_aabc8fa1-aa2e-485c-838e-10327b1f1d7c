{"name": "mizzy-star-frontend", "version": "0.3.0", "description": "案例管理系统前端应用", "main": "src/main.js", "scripts": {"dev": "concurrently \"npm run build:css:watch\" \"electron .\"", "build": "npm run build:css && npm run build:electron", "build:css": "tailwindcss -i ./src/styles/input.css -o ./src/styles/output.css", "build:css:watch": "tailwindcss -i ./src/styles/input.css -o ./src/styles/output.css --watch", "build:electron": "electron-builder", "start": "electron .", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "case-management", "file-management"], "author": "Mizzy Star Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "electron": "^27.0.0", "electron-builder": "^24.6.4", "tailwindcss": "^3.3.5"}, "dependencies": {"axios": "^1.5.0", "filepond": "^4.30.4", "filepond-plugin-file-validate-type": "^1.2.8", "filepond-plugin-image-preview": "^4.6.11", "filepond-plugin-image-resize": "^2.0.10", "date-fns": "^2.30.0", "lucide": "^0.292.0"}, "build": {"appId": "com.mizzystar.casemanagement", "productName": "Mizzy Star Case Management", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}