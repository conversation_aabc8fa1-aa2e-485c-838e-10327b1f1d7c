/* 标签显示组件样式 */

/* 标签显示主容器 */
.tag-display {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 标签区域 */
.tag-section {
    margin-bottom: 24px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.tag-section:last-child {
    margin-bottom: 0;
}

/* 标签区域标题 */
.tag-section-title {
    background: #f8f9fa;
    padding: 12px 16px;
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 不同类型的标签区域颜色 */
.tag-section.properties .tag-section-title {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    color: #1976d2;
}

.tag-section.metadata .tag-section-title {
    background: linear-gradient(135deg, #f3e5f5 0%, #e8f5e8 100%);
    color: #388e3c;
}

.tag-section.cv .tag-section-title {
    background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
    color: #f57c00;
}

.tag-section.user .tag-section-title {
    background: linear-gradient(135deg, #e8eaf6 0%, #f3e5f5 100%);
    color: #5e35b1;
}

.tag-section.ai .tag-section-title {
    background: linear-gradient(135deg, #e0f2f1 0%, #e8f5e8 100%);
    color: #00695c;
}

/* 标签项目 */
.tag-items {
    padding: 16px;
}

.tag-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.tag-item:last-child {
    border-bottom: none;
}

.tag-key {
    font-weight: 500;
    color: #6c757d;
    font-size: 13px;
    min-width: 100px;
    flex-shrink: 0;
}

.tag-value {
    color: #495057;
    font-size: 13px;
    text-align: right;
    word-break: break-word;
}

/* 标签徽章 */
.tag-badges {
    padding: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.tag-badge.user {
    background: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.tag-badge.ai {
    background: #e8f5e8;
    color: #388e3c;
    border: 1px solid #c8e6c9;
}

/* 标签概览组件（用于文件列表） */
.tag-overview {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;
}

.tag-overview-item {
    background: #f8f9fa;
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    border: 1px solid #e9ecef;
}

.tag-more {
    background: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 500;
}

.no-tags-indicator {
    color: #6c757d;
    font-style: italic;
    font-size: 12px;
}

/* 无标签状态 */
.no-tags {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-style: italic;
}

/* 标签筛选器组件 */
.tag-filter-component {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
}

.filter-category {
    margin-bottom: 16px;
}

.filter-category:last-child {
    margin-bottom: 0;
}

.filter-category h5 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px solid #e9ecef;
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-option:hover {
    background: #f8f9fa;
    border-color: #007bff;
}

.filter-option input[type="checkbox"] {
    margin: 0;
    width: 12px;
    height: 12px;
}

.filter-option input[type="checkbox"]:checked + span {
    color: #007bff;
    font-weight: 500;
}

/* 文件详情模态框中的标签显示 */
.modal .tag-display {
    margin-top: 20px;
    box-shadow: none;
    border: 1px solid #e9ecef;
}

.file-detail-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 20px;
    align-items: start;
}

.file-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.file-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-info-detail {
    flex: 1;
}

.file-info-detail h4 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 18px;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.detail-item {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
}

.detail-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
    font-weight: 500;
}

.detail-value {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

/* 标签详情样式 */
.tag-section h5 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.tag-section .tag-items .tag-item {
    padding: 6px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tag-section .tag-badges {
    padding: 0;
    margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .file-detail-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .file-preview {
        order: 2;
    }
    
    .file-info-detail {
        order: 1;
    }
    
    .detail-grid {
        grid-template-columns: 1fr;
    }
    
    .tag-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .tag-key {
        min-width: auto;
    }
    
    .tag-value {
        text-align: left;
    }
}

/* 动画效果 */
.tag-section {
    transition: all 0.3s ease;
}

.tag-section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tag-badge {
    transition: all 0.2s ease;
}

.tag-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.tag-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
}

.tag-loading .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
