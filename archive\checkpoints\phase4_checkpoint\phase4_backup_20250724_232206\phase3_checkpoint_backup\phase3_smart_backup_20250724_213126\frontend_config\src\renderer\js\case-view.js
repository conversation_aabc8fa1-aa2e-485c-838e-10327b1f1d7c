// case-view.js - 案例查看页面逻辑

class CaseViewer {
    constructor() {
        this.caseId = this.getCaseIdFromUrl();

        // 如果没有有效的案例ID，重定向到主页
        if (!this.caseId) {
            console.error('无效的案例ID，重定向到主页');
            window.location.href = 'index.html';
            return;
        }

        this.currentCase = null;
        this.files = [];
        this.filteredFiles = [];
        this.filePond = null; // FilePond 实例

        // 批量选择相关状态
        this.batchMode = false;
        this.selectedFiles = new Set();

        this.initializeElements();
        this.checkElements(); // 调试用
        this.setupEventListeners();
        this.initializeFilePond();
        this.loadCaseData();
    }

    getCaseIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        // 优先使用 caseId 参数，如果没有则尝试 id 参数（向后兼容）
        let caseId = parseInt(urlParams.get('caseId'));
        if (!caseId || isNaN(caseId)) {
            caseId = parseInt(urlParams.get('id'));
        }

        // 如果没有提供案例ID，返回null而不是默认值
        if (!caseId || isNaN(caseId)) {
            console.warn('未提供有效的案例ID，将重定向到主页');
            return null;
        }

        return caseId;
    }

    initializeElements() {
        this.elements = {
            error: document.getElementById('error'),
            errorMessage: document.getElementById('error-message'),
            emptyState: document.getElementById('empty-state'),
            imageGrid: document.getElementById('image-grid'),
            caseName: document.getElementById('case-name'),
            totalFiles: document.getElementById('total-files'),
            imageFiles: document.getElementById('image-files'),
            caseStatus: document.getElementById('case-status'),
            sortSelect: document.getElementById('sort-select'),
            searchInput: document.getElementById('search-input'),
            refreshBtn: document.getElementById('refresh-btn'),
            importFilesBtn: document.getElementById('import-files-btn'),
            batchImportBtn: document.getElementById('batch-import-btn'),
            batchImportModal: document.getElementById('batch-import-modal'),
            batchImportForm: document.getElementById('batch-import-form'),
            cancelImportBtn: document.getElementById('cancel-import'),
            closeBatchModalBtn: document.getElementById('close-batch-modal'),
            importModal: document.getElementById('import-modal'),
            closeImportModalBtn: document.getElementById('close-import-modal'),
            cancelFileImportBtn: document.getElementById('cancel-file-import'),
            startFileImportBtn: document.getElementById('start-file-import'),
            uploadProgressDiv: document.getElementById('upload-progress'),
            progressBar: document.getElementById('progress-bar'),
            progressText: document.getElementById('progress-text'),
            uploadFirstBtn: document.getElementById('upload-first-btn'),
            imageModal: document.getElementById('image-modal'),
            modalImage: document.getElementById('modal-image'),
            modalInfo: document.getElementById('modal-info'),
            // 批量选择相关元素
            batchSelectBtn: document.getElementById('batch-select-btn'),
            batchActions: document.getElementById('batch-actions'),
            selectedCount: document.getElementById('selected-count'),
            selectAllBtn: document.getElementById('select-all-btn'),
            deselectAllBtn: document.getElementById('deselect-all-btn'),
            batchQualityAnalysisBtn: document.getElementById('batch-quality-analysis-btn'),
            exitBatchModeBtn: document.getElementById('exit-batch-mode-btn'),
            // 批量操作相关元素
            setCoverBtn: document.getElementById('set-cover-btn'),
            batchDeleteBtn: document.getElementById('batch-delete-btn'),
            // 封面菜单相关元素（可能不存在）
            coverMenuBtn: document.getElementById('cover-menu-btn'),
            coverMenu: document.getElementById('cover-menu')
        };
    }

    checkElements() {
        // 调试：检查关键元素是否存在
        const criticalElements = ['loading', 'emptyState', 'imageGrid', 'caseName', 'totalFiles', 'imageFiles'];
        criticalElements.forEach(elementName => {
            if (!this.elements[elementName]) {
                console.error(`找不到元素: ${elementName}`);
            } else {
                console.log(`元素正常: ${elementName}`);
            }
        });
    }

    setupEventListeners() {
        // 刷新按钮
        this.elements.refreshBtn.addEventListener('click', () => {
            this.loadCaseData();
        });

        // 顶部质量分析按钮已移除，功能整合到批量选择模式中



        // 标签管理按钮
        const tagManagementBtn = document.getElementById('tag-management-btn');
        if (tagManagementBtn) {
            tagManagementBtn.addEventListener('click', () => {
                this.openTagManagement();
            });
        }

        // 回收站按钮
        const trashBtn = document.getElementById('trash-btn');
        if (trashBtn) {
            trashBtn.addEventListener('click', () => {
                this.openTrashModal();
            });
        }

        // 批量选择相关事件
        if (this.elements.batchSelectBtn) {
            this.elements.batchSelectBtn.addEventListener('click', () => {
                this.toggleBatchMode();
            });
        }

        if (this.elements.selectAllBtn) {
            this.elements.selectAllBtn.addEventListener('click', () => {
                this.selectAllImages();
            });
        }

        if (this.elements.deselectAllBtn) {
            this.elements.deselectAllBtn.addEventListener('click', () => {
                this.deselectAllImages();
            });
        }

        if (this.elements.batchQualityAnalysisBtn) {
            this.elements.batchQualityAnalysisBtn.addEventListener('click', () => {
                this.startBatchQualityAnalysis();
            });
        }

        if (this.elements.exitBatchModeBtn) {
            this.elements.exitBatchModeBtn.addEventListener('click', () => {
                this.exitBatchMode();
            });
        }

        // 文件导入按钮
        this.elements.importFilesBtn.addEventListener('click', () => {
            this.showImportModal();
        });

        // 批量导入按钮
        this.elements.batchImportBtn.addEventListener('click', () => {
            this.showBatchImportModal();
        });

        // "上传第一张图片"按钮
        if (this.elements.uploadFirstBtn) {
            this.elements.uploadFirstBtn.addEventListener('click', () => {
                this.showImportModal();
            });
        }

        // 搜索输入
        this.elements.searchInput.addEventListener('input', (e) => {
            this.filterFiles(e.target.value);
        });

        // 排序选择
        this.elements.sortSelect.addEventListener('change', (e) => {
            this.sortFiles(e.target.value);
        });

        // 批量导入表单
        this.elements.batchImportForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleBatchImport();
        });

        // 取消导入
        this.elements.cancelImportBtn.addEventListener('click', () => {
            this.hideBatchImportModal();
        });

        // 关闭批量导入模态框
        this.elements.closeBatchModalBtn.addEventListener('click', () => {
            this.hideBatchImportModal();
        });

        // 文件导入模态框相关事件
        this.elements.closeImportModalBtn.addEventListener('click', () => {
            this.hideImportModal();
        });

        this.elements.cancelFileImportBtn.addEventListener('click', () => {
            this.hideImportModal();
        });

        this.elements.startFileImportBtn.addEventListener('click', () => {
            this.handleFileImport();
        });

        // 回收站模态框相关事件
        const closeTrashModalBtn = document.getElementById('close-trash-modal');
        const closeTrashModalBtnBottom = document.getElementById('close-trash-modal-btn');
        const restoreAllBtn = document.getElementById('restore-all-btn');
        const emptyTrashBtn = document.getElementById('empty-trash-btn');

        if (closeTrashModalBtn) {
            closeTrashModalBtn.addEventListener('click', () => {
                this.hideTrashModal();
            });
        }

        if (closeTrashModalBtnBottom) {
            closeTrashModalBtnBottom.addEventListener('click', () => {
                this.hideTrashModal();
            });
        }

        if (restoreAllBtn) {
            restoreAllBtn.addEventListener('click', () => {
                this.restoreAllFiles();
            });
        }

        if (emptyTrashBtn) {
            emptyTrashBtn.addEventListener('click', () => {
                this.emptyTrash();
            });
        }

        // 模态框关闭
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });

        // 封面管理相关事件
        this.setupCoverManagementEvents();
    }

    setupCoverManagementEvents() {
        // 封面菜单按钮
        if (this.elements.coverMenuBtn) {
            this.elements.coverMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleCoverMenu();
            });
        }

        // 封面管理功能已整合到批量选择模式中

        // 设置封面按钮（批量选择模式）
        if (this.elements.setCoverBtn) {
            this.elements.setCoverBtn.addEventListener('click', () => {
                this.setCoverFromSelected();
            });
        }

        // 批量删除按钮
        if (this.elements.batchDeleteBtn) {
            this.elements.batchDeleteBtn.addEventListener('click', () => {
                this.batchDeleteFiles();
            });
        }

        // 点击其他地方关闭封面菜单
        document.addEventListener('click', (e) => {
            if (this.elements.coverMenuBtn && this.elements.coverMenu &&
                !this.elements.coverMenuBtn.contains(e.target) &&
                !this.elements.coverMenu.contains(e.target)) {
                this.hideCoverMenu();
            }
        });
    }

    async loadCaseData() {
        try {
            // 加载案例基本信息
            const caseResponse = await api.getCase(this.caseId);
            this.currentCase = caseResponse;

            // 更新案例信息显示
            this.updateCaseInfo();

            // 加载文件列表
            this.files = this.currentCase.files || [];
            this.filteredFiles = [...this.files];

            // 渲染文件网格
            this.renderFileGrid();

        } catch (error) {
            console.error('加载案例数据失败:', error);

            // 如果是404错误（案例不存在），重定向到主页
            if (error.message && error.message.includes('404')) {
                console.error(`案例 ${this.caseId} 不存在，重定向到主页`);
                alert(`案例 ${this.caseId} 不存在，将返回主页`);
                window.location.href = 'index.html';
                return;
            }

            this.showError('加载案例数据失败: ' + error.message);
        }
    }

    updateCaseInfo() {
        if (!this.currentCase) {
            console.error('updateCaseInfo: currentCase is null');
            return;
        }

        console.log('updateCaseInfo: 更新案例信息', this.currentCase);

        this.elements.caseName.textContent = this.currentCase.case_name;

        // 确保使用最新的文件列表
        const fileList = this.currentCase.files || [];
        const imageCount = fileList.filter(f =>
            f.file_type && f.file_type.startsWith('image/')).length;

        console.log(`文件统计: 总文件 ${fileList.length}, 图片文件 ${imageCount}`);

        this.elements.totalFiles.textContent = fileList.length;
        this.elements.imageFiles.textContent = imageCount;

        // 更新质量分析统计
        const analyzedImages = fileList.filter(file =>
            file.file_type && file.file_type.startsWith('image/') && file.quality_score !== null
        ).length;
        const analyzedImagesElement = document.getElementById('analyzed-images');
        if (analyzedImagesElement) {
            analyzedImagesElement.textContent = analyzedImages;
        }

        // 计算相似性群组数量
        const groups = new Set(
            fileList
                .filter(file => file.cluster_id !== null)
                .map(file => file.cluster_id)
        );
        const similarityGroupsElement = document.getElementById('similarity-groups');
        if (similarityGroupsElement) {
            similarityGroupsElement.textContent = groups.size;
        }

        const statusText = this.currentCase.status === 'active' ? '活跃' : '已删除';
        const statusClass = this.currentCase.status === 'active' ? 'text-green-600' : 'text-red-600';
        this.elements.caseStatus.textContent = statusText;
        this.elements.caseStatus.className = `font-semibold ${statusClass}`;

        // 更新封面状态
        this.updateCoverStatus();
    }

    updateCoverStatus() {
        if (!this.currentCase) return;

        const coverStatus = api.getCoverStatus(this.currentCase);
        if (!coverStatus) return;

        // 更新封面类型标识
        if (this.elements.coverTypeBadge) {
            let badgeText = '';
            let badgeClass = '';

            switch (coverStatus.type) {
                case 'manual':
                    badgeText = '手动';
                    badgeClass = 'bg-blue-500 text-white';
                    break;
                case 'automatic':
                    badgeText = '自动';
                    badgeClass = 'bg-green-500 text-white';
                    break;
                case 'placeholder':
                default:
                    badgeText = '占位';
                    badgeClass = 'bg-gray-500 text-white';
                    break;
            }

            if (coverStatus.needsAttention) {
                badgeText += ' ⚠️';
                badgeClass = 'bg-orange-500 text-white';
            }

            this.elements.coverTypeBadge.textContent = badgeText;
            this.elements.coverTypeBadge.className = `px-2 py-1 rounded-full text-xs ${badgeClass}`;
        }

        // 更新封面管理按钮状态
        this.updateCoverMenuButtons(coverStatus);
    }

    updateCoverMenuButtons(coverStatus) {
        if (!coverStatus) return;

        // 封面管理功能已整合到批量选择模式中
    }

    renderFileGrid() {
        console.log(`renderFileGrid: 渲染 ${this.filteredFiles.length} 个文件`);

        if (this.filteredFiles.length === 0) {
            console.log('renderFileGrid: 显示空状态');
            this.showEmptyState();
            return;
        }

        this.hideEmptyState();
        this.elements.imageGrid.innerHTML = '';

        this.filteredFiles.forEach(file => {
            const card = this.createFileCard(file);
            this.elements.imageGrid.appendChild(card);
        });

        this.elements.imageGrid.classList.remove('hidden');
    }

    createFileCard(file) {
        const card = document.createElement('div');
        card.className = 'image-card';

        // 添加调试信息
        console.log('创建文件卡片:', {
            id: file.id,
            file_name: file.file_name,
            file_path: file.file_path,
            thumbnail_small_path: file.thumbnail_small_path
        });

        const imageUrl = this.getImageUrl(file);
        const formattedDate = this.formatDate(file.created_at);
        const fileSize = this.formatFileSize(file);

        // 生成质量分析标签
        const qualityBadge = this.getQualityBadge(file);

        // 生成封面源标识
        const coverBadge = this.getCoverBadge(file);

        // 生成选择框（批量模式下显示）
        const selectCheckbox = this.batchMode ? `
            <div class="absolute top-2 left-2">
                <input type="checkbox" class="file-checkbox w-5 h-5 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"
                       data-file-id="${file.id}" ${this.selectedFiles.has(file.id) ? 'checked' : ''}>
            </div>
        ` : '';

        card.innerHTML = `
            <div class="relative">
                <img src="${imageUrl}" alt="${file.file_name}" class="image-preview"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik02MCA2MEgxNDBWMTQwSDYwVjYwWiIgZmlsbD0iI0Q1RDlERiIvPgo8Y2lyY2xlIGN4PSI4NSIgY3k9Ijg1IiByPSI4IiBmaWxsPSIjOUI5Q0EwIi8+CjxwYXRoIGQ9Ik02MCA2MEgxNDBWMTQwSDYwVjYwWiIgc3Ryb2tlPSIjOUI5Q0EwIiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+'>
                ${selectCheckbox}
                ${qualityBadge}
                ${coverBadge}
            </div>
            <div class="image-info">
                <div class="image-name" title="${file.file_name}">${file.file_name}</div>
                <div class="image-meta">
                    <div>${file.width || 0} × ${file.height || 0}</div>
                    <div>${formattedDate}</div>
                    <div>${file.file_type || '未知类型'}</div>
                    ${this.getQualityInfo(file)}
                </div>
            </div>
        `;

        // 点击预览
        card.querySelector('.image-preview').addEventListener('click', () => {
            if (!this.batchMode) {
                this.showImageModal(file);
            }
        });

        // 批量选择模式下的复选框事件
        if (this.batchMode) {
            const checkbox = card.querySelector('.file-checkbox');
            if (checkbox) {
                checkbox.addEventListener('change', (e) => {
                    this.toggleFileSelection(file.id, e.target.checked);
                });
            }
        }

        return card;
    }

    getImageUrl(file) {
        // 优先使用缩略图，如果没有则使用API端点
        if (file.thumbnail_small_path) {
            return `file://${file.thumbnail_small_path}`;
        }
        // 备用：使用API端点获取缩略图
        return `http://localhost:8000/api/v1/cases/${this.caseId}/files/${file.id}/thumbnail`;
    }

    getOriginalImageUrl(file) {
        // 直接使用本地文件路径（Electron支持file://协议）
        if (file.file_path) {
            return `file://${file.file_path}`;
        }
        // 备用：使用API端点
        return `http://localhost:8000/api/v1/cases/${this.caseId}/files/${file.id}/view`;
    }

    async showImageModal(file) {
        // 检查是否需要权限确认
        await this.checkFileAccessPermission();

        // 使用原始大图URL
        const originalImageUrl = this.getOriginalImageUrl(file);

        // 设置图片
        this.elements.modalImage.src = originalImageUrl;
        this.elements.modalImage.onerror = () => {
            // 如果本地文件访问失败，尝试使用API端点
            console.warn('本地文件访问失败，尝试使用API端点');
            this.elements.modalImage.src = `http://localhost:8000/api/v1/cases/${this.caseId}/files/${file.id}/view`;
        };

        // 设置标题
        const modalTitle = document.getElementById('modal-title');
        const modalSubtitle = document.getElementById('modal-subtitle');
        if (modalTitle) modalTitle.textContent = file.file_name;
        if (modalSubtitle) modalSubtitle.textContent = `${file.width || 0} × ${file.height || 0} | ${this.formatFileSize(file.file_size || 0)}`;

        // 生成详细信息（异步）
        this.elements.modalInfo.innerHTML = '<div class="loading">加载详细信息中...</div>';
        try {
            const detailedInfo = await this.generateDetailedFileInfo(file);
            this.elements.modalInfo.innerHTML = detailedInfo;
        } catch (error) {
            console.error('生成详细信息失败:', error);
            this.elements.modalInfo.innerHTML = '<div class="error">加载详细信息失败</div>';
        }

        // 生成操作按钮
        const modalActions = document.getElementById('modal-actions');
        if (modalActions) {
            modalActions.innerHTML = this.generateActionButtons(file, originalImageUrl);
        }

        this.elements.imageModal.style.display = 'block';
    }

    async generateDetailedFileInfo(file) {
        const qualityInfo = this.getDetailedQualityInfo(file);

        // 异步加载完整的文件标签信息（包括自定义标签）
        let tagsInfo = this.getTagsInfo(file);

        // 尝试获取完整的标签数据（包括自定义标签）
        try {
            const fullTagData = await api.getFileAllTags(this.caseId, file.id);
            if (fullTagData && fullTagData.custom_tags) {
                // 将自定义标签数据添加到文件对象中
                const fileWithCustomTags = {
                    ...file,
                    tags: {
                        ...file.tags,
                        custom_tags: fullTagData.custom_tags
                    }
                };
                tagsInfo = this.getTagsInfo(fileWithCustomTags);
            }
        } catch (error) {
            console.warn('获取完整标签数据失败，使用基础标签数据:', error);
        }

        return `
            <!-- 基本信息 -->
            <div class="info-section">
                <h4>基本信息</h4>
                <div class="info-item">
                    <span class="info-label">文件名</span>
                    <span class="info-value">${file.file_name}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">文件类型</span>
                    <span class="info-value">${file.file_type || '未知'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">文件大小</span>
                    <span class="info-value">${this.formatFileSize(file.file_size || 0)}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">图像尺寸</span>
                    <span class="info-value">${file.width || 0} × ${file.height || 0}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">宽高比</span>
                    <span class="info-value">${this.calculateAspectRatio(file.width, file.height)}</span>
                </div>
            </div>

            <!-- 时间信息 -->
            <div class="info-section">
                <h4>时间信息</h4>
                <div class="info-item">
                    <span class="info-label">创建时间</span>
                    <span class="info-value">${this.formatDate(file.created_at)}</span>
                </div>
                ${file.taken_at ? `
                <div class="info-item">
                    <span class="info-label">拍摄时间</span>
                    <span class="info-value">${this.formatDate(file.taken_at)}</span>
                </div>
                ` : ''}
                <div class="info-item">
                    <span class="info-label">修改时间</span>
                    <span class="info-value">${this.formatDate(file.updated_at || file.created_at)}</span>
                </div>
            </div>

            <!-- 文件路径 - 用户要求隐藏 -->
            <!--
            <div class="info-section">
                <h4>文件路径</h4>
                <div class="info-item">
                    <span class="info-label">原始路径</span>
                    <span class="info-value" title="${file.file_path || '未知'}">${this.truncatePath(file.file_path || '未知', 30)}</span>
                </div>
                ${file.thumbnail_small_path ? `
                <div class="info-item">
                    <span class="info-label">缩略图路径</span>
                    <span class="info-value" title="${file.thumbnail_small_path}">${this.truncatePath(file.thumbnail_small_path, 30)}</span>
                </div>
                ` : ''}
            </div>
            -->

            ${qualityInfo ? `
            <!-- 质量信息 -->
            <div class="info-section">
                <h4>质量分析</h4>
                ${qualityInfo}
            </div>
            ` : ''}

            ${tagsInfo ? `
            <!-- 标签信息 -->
            <div class="info-section">
                <h4>标签数据</h4>
                ${tagsInfo}
            </div>
            ` : ''}

            <!-- EXIF元数据 -->
            ${this.getExifInfo(file)}

            <!-- 技术信息 -->
            <div class="info-section">
                <h4>技术信息</h4>
                <div class="info-item">
                    <span class="info-label">文件ID</span>
                    <span class="info-value">${file.id}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">像素总数</span>
                    <span class="info-value">${this.formatPixelCount(file.width, file.height)}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">色彩深度</span>
                    <span class="info-value">24位 (RGB)</span>
                </div>
                ${file.cluster_id ? `
                <div class="info-item">
                    <span class="info-label">聚类ID</span>
                    <span class="info-value">${file.cluster_id}</span>
                </div>
                ` : ''}
            </div>
        `;
    }

    generateActionButtons(file, originalImageUrl) {
        // 移除查看大图中的操作按钮，功能已整合到批量选择模式中
        return '';
    }

    getExifInfo(file) {
        // 检查文件是否有标签数据
        if (!file.tags) {
            return '';
        }

        try {
            const tagsData = typeof file.tags === 'string' ? JSON.parse(file.tags) : file.tags;

            // 检查是否有元数据
            if (!tagsData.tags || !tagsData.tags.metadata) {
                return '';
            }

            const metadata = tagsData.tags.metadata;
            const exifItems = [];

            // 辅助函数：检查值是否有效（非null、undefined、空字符串）
            const isValidValue = (value) => {
                return value !== null && value !== undefined && value !== '' && value !== 'null' && value !== 'undefined';
            };

            // 相机信息
            if (isValidValue(metadata.camera_make) && isValidValue(metadata.camera_model)) {
                exifItems.push({
                    label: '相机型号',
                    value: `${metadata.camera_make} ${metadata.camera_model}`,
                    icon: '📷'
                });
            } else if (isValidValue(metadata.camera_make)) {
                exifItems.push({
                    label: '相机品牌',
                    value: metadata.camera_make,
                    icon: '📷'
                });
            } else if (isValidValue(metadata.camera_model)) {
                exifItems.push({
                    label: '相机型号',
                    value: metadata.camera_model,
                    icon: '📷'
                });
            }

            // 文件类型
            if (isValidValue(metadata.fileType)) {
                const fileExt = metadata.fileType.replace('image/', '').toLowerCase();
                const displayExt = fileExt === 'jpeg' ? 'JPG' : fileExt.toUpperCase();
                exifItems.push({
                    label: '图像格式',
                    value: displayExt,
                    icon: '📄'
                });
            }

            // 图像尺寸
            if (isValidValue(metadata.dimensions)) {
                exifItems.push({
                    label: '图像尺寸',
                    value: metadata.dimensions,
                    icon: '📐'
                });
            }

            // 分辨率
            if (isValidValue(metadata.resolution)) {
                let resolution = metadata.resolution.replace(' DPI', '').replace('.0', '');
                exifItems.push({
                    label: '分辨率',
                    value: `${resolution} DPI`,
                    icon: '🔍'
                });
            }

            // 色深
            if (isValidValue(metadata.color_depth)) {
                let colorDepth = metadata.color_depth;
                if (colorDepth.includes('浣�') || colorDepth.includes('位')) {
                    colorDepth = colorDepth.replace('浣�', '').replace('位', '').trim() + '-bit';
                }
                exifItems.push({
                    label: '色彩深度',
                    value: colorDepth,
                    icon: '🎨'
                });
            }

            // 添加更多EXIF字段
            // 镜头信息
            if (isValidValue(metadata.lens_make) && isValidValue(metadata.lens_model)) {
                exifItems.push({
                    label: '镜头',
                    value: `${metadata.lens_make} ${metadata.lens_model}`,
                    icon: '🔭'
                });
            } else if (isValidValue(metadata.lens_model)) {
                exifItems.push({
                    label: '镜头型号',
                    value: metadata.lens_model,
                    icon: '🔭'
                });
            }

            // 文件大小
            if (isValidValue(metadata.file_size)) {
                exifItems.push({
                    label: '文件大小',
                    value: metadata.file_size,
                    icon: '💾'
                });
            }

            // GPS信息
            if (isValidValue(metadata.gps_latitude) && isValidValue(metadata.gps_longitude)) {
                exifItems.push({
                    label: 'GPS坐标',
                    value: `${metadata.gps_latitude}, ${metadata.gps_longitude}`,
                    icon: '📍'
                });
            }

            // 扩展EXIF数据（如果存在）
            if (isValidValue(metadata.aperture)) {
                exifItems.push({
                    label: '光圈',
                    value: metadata.aperture,
                    icon: '🔆'
                });
            }

            if (isValidValue(metadata.shutter_speed)) {
                exifItems.push({
                    label: '快门速度',
                    value: metadata.shutter_speed,
                    icon: '⏱️'
                });
            }

            if (isValidValue(metadata.iso)) {
                exifItems.push({
                    label: 'ISO感光度',
                    value: metadata.iso,
                    icon: '🌟'
                });
            }

            if (isValidValue(metadata.focal_length)) {
                exifItems.push({
                    label: '焦距',
                    value: metadata.focal_length,
                    icon: '📏'
                });
            }

            if (isValidValue(metadata.date_time_original)) {
                try {
                    const date = new Date(metadata.date_time_original);
                    if (!isNaN(date.getTime())) {
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        const hours = String(date.getHours()).padStart(2, '0');
                        const minutes = String(date.getMinutes()).padStart(2, '0');
                        exifItems.push({
                            label: '拍摄时间',
                            value: `${year}/${month}/${day} ${hours}:${minutes}`,
                            icon: '📅'
                        });
                    }
                } catch (e) {
                    console.warn('日期格式化失败:', metadata.date_time_original);
                }
            }

            if (isValidValue(metadata.color_space)) {
                exifItems.push({
                    label: '色彩空间',
                    value: metadata.color_space,
                    icon: '🌈'
                });
            }

            if (isValidValue(metadata.flash)) {
                exifItems.push({
                    label: '闪光灯',
                    value: metadata.flash,
                    icon: '⚡'
                });
            }

            if (isValidValue(metadata.white_balance)) {
                exifItems.push({
                    label: '白平衡',
                    value: metadata.white_balance,
                    icon: '⚪'
                });
            }

            // 添加更多详细EXIF信息
            if (isValidValue(metadata.exposure_mode)) {
                exifItems.push({
                    label: '曝光模式',
                    value: metadata.exposure_mode,
                    icon: '📸'
                });
            }

            if (isValidValue(metadata.metering_mode)) {
                exifItems.push({
                    label: '测光模式',
                    value: metadata.metering_mode,
                    icon: '📊'
                });
            }

            if (isValidValue(metadata.scene_type)) {
                exifItems.push({
                    label: '场景类型',
                    value: metadata.scene_type,
                    icon: '🎬'
                });
            }

            if (isValidValue(metadata.software)) {
                exifItems.push({
                    label: '处理软件',
                    value: metadata.software,
                    icon: '💻'
                });
            }

            // 如果没有EXIF数据，返回空
            if (exifItems.length === 0) {
                return '';
            }

            // 生成HTML
            const exifHtml = exifItems.map(item => `
                <div class="info-item">
                    <span class="info-label">${item.icon} ${item.label}</span>
                    <span class="info-value">${item.value}</span>
                </div>
            `).join('');

            return `
                <div class="info-section">
                    <h4>📊 EXIF元数据</h4>
                    ${exifHtml}
                </div>
            `;

        } catch (error) {
            console.error('解析EXIF数据失败:', error);
            return '';
        }
    }

    // 辅助函数
    formatFileSize(bytes) {
        if (!bytes || bytes === 0) return '未知';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    calculateAspectRatio(width, height) {
        if (!width || !height) return '未知';
        const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);
        const divisor = gcd(width, height);
        return `${width / divisor}:${height / divisor}`;
    }

    formatPixelCount(width, height) {
        if (!width || !height) return '未知';
        const pixels = width * height;
        if (pixels >= 1000000) {
            return `${(pixels / 1000000).toFixed(1)}MP`;
        }
        return `${pixels.toLocaleString()} 像素`;
    }

    truncatePath(path, maxLength) {
        if (!path || path.length <= maxLength) return path;
        return '...' + path.slice(-(maxLength - 3));
    }

    getTagsInfo(file) {
        if (!file.tags || !file.tags.tags) return null;

        const tags = file.tags.tags;
        let html = '';

        // 辅助函数：检查值是否有效（非null、undefined、空字符串）
        const isValidValue = (value) => {
            return value !== null && value !== undefined && value !== '' && value !== 'null' && value !== 'undefined';
        };

        // 元数据标签
        if (tags.metadata && Object.keys(tags.metadata).length > 0) {
            const validMetadata = Object.entries(tags.metadata).filter(([key, value]) => isValidValue(value));
            if (validMetadata.length > 0) {
                html += '<div class="mb-3"><h5 class="text-blue-300 text-xs font-semibold mb-2">元数据</h5>';
                for (const [key, value] of validMetadata) {
                    html += `
                        <div class="info-item">
                            <span class="info-label">${key}</span>
                            <span class="info-value clickable-tag"
                                  data-tag-type="metadata"
                                  data-tag-key="${key}"
                                  data-tag-text="${value}"
                                  onclick="window.caseView.jumpToTagManagement('metadata', '${key}', '${value}', ${file.id})"
                                  title="点击跳转到标签管理页面">${value}</span>
                        </div>
                    `;
                }
                html += '</div>';
            }
        }

        // CV标签
        if (tags.cv && Object.keys(tags.cv).length > 0) {
            const validCvTags = Object.entries(tags.cv).filter(([key, value]) => isValidValue(value));
            if (validCvTags.length > 0) {
                html += '<div class="mb-3"><h5 class="text-green-300 text-xs font-semibold mb-2">计算机视觉</h5>';
                for (const [key, value] of validCvTags) {
                    // 如果值是数组，过滤掉无效值
                    if (Array.isArray(value)) {
                        const validValues = value.filter(v => isValidValue(v));
                        if (validValues.length > 0) {
                            html += `
                                <div class="info-item">
                                    <span class="info-label">${key}</span>
                                    <span class="info-value clickable-tag"
                                          data-tag-type="cv"
                                          data-tag-key="${key}"
                                          data-tag-text="${validValues.join(', ')}"
                                          onclick="window.caseView.jumpToTagManagement('cv', '${key}', '${validValues.join(', ')}', ${file.id})"
                                          title="点击跳转到标签管理页面">${validValues.join(', ')}</span>
                                </div>
                            `;
                        }
                    } else {
                        html += `
                            <div class="info-item">
                                <span class="info-label">${key}</span>
                                <span class="info-value">${value}</span>
                            </div>
                        `;
                    }
                }
                html += '</div>';
            }
        }

        // 用户标签
        if (tags.user && Array.isArray(tags.user)) {
            const validUserTags = tags.user.filter(tag => isValidValue(tag));
            if (validUserTags.length > 0) {
                html += '<div class="mb-3"><h5 class="text-purple-300 text-xs font-semibold mb-2">用户标签</h5>';
                html += `<div class="info-value clickable-tag"
                              data-tag-type="user"
                              data-tag-key="user"
                              data-tag-text="${validUserTags.join(', ')}"
                              onclick="window.caseView.jumpToTagManagement('user', 'user', '${validUserTags.join(', ')}', ${file.id})"
                              title="点击跳转到标签管理页面">${validUserTags.join(', ')}</div></div>`;
            }
        }

        // AI标签
        if (tags.ai && Array.isArray(tags.ai)) {
            const validAiTags = tags.ai.filter(tag => isValidValue(tag));
            if (validAiTags.length > 0) {
                html += '<div class="mb-3"><h5 class="text-yellow-300 text-xs font-semibold mb-2">AI标签</h5>';
                html += `<div class="info-value clickable-tag"
                              data-tag-type="ai"
                              data-tag-key="ai"
                              data-tag-text="${validAiTags.join(', ')}"
                              onclick="window.caseView.jumpToTagManagement('ai', 'ai', '${validAiTags.join(', ')}', ${file.id})"
                              title="点击跳转到标签管理页面">${validAiTags.join(', ')}</div></div>`;
            }
        }

        // 自定义标签
        if (file.tags && file.tags.custom_tags && Array.isArray(file.tags.custom_tags)) {
            const validCustomTags = file.tags.custom_tags.filter(tag => tag && tag.name);
            if (validCustomTags.length > 0) {
                html += '<div class="mb-3"><h5 class="text-pink-300 text-xs font-semibold mb-2">自定义标签</h5>';
                html += '<div class="custom-tags-container">';

                validCustomTags.forEach(tag => {
                    html += `
                        <span class="custom-tag-item clickable-tag"
                              data-tag-type="custom"
                              data-tag-id="${tag.id}"
                              data-tag-name="${tag.name}"
                              onclick="window.caseView.jumpToTagManagement('custom', '${tag.id}', '${tag.name}', ${file.id})"
                              title="点击跳转到标签管理页面"
                              style="background-color: ${tag.color}20; border-color: ${tag.color}; color: ${tag.color};">
                            ${tag.name}
                        </span>
                    `;
                });

                html += '</div></div>';
            }
        }

        return html || null;
    }

    async checkFileAccessPermission() {
        // 检查是否已经确认过权限
        const hasPermission = localStorage.getItem('fileAccessPermissionGranted');

        if (!hasPermission) {
            const confirmed = await this.showPermissionDialog();
            if (confirmed) {
                localStorage.setItem('fileAccessPermissionGranted', 'true');
            } else {
                throw new Error('用户拒绝了文件访问权限');
            }
        }
    }

    async showPermissionDialog() {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">文件访问权限确认</h3>
                        <div class="mt-2 px-7 py-3">
                            <p class="text-sm text-gray-500">
                                智慧之眼需要访问您的本地文件来显示原始图片。
                                <br><br>
                                这将允许应用程序：
                                <br>• 显示原始大图
                                <br>• 进行质量分析
                                <br>• 生成标签数据
                                <br><br>
                                您的文件将保持安全，应用程序只会读取文件内容。
                            </p>
                        </div>
                        <div class="items-center px-4 py-3">
                            <button id="confirm-permission" class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
                                允许
                            </button>
                            <button id="deny-permission" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                                拒绝
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('confirm-permission').onclick = () => {
                document.body.removeChild(modal);
                resolve(true);
            };

            document.getElementById('deny-permission').onclick = () => {
                document.body.removeChild(modal);
                resolve(false);
            };
        });
    }

    openFileInExplorer(filePath) {
        if (filePath && window.require) {
            const { shell } = window.require('electron');
            shell.showItemInFolder(filePath);
        } else {
            showNotification('无法打开文件管理器', 'warning');
        }
    }

    copyImagePath(filePath) {
        if (navigator.clipboard && filePath) {
            navigator.clipboard.writeText(filePath).then(() => {
                showNotification('文件路径已复制到剪贴板', 'success');
            }).catch(() => {
                showNotification('复制失败', 'error');
            });
        } else {
            showNotification('无法复制路径', 'warning');
        }
    }

    showImageProperties(fileId) {
        // 显示详细的图像属性对话框
        showNotification('图像属性功能开发中...', 'info');
        // TODO: 实现详细属性对话框
    }

    editFileTags(fileId) {
        // 编辑文件标签
        showNotification('标签编辑功能开发中...', 'info');
        // TODO: 实现标签编辑对话框
    }

    async setCover(fileId) {
        try {
            const response = await fetch(`http://localhost:8000/api/v1/cases/${this.caseId}/cover`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ fileId: fileId })  // 修复：使用正确的字段名 fileId
            });

            if (response.ok) {
                showNotification('封面设置成功', 'success');
                this.elements.imageModal.style.display = 'none';
                // 重新加载案例信息以更新封面
                this.loadCaseData();  // 修复：使用正确的方法名
            } else {
                const error = await response.json();
                showNotification(`设置封面失败: ${error.detail}`, 'error');
            }
        } catch (error) {
            console.error('设置封面失败:', error);
            showNotification('设置封面失败', 'error');
        }
    }

    async deleteFile(fileId) {
        const confirmed = await this.showConfirmDialog(
            '确定要删除这个文件吗？',
            '文件将被移动到回收站，可以恢复。'
        );

        if (confirmed) {
            try {
                const response = await fetch(`http://localhost:8000/api/v1/cases/${this.caseId}/files/${fileId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    showNotification('文件已删除', 'success');
                    this.elements.imageModal.style.display = 'none';
                    this.loadCaseData(); // 修复：使用正确的方法名重新加载文件列表
                } else {
                    const error = await response.json();
                    showNotification(`删除文件失败: ${error.message || error.detail || '未知错误'}`, 'error');
                }
            } catch (error) {
                console.error('删除文件失败:', error);
                showNotification('删除文件失败', 'error');
            }
        }
    }

    showConfirmDialog(title, message) {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">${title}</h3>
                        <div class="mt-2 px-7 py-3">
                            <p class="text-sm text-gray-500">${message}</p>
                        </div>
                        <div class="items-center px-4 py-3">
                            <button id="confirm-btn" class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300">
                                确定
                            </button>
                            <button id="cancel-btn" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                                取消
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('confirm-btn').onclick = () => {
                document.body.removeChild(modal);
                resolve(true);
            };

            document.getElementById('cancel-btn').onclick = () => {
                document.body.removeChild(modal);
                resolve(false);
            };
        });
    }

    filterFiles(searchTerm) {
        if (!searchTerm) {
            this.filteredFiles = [...this.files];
        } else {
            const term = searchTerm.toLowerCase();
            this.filteredFiles = this.files.filter(file =>
                file.file_name.toLowerCase().includes(term)
            );
        }
        this.renderFileGrid();
    }

    sortFiles(sortBy) {
        switch (sortBy) {
            case 'name':
                this.filteredFiles.sort((a, b) => a.file_name.localeCompare(b.file_name));
                break;
            case 'date':
                this.filteredFiles.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                break;
            case 'size':
                this.filteredFiles.sort((a, b) => (b.width * b.height) - (a.width * a.height));
                break;
        }
        this.renderFileGrid();
    }

    showBatchImportModal() {
        this.elements.batchImportModal.classList.remove('hidden');
    }

    hideBatchImportModal() {
        this.elements.batchImportModal.classList.add('hidden');
    }

    async handleBatchImport() {
        const formData = new FormData(this.elements.batchImportForm);
        const directoryPath = formData.get('directory-path');
        const recursive = formData.get('recursive') === 'on';
        const batchSize = parseInt(formData.get('batch-size'));

        if (!directoryPath) {
            alert('请输入目录路径');
            return;
        }

        try {
            this.hideBatchImportModal();

            const response = await api.post(`/cases/${this.caseId}/files/batch-import`, {
                directory_path: directoryPath,
                recursive: recursive,
                batch_size: batchSize
            });

            alert(`批量导入${response.status === 'completed' ? '完成' : '已启动'}!\n${response.message}`);

            // 重新加载数据
            this.loadCaseData();

        } catch (error) {
            console.error('批量导入失败:', error);
            alert('批量导入失败: ' + error.message);
        }
    }

    formatDate(dateString) {
        if (!dateString) return '未知';
        try {
            return new Date(dateString).toLocaleString('zh-CN');
        } catch {
            return '无效日期';
        }
    }

    formatFileSize(file) {
        // 简单的文件大小估算（基于像素数）
        if (file.width && file.height) {
            const pixels = file.width * file.height;
            if (pixels > 1000000) {
                return `${(pixels / 1000000).toFixed(1)}MP`;
            }
            return `${Math.round(pixels / 1000)}K像素`;
        }
        return '未知大小';
    }



    showError(message) {
        this.elements.errorMessage.textContent = message;
        this.elements.error.classList.remove('hidden');
        this.elements.emptyState.classList.add('hidden');
        this.elements.imageGrid.classList.add('hidden');
    }

    showEmptyState() {
        this.elements.emptyState.classList.remove('hidden');
        this.elements.imageGrid.classList.add('hidden');
    }

    hideEmptyState() {
        this.elements.emptyState.classList.add('hidden');
    }

    // FilePond 初始化
    initializeFilePond() {
        if (typeof FilePond === 'undefined') {
            console.warn('FilePond is not loaded');
            return;
        }

        // 注册插件
        FilePond.registerPlugin(FilePondPluginImagePreview, FilePondPluginImageResize);

        // 创建 FilePond 实例
        const inputElement = document.getElementById('filepond');
        this.filePond = FilePond.create(inputElement, {
            multiple: true,
            acceptedFileTypes: ['image/*'],
            maxFileSize: '20MB',
            allowReplace: false,
            allowMultiple: true,
            server: null, // 禁用自动上传
            labelIdle: '拖拽图片到这里或者 <span class="filepond--label-action">点击浏览</span>',
            labelFileProcessing: '上传中...',
            labelFileProcessingComplete: '上传完成',
            labelFileProcessingAborted: '上传取消',
            labelFileProcessingError: '上传错误',
            labelTapToCancel: '点击取消',
            labelTapToRetry: '点击重试',
            labelTapToUndo: '点击撤销',
            imagePreviewHeight: 170,
            imageResizeTargetWidth: 1920,
            imageResizeTargetHeight: 1080,
            imageResizeMode: 'contain',
            imageResizeUpscale: false
        });
    }

    // 显示文件导入模态框
    showImportModal() {
        this.elements.importModal.classList.remove('hidden');
        // 重置 FilePond
        if (this.filePond) {
            this.filePond.removeFiles();
        }
        this.hideProgress();
    }

    // 隐藏文件导入模态框
    hideImportModal() {
        this.elements.importModal.classList.add('hidden');
        if (this.filePond) {
            this.filePond.removeFiles();
        }
        this.hideProgress();
    }

    // 处理文件导入
    async handleFileImport() {
        if (!this.filePond || this.filePond.getFiles().length === 0) {
            alert('请先选择要导入的文件');
            return;
        }

        const files = this.filePond.getFiles();
        this.showProgress();

        try {
            let successCount = 0;
            let errorCount = 0;

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const progress = ((i + 1) / files.length) * 100;

                this.updateProgress(progress, `正在导入 ${i + 1}/${files.length}: ${file.filename}`);

                try {
                    // 🔧 修复：在Electron环境中，文件导入默认不复制原始文件
                    // 只记录文件路径，不占用额外存储空间
                    let filePath;

                    if (file.file && file.file.path) {
                        // Electron环境：使用文件的实际路径
                        filePath = file.file.path;
                    } else if (file.file && file.file.webkitRelativePath) {
                        // 可能的相对路径
                        filePath = file.file.webkitRelativePath;
                    } else {
                        // 如果无法获取路径，说明这是真正的Web上传场景
                        console.warn(`无法获取文件路径，使用上传模式: ${file.filename}`);
                        await api.uploadAndCopyFile(this.caseId, file.file);
                        successCount++;
                        continue;
                    }

                    console.log(`文件导入（不复制）: ${filePath}`);
                    await api.importByPath(this.caseId, filePath);
                    successCount++;

                } catch (error) {
                    console.error(`导入文件 ${file.filename} 失败:`, error);
                    errorCount++;
                }
            }

            this.hideProgress();
            this.hideImportModal();

            // 显示结果
            if (errorCount === 0) {
                alert(`成功导入 ${successCount} 个文件！`);
            } else {
                alert(`导入完成！成功 ${successCount} 个，失败 ${errorCount} 个。`);
            }

            // 重新加载数据
            this.loadCaseData();

        } catch (error) {
            console.error('文件导入失败:', error);
            this.hideProgress();
            alert('文件导入失败: ' + error.message);
        }
    }

    // 显示上传进度
    showProgress() {
        this.elements.uploadProgressDiv.classList.remove('hidden');
        this.elements.startFileImportBtn.disabled = true;
        this.elements.startFileImportBtn.textContent = '正在上传...';
    }

    // 隐藏上传进度
    hideProgress() {
        this.elements.uploadProgressDiv.classList.add('hidden');
        this.elements.startFileImportBtn.disabled = false;
        this.elements.startFileImportBtn.textContent = '开始上传';
        this.updateProgress(0, '');
    }

    // 更新进度
    updateProgress(percentage, text) {
        this.elements.progressBar.style.width = `${percentage}%`;
        this.elements.progressText.textContent = `${Math.round(percentage)}%`;
        if (text) {
            this.elements.progressText.textContent += ` - ${text}`;
        }
    }

    // 开始质量分析
    async startQualityAnalysis() {
        if (!this.currentCase || !this.currentCase.id) {
            this.showNotification('无法获取案例信息', 'error');
            return;
        }

        const imageFiles = this.files.filter(file =>
            file.file_type && file.file_type.startsWith('image/')
        );

        if (imageFiles.length === 0) {
            this.showNotification('当前案例中没有图片文件，无法进行质量分析', 'warning');
            return;
        }

        try {
            this.showNotification('正在启动图像质量分析，请稍候...', 'info');

            const response = await fetch(`http://localhost:8000/api/v1/quality/${this.currentCase.id}/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    analyze_similarity: true,
                    export_excel: true,
                    min_cluster_size: 2
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.showNotification(
                    `质量分析已启动！分析${imageFiles.length}个图片文件，任务ID: ${result.task_id}`,
                    'success'
                );

                // 检查是否有封面更新
                if (result.coverUpdated && result.newCoverUrl) {
                    this.showNotification('质量分析完成后自动更新了封面', 'info');
                }

                // 启动轮询检查分析结果
                this.pollAnalysisResult(result.task_id);
            } else {
                throw new Error(result.message || '质量分析启动失败');
            }

        } catch (error) {
            console.error('质量分析失败:', error);
            this.showNotification(`质量分析失败: ${error.message}`, 'error');
        }
    }

    // 轮询分析结果
    async pollAnalysisResult(taskId) {
        const maxPolls = 20; // 最多轮询20次
        let pollCount = 0;

        const poll = async () => {
            pollCount++;
            console.log(`轮询分析结果 - 第 ${pollCount} 次检查`);

            try {
                // 重新加载案例数据以获取最新的分析结果
                await this.loadCaseData();

                // 检查是否有已分析的图片
                const imageFiles = this.files.filter(file =>
                    file.file_type && file.file_type.startsWith('image/')
                );

                const analyzedFiles = imageFiles.filter(file =>
                    file.quality_score !== null && file.quality_score !== undefined
                );

                console.log(`图片文件总数: ${imageFiles.length}, 已分析: ${analyzedFiles.length}`);

                // 打印一些调试信息
                if (analyzedFiles.length > 0) {
                    console.log('已分析的文件示例:', analyzedFiles.slice(0, 3).map(f => ({
                        id: f.id,
                        name: f.file_name,
                        quality_score: f.quality_score
                    })));
                }

                if (analyzedFiles.length > 0) {
                    this.showNotification(
                        `质量分析完成！已分析 ${analyzedFiles.length} 张图片`,
                        'success'
                    );

                    // 质量分析完成后，检查封面是否需要更新
                    this.checkCoverAfterAnalysis();

                    return;
                }

                if (pollCount < maxPolls) {
                    // 3秒后再次检查
                    setTimeout(poll, 3000);
                } else {
                    this.showNotification('分析时间较长，请手动刷新页面查看结果', 'info');
                }

            } catch (error) {
                console.error('轮询分析结果失败:', error);
                if (pollCount < maxPolls) {
                    setTimeout(poll, 3000);
                } else {
                    this.showNotification('轮询分析结果失败，请手动刷新页面', 'error');
                }
            }
        };

        // 2秒后开始第一次检查（缩短等待时间）
        setTimeout(poll, 2000);
    }

    // 质量分析完成后检查封面更新
    async checkCoverAfterAnalysis() {
        if (!this.currentCase) return;

        const coverStatus = api.getCoverStatus(this.currentCase);

        // 如果当前是占位图或者需要关注，尝试自动重选封面
        if (coverStatus && (coverStatus.isPlaceholder || coverStatus.needsAttention)) {
            try {
                const result = await api.reselectCover(this.currentCase.id);

                if (result.coverReselected && result.reselectionStatus === 'SUCCESS') {
                    // 重新加载案例数据以获取最新封面
                    await this.loadCaseData();
                    this.showNotification('质量分析完成后自动选择了新封面', 'success');
                }
            } catch (error) {
                console.error('自动重选封面失败:', error);
                // 不显示错误，因为这是后台操作
            }
        }
    }

    // 显示确认对话框
    showConfirmDialog(message, title = '确认操作') {
        return new Promise((resolve) => {
            const result = confirm(`${title}\n\n${message}`);
            resolve(result);
        });
    }

    // 显示详细错误信息
    showDetailedError(error, context = '') {
        let errorMessage = error.message || '未知错误';

        // 根据错误类型提供更友好的提示
        if (error.message && error.message.includes('FILE_NOT_FOUND')) {
            errorMessage = '文件不存在或已被删除，请刷新页面后重试';
        } else if (error.message && error.message.includes('INVALID_FILE_TYPE')) {
            errorMessage = '只能选择图片文件作为封面';
        } else if (error.message && error.message.includes('Network Error')) {
            errorMessage = '网络连接失败，请检查服务器状态';
        } else if (error.message && error.message.includes('404')) {
            errorMessage = '请求的资源不存在，请刷新页面后重试';
        } else if (error.message && error.message.includes('500')) {
            errorMessage = '服务器内部错误，请稍后重试';
        }

        const fullMessage = context ? `${context}: ${errorMessage}` : errorMessage;
        this.showNotification(fullMessage, 'error');

        // 在控制台输出详细错误信息用于调试
        console.error(`${context} 详细错误:`, error);
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification bg-white border-l-4 p-4 mb-2 rounded-r shadow-lg ${
            type === 'success' ? 'border-green-500 text-green-700' :
            type === 'error' ? 'border-red-500 text-red-700' :
            type === 'warning' ? 'border-yellow-500 text-yellow-700' :
            'border-blue-500 text-blue-700'
        }`;

        notification.innerHTML = `
            <div class="flex justify-between items-center">
                <span>${message}</span>
                <button class="ml-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                    ✕
                </button>
            </div>
        `;

        const container = document.getElementById('notifications');
        if (container) {
            container.appendChild(notification);

            // 5秒后自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    // 获取质量分析标签
    getQualityBadge(file) {
        if (file.quality_score === null) {
            return '';
        }

        const score = file.quality_score;
        let badgeClass = '';
        let badgeText = '';

        if (score >= 80) {
            badgeClass = 'bg-green-500';
            badgeText = '优';
        } else if (score >= 60) {
            badgeClass = 'bg-yellow-500';
            badgeText = '良';
        } else {
            badgeClass = 'bg-red-500';
            badgeText = '差';
        }

        return `<span class="absolute top-2 right-2 ${badgeClass} text-white text-xs px-2 py-1 rounded">${badgeText}</span>`;
    }

    // 获取封面标识
    getCoverBadge(file) {
        if (!this.currentCase || !file.isCurrentCoverSource) {
            return '';
        }

        return `<span class="absolute bottom-2 right-2 bg-purple-500 text-white text-xs px-2 py-1 rounded flex items-center">
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"/>
            </svg>
            封面
        </span>`;
    }

    // 获取质量分析简要信息
    getQualityInfo(file) {
        if (file.quality_score === null) {
            return '<div class="text-xs text-gray-400">未分析</div>';
        }

        const score = Math.round(file.quality_score);
        const cluster = file.cluster_id ? `群组${file.cluster_id}` : '独立';

        return `
            <div class="text-xs text-blue-600">
                质量: ${score}分 | ${cluster}
            </div>
        `;
    }

    // 获取详细质量分析信息（用于模态框）
    getDetailedQualityInfo(file) {
        if (file.quality_score === null) {
            return `
                <div class="mt-3 pt-3 border-t border-gray-300">
                    <h4 class="font-medium text-gray-600 mb-2">质量分析</h4>
                    <div class="text-gray-500">此图片尚未进行质量分析</div>
                </div>
            `;
        }

        const formatScore = (value) => value !== null ? Math.round(value * 100) / 100 : 'N/A';

        return `
            <div class="mt-3 pt-3 border-t border-gray-300">
                <h4 class="font-medium text-gray-600 mb-2">质量分析结果</h4>
                <div class="grid grid-cols-2 gap-2 text-xs">
                    <div><strong>综合质量:</strong> ${formatScore(file.quality_score)}分</div>
                    <div><strong>清晰度:</strong> ${formatScore(file.sharpness)}</div>
                    <div><strong>亮度:</strong> ${formatScore(file.brightness)}</div>
                    <div><strong>动态范围:</strong> ${formatScore(file.dynamic_range)}</div>
                    <div><strong>人脸数量:</strong> ${file.num_faces || 0}个</div>
                    <div><strong>人脸清晰度:</strong> ${formatScore(file.face_sharpness)}</div>
                </div>
                ${file.cluster_id ? `
                    <div class="mt-2 pt-2 border-t border-gray-200">
                        <div class="text-xs"><strong>相似性群组:</strong> 群组 ${file.cluster_id}</div>
                        ${file.phash ? `<div class="text-xs text-gray-400"><strong>感知哈希:</strong> ${file.phash.substring(0, 16)}...</div>` : ''}
                    </div>
                ` : '<div class="mt-2 text-xs text-gray-500">独立图片（无相似图片）</div>'}
                         </div>
         `;
     }

     // 批量选择相关方法

     // 切换批量选择模式
     toggleBatchMode() {
         this.batchMode = !this.batchMode;

         if (this.batchMode) {
             this.elements.batchSelectBtn.textContent = '退出选择';
             this.elements.batchActions.classList.remove('hidden');
             this.selectedFiles.clear();
         } else {
             this.exitBatchMode();
         }

         // 重新渲染图片网格以显示/隐藏选择框
         this.renderFileGrid();
         this.updateSelectedCount();
     }

     // 退出批量选择模式
     exitBatchMode() {
         this.batchMode = false;
         this.elements.batchSelectBtn.textContent = '选择图片';
         this.elements.batchActions.classList.add('hidden');
         this.selectedFiles.clear();

         // 重新渲染图片网格
         this.renderFileGrid();
     }

     // 切换单个文件的选择状态
     toggleFileSelection(fileId, selected) {
         if (selected) {
             this.selectedFiles.add(fileId);
         } else {
             this.selectedFiles.delete(fileId);
         }
         this.updateSelectedCount();
     }

     // 全选图片
     selectAllImages() {
         const imageFiles = this.filteredFiles.filter(file =>
             file.file_type && file.file_type.startsWith('image/')
         );

         imageFiles.forEach(file => {
             this.selectedFiles.add(file.id);
         });

         this.renderFileGrid();
         this.updateSelectedCount();
     }

     // 取消全选
     deselectAllImages() {
         this.selectedFiles.clear();
         this.renderFileGrid();
         this.updateSelectedCount();
     }

     // 更新选中文件计数
     updateSelectedCount() {
         if (this.elements.selectedCount) {
             this.elements.selectedCount.textContent = this.selectedFiles.size;
         }
         // 更新封面按钮状态
         this.updateCoverButtonState();
     }

     // 更新批量选择模式下的按钮状态
     updateCoverButtonState() {
         if (!this.batchMode) return;

         const selectedCount = this.selectedFiles.size;

         // 设置封面按钮：只有选择了一个图片文件时才启用
         if (this.elements.setCoverBtn) {
             if (selectedCount === 1) {
                 const selectedFile = Array.from(this.selectedFiles)[0];
                 const file = this.files.find(f => f.id === selectedFile);

                 if (file && api.canSetAsCover(file)) {
                     this.elements.setCoverBtn.disabled = false;
                 } else {
                     this.elements.setCoverBtn.disabled = true;
                 }
             } else {
                 this.elements.setCoverBtn.disabled = true;
             }
         }

         // 批量删除按钮：选择了一个或多个文件时启用
         if (this.elements.batchDeleteBtn) {
             if (selectedCount > 0) {
                 this.elements.batchDeleteBtn.disabled = false;
             } else {
                 this.elements.batchDeleteBtn.disabled = true;
             }
         }
     }

     // 开始批量质量分析
     async startBatchQualityAnalysis() {
         if (this.selectedFiles.size === 0) {
             this.showNotification('请先选择要分析的图片', 'warning');
             return;
         }

         if (!this.currentCase || !this.currentCase.id) {
             this.showNotification('无法获取案例信息', 'error');
             return;
         }

         const selectedImageIds = Array.from(this.selectedFiles);

         // 禁用按钮，防止重复点击
         const btn = this.elements.batchQualityAnalysisBtn;
         const originalText = btn.textContent;
         btn.disabled = true;
         btn.textContent = '分析中...';

         try {
             this.showNotification(`正在启动对 ${selectedImageIds.length} 张图片的质量分析...`, 'info');

             const response = await fetch(`http://localhost:8000/api/v1/quality/${this.currentCase.id}/analyze`, {
                 method: 'POST',
                 headers: {
                     'Content-Type': 'application/json',
                     'Accept': 'application/json'
                 },
                 body: JSON.stringify({
                     file_ids: selectedImageIds,  // 只分析选中的文件
                     analyze_similarity: true,
                     export_excel: false,
                     min_cluster_size: 2
                 })
             });

             if (!response.ok) {
                 throw new Error(`HTTP ${response.status}: ${response.statusText}`);
             }

             const result = await response.json();

             if (result.success) {
                 this.showNotification(
                     `批量质量分析已启动！正在分析 ${selectedImageIds.length} 张图片`,
                     'success'
                 );

                 // 启动轮询检查分析结果
                 this.pollAnalysisResult(result.task_id);

                 // 退出批量模式
                 this.exitBatchMode();
             } else {
                 throw new Error(result.message || '批量质量分析启动失败');
             }

         } catch (error) {
             console.error('批量质量分析失败:', error);
             this.showNotification(`批量质量分析失败: ${error.message}`, 'error');
         } finally {
             // 恢复按钮状态
             btn.disabled = false;
             btn.textContent = originalText;
         }
    }

    // ===== 封面管理功能 =====

    toggleCoverMenu() {
        if (this.elements.coverMenu) {
            this.elements.coverMenu.classList.toggle('hidden');
        }
    }

    hideCoverMenu() {
        if (this.elements.coverMenu) {
            this.elements.coverMenu.classList.add('hidden');
        }
    }

    // 封面管理功能已整合到批量选择模式中，移除了独立的封面管理方法

    async setCoverFromSelected() {
        if (this.selectedFiles.size !== 1) {
            this.showNotification('请选择一个图片文件作为封面', 'warning');
            return;
        }

        const selectedFileId = Array.from(this.selectedFiles)[0];
        const file = this.files.find(f => f.id === selectedFileId);

        if (!file || !api.canSetAsCover(file)) {
            this.showNotification('只能选择图片文件作为封面', 'warning');
            return;
        }

        try {
            this.showNotification('正在设置封面...', 'info');

            const updatedCase = await api.setCover(this.currentCase.id, selectedFileId);

            // 更新当前案例数据
            this.currentCase = updatedCase;
            this.updateCaseInfo();

            // 重新渲染文件网格以更新封面源标识
            this.renderFileGrid();

            this.showNotification('封面设置成功', 'success');

            // 退出批量选择模式
            this.exitBatchMode();

        } catch (error) {
            this.showDetailedError(error, '设置封面失败');
        }
    }

    // 批量删除文件
    async batchDeleteFiles() {
        if (this.selectedFiles.size === 0) {
            this.showNotification('请先选择要删除的图片', 'warning');
            return;
        }

        const selectedCount = this.selectedFiles.size;
        const confirmed = await this.showConfirmDialog(
            `确定要删除选中的 ${selectedCount} 张图片吗？`,
            '文件将被移动到回收站，可以恢复。'
        );

        if (!confirmed) return;

        const selectedFileIds = Array.from(this.selectedFiles);
        let successCount = 0;
        let failCount = 0;

        // 禁用删除按钮，防止重复点击
        const btn = this.elements.batchDeleteBtn;
        const originalText = btn.textContent;
        btn.disabled = true;
        btn.textContent = '删除中...';

        try {
            this.showNotification(`正在删除 ${selectedCount} 张图片...`, 'info');

            // 逐个删除文件
            for (const fileId of selectedFileIds) {
                try {
                    const response = await fetch(`http://localhost:8000/api/v1/cases/${this.currentCase.id}/files/${fileId}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        successCount++;
                    } else {
                        failCount++;
                        console.error(`删除文件 ${fileId} 失败:`, await response.text());
                    }
                } catch (error) {
                    failCount++;
                    console.error(`删除文件 ${fileId} 失败:`, error);
                }
            }

            // 显示结果通知
            if (successCount > 0 && failCount === 0) {
                this.showNotification(`成功删除 ${successCount} 张图片`, 'success');
            } else if (successCount > 0 && failCount > 0) {
                this.showNotification(`成功删除 ${successCount} 张图片，${failCount} 张失败`, 'warning');
            } else {
                this.showNotification(`删除失败，${failCount} 张图片删除失败`, 'error');
            }

            // 退出批量选择模式并重新加载数据
            this.exitBatchMode();
            await this.loadCaseData();

        } catch (error) {
            console.error('批量删除失败:', error);
            this.showNotification('批量删除失败', 'error');
        } finally {
            // 恢复按钮状态
            btn.disabled = false;
            btn.textContent = originalText;
        }
    }

    // ==================== 标签系统相关方法 ====================



    // 打开标签管理页面
    openTagManagement(sourceFileId = null) {
        let url = `tag-management.html?caseId=${this.caseId}`;

        // 如果提供了源文件ID，添加到URL参数中
        if (sourceFileId) {
            url += `&sourceFileId=${sourceFileId}`;
            console.log('🎯 跳转到标签管理页面，源文件ID:', sourceFileId);
        }

        window.location.href = url;
    }

    // 跳转到标签管理页面并高亮特定标签
    jumpToTagManagement(tagType, tagKey, tagText, sourceFileId) {
        console.log('🔗 标签点击跳转:', { tagType, tagKey, tagText, sourceFileId });

        // 构建URL，包含案例ID、源文件ID和标签信息
        let url = `tag-management.html?caseId=${this.caseId}&sourceFileId=${sourceFileId}`;
        url += `&tagType=${encodeURIComponent(tagType)}`;

        // 根据标签类型传递正确的参数
        if (tagType === 'custom') {
            // 自定义标签：tagKey是标签ID，tagText是标签名称
            url += `&tagId=${encodeURIComponent(tagKey)}`;
            url += `&tagText=${encodeURIComponent(tagText)}`;
            console.log('🏷️ 自定义标签跳转，标签ID:', tagKey);
        } else {
            // 系统标签：需要传递原始的键名和值
            url += `&tagKey=${encodeURIComponent(tagKey)}`;
            url += `&tagText=${encodeURIComponent(tagText)}`;
        }

        console.log('🎯 跳转URL:', url);
        window.location.href = url;
    }

    // ==================== 回收站相关方法 ====================

    // 打开回收站模态框
    async openTrashModal() {
        const modal = document.getElementById('trash-modal');
        if (modal) {
            modal.classList.remove('hidden');
            await this.loadTrashFiles();
        }
    }

    // 隐藏回收站模态框
    hideTrashModal() {
        const modal = document.getElementById('trash-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    // 加载回收站文件
    async loadTrashFiles() {
        const loadingEl = document.getElementById('trash-loading');
        const emptyEl = document.getElementById('trash-empty');
        const gridEl = document.getElementById('trash-files-grid');
        const countEl = document.getElementById('trash-count');
        const restoreAllBtn = document.getElementById('restore-all-btn');
        const emptyTrashBtn = document.getElementById('empty-trash-btn');

        // 显示加载状态
        loadingEl.classList.remove('hidden');
        emptyEl.classList.add('hidden');
        gridEl.classList.add('hidden');

        try {
            const response = await api.getTrashFiles(this.caseId);
            const trashFiles = response.files || [];

            // 更新计数
            countEl.textContent = trashFiles.length;

            if (trashFiles.length === 0) {
                // 显示空状态
                loadingEl.classList.add('hidden');
                emptyEl.classList.remove('hidden');
                restoreAllBtn.disabled = true;
                emptyTrashBtn.disabled = true;
            } else {
                // 显示文件网格
                loadingEl.classList.add('hidden');
                gridEl.classList.remove('hidden');
                restoreAllBtn.disabled = false;
                emptyTrashBtn.disabled = false;

                // 渲染文件网格
                this.renderTrashFiles(trashFiles);
            }
        } catch (error) {
            console.error('加载回收站文件失败:', error);
            loadingEl.classList.add('hidden');
            emptyEl.classList.remove('hidden');
            this.showNotification('加载回收站文件失败', 'error');
        }
    }

    // 渲染回收站文件
    renderTrashFiles(trashFiles) {
        const gridEl = document.getElementById('trash-files-grid');
        gridEl.innerHTML = '';

        trashFiles.forEach(file => {
            const fileEl = this.createTrashFileElement(file);
            gridEl.appendChild(fileEl);
        });
    }

    // 创建回收站文件元素
    createTrashFileElement(file) {
        const fileEl = document.createElement('div');
        fileEl.className = 'relative bg-white border border-gray-200 rounded-lg p-2 hover:shadow-md transition-shadow';
        fileEl.dataset.fileId = file.file_id;

        const thumbnailUrl = file.thumbnail_small_path ?
            `file://${file.thumbnail_small_path}` :
            'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNEgyMFYyMEg0VjRaIiBzdHJva2U9IiM5Q0E3QUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K';

        fileEl.innerHTML = `
            <div class="aspect-square mb-2 relative">
                <img src="${thumbnailUrl}" alt="${file.file_name}"
                     class="w-full h-full object-cover rounded"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNEgyMFYyMEg0VjRaIiBzdHJva2U9IiM5Q0E3QUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K'">
                <div class="absolute top-1 right-1 bg-red-500 text-white text-xs px-1 rounded">
                    已删除
                </div>
            </div>
            <div class="text-xs text-gray-600 truncate mb-2" title="${file.file_name}">
                ${file.file_name}
            </div>
            <div class="text-xs text-gray-400 mb-2">
                删除时间: ${new Date(file.deleted_at).toLocaleString()}
            </div>
            <div class="flex space-x-1">
                <button class="restore-file-btn flex-1 bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded"
                        data-file-id="${file.file_id}">
                    恢复
                </button>
                <button class="delete-permanently-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded"
                        data-file-id="${file.file_id}">
                    永久删除
                </button>
            </div>
        `;

        // 添加事件监听器
        const restoreBtn = fileEl.querySelector('.restore-file-btn');
        const deleteBtn = fileEl.querySelector('.delete-permanently-btn');

        restoreBtn.addEventListener('click', () => {
            this.restoreFile(file.file_id);
        });

        deleteBtn.addEventListener('click', () => {
            this.deletePermanently(file.file_id);
        });

        return fileEl;
    }

    // 恢复单个文件
    async restoreFile(fileId) {
        try {
            await api.restoreFile(this.caseId, fileId);
            this.showNotification('文件恢复成功', 'success');
            await this.loadTrashFiles(); // 重新加载回收站
            await this.loadCaseData(); // 重新加载案例数据
        } catch (error) {
            console.error('恢复文件失败:', error);
            this.showNotification('恢复文件失败', 'error');
        }
    }

    // 永久删除单个文件
    async deletePermanently(fileId) {
        if (!confirm('确定要永久删除这个文件吗？此操作无法撤销！')) {
            return;
        }

        try {
            await api.deletePermanently(this.caseId, fileId);
            this.showNotification('文件已永久删除', 'success');
            await this.loadTrashFiles(); // 重新加载回收站
        } catch (error) {
            console.error('永久删除文件失败:', error);
            this.showNotification('永久删除文件失败', 'error');
        }
    }

    // 恢复全部文件
    async restoreAllFiles() {
        if (!confirm('确定要恢复回收站中的所有文件吗？')) {
            return;
        }

        // 检查案例ID是否有效
        if (!this.caseId) {
            console.error('案例ID无效，无法恢复文件');
            this.showNotification('案例ID无效，无法恢复文件', 'error');
            return;
        }

        try {
            await api.restoreAllFiles(this.caseId);
            this.showNotification('所有文件恢复成功', 'success');
            await this.loadTrashFiles(); // 重新加载回收站
            await this.loadCaseData(); // 重新加载案例数据
        } catch (error) {
            console.error('恢复所有文件失败:', error);
            this.showNotification(`恢复所有文件失败: ${error.message}`, 'error');
        }
    }

    // 清空回收站
    async emptyTrash() {
        if (!confirm('确定要清空回收站吗？所有文件将被永久删除，此操作无法撤销！')) {
            return;
        }

        // 检查案例ID是否有效
        if (!this.caseId) {
            console.error('案例ID无效，无法清空回收站');
            this.showNotification('案例ID无效，无法清空回收站', 'error');
            return;
        }

        try {
            await api.emptyCaseTrash(this.caseId);
            this.showNotification('回收站已清空', 'success');
            await this.loadTrashFiles(); // 重新加载回收站
        } catch (error) {
            console.error('清空回收站失败:', error);
            this.showNotification(`清空回收站失败: ${error.message}`, 'error');
        }
    }

}

// 等待所有依赖加载完成后初始化
function initializeWhenReady() {
    if (typeof api !== 'undefined' && typeof FilePond !== 'undefined') {
        window.caseView = new CaseViewer();
    } else {
        // 如果依赖还没有加载完成，等待一下再尝试
        setTimeout(initializeWhenReady, 100);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    initializeWhenReady();
});