# PostgreSQL升级项目依赖包
# 用于开发工具和测试脚本

# 数据库连接
psycopg2-binary>=2.9.0

# 环境变量管理
python-dotenv>=0.19.0

# 命令行工具
click>=8.0.0

# 终端美化输出
rich>=12.0.0

# 表格格式化
tabulate>=0.9.0

# 数据处理
pandas>=1.5.0

# 统计分析
numpy>=1.21.0

# 日期时间处理
python-dateutil>=2.8.0

# JSON处理增强
ujson>=5.0.0

# 进度条
tqdm>=4.64.0

# 配置文件处理
pyyaml>=6.0

# HTTP请求 (如果需要)
requests>=2.28.0

# 测试框架
pytest>=7.0.0
pytest-asyncio>=0.21.0

# 代码质量
flake8>=5.0.0
black>=22.0.0

# 类型检查
mypy>=0.991
